{"version": 3, "sources": ["../../../src/server/typescript/index.ts"], "names": ["createTSPlugin", "typescript", "ts", "create", "info", "init", "proxy", "Object", "k", "keys", "languageService", "x", "args", "apply", "getCompletionsAtPosition", "fileName", "position", "options", "prior", "isGlobalCompletion", "isMemberCompletion", "isNewIdentifierLocation", "entries", "isAppEntryFile", "entryInfo", "getEntryInfo", "client", "serverLayer", "filterCompletionsAtPosition", "metadata", "entryConfig", "addCompletionsAtPosition", "source", "getSource", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "isPositionInsideNode", "isDefaultFunctionExport", "push", "<PERSON><PERSON><PERSON><PERSON>", "getCompletionEntryDetails", "entryName", "formatOptions", "preferences", "data", "entryCompletionEntryDetails", "metadataCompletionEntryDetails", "getQuickInfoAtPosition", "definitions", "getDefinitionAtPosition", "hasDisallowedReactAPIDefinition", "metadataInfo", "overridden", "getSemanticDiagnostics", "isClientEntry", "isServerEntry", "isAppEntry", "server", "e", "file", "category", "DiagnosticCategory", "Error", "code", "NEXT_TS_ERRORS", "MISPLACED_ENTRY_DIRECTIVE", "isInsideApp", "errorDiagnostic", "errorEntry", "isImportDeclaration", "diagnostics", "getSemanticDiagnosticsForImportDeclaration", "isVariableStatement", "modifiers", "some", "m", "kind", "SyntaxKind", "ExportKeyword", "getSemanticDiagnosticsForExportVariableStatement", "metadataDiagnostics", "getSemanticDiagnosticsForExportVariableStatementInClientEntry", "clientBoundary", "serverBoundary", "getSemanticDiagnosticsForFunctionExport", "isFunctionDeclaration", "isExportDeclaration", "getSemanticDiagnosticsForExportDeclarationInClientEntry", "getSemanticDiagnosticsForExportDeclaration", "getDefinitionAndBoundSpan", "metadataDefinition"], "mappings": "AAAA;;;;;;;;CAQC;;;;+BAsBYA;;;eAAAA;;;uBAZN;0BACwB;+DAEP;+DACA;8DACC;uEACE;uEACA;iEACN;8DACE;;;;;;AAGhB,MAAMA,iBAAsD,CAAC,EAClEC,YAAYC,EAAE,EACf;IACC,SAASC,OAAOC,IAAsC;QACpDC,IAAAA,WAAI,EAAC;YACHH;YACAE;QACF;QAEA,0BAA0B;QAC1B,MAAME,QAAQC,OAAOJ,MAAM,CAAC;QAC5B,KAAK,IAAIK,KAAKD,OAAOE,IAAI,CAACL,KAAKM,eAAe,EAAG;YAC/C,MAAMC,IAAI,AAACP,KAAKM,eAAe,AAAQ,CAACF,EAAE;YAC1CF,KAAK,CAACE,EAAE,GAAG,CAAC,GAAGI,OAAoBD,EAAEE,KAAK,CAACT,KAAKM,eAAe,EAAEE;QACnE;QAEA,kBAAkB;QAClBN,MAAMQ,wBAAwB,GAAG,CAC/BC,UACAC,UACAC;YAEA,IAAIC,QAAQd,KAAKM,eAAe,CAACI,wBAAwB,CACvDC,UACAC,UACAC,YACG;gBACHE,oBAAoB;gBACpBC,oBAAoB;gBACpBC,yBAAyB;gBACzBC,SAAS,EAAE;YACb;YACA,IAAI,CAACC,IAAAA,qBAAc,EAACR,WAAW,OAAOG;YAEtC,0BAA0B;YAC1B,MAAMM,YAAYC,IAAAA,mBAAY,EAACV;YAC/B,IAAI,CAACS,UAAUE,MAAM,EAAE;gBACrB,gDAAgD;gBAChDR,MAAMI,OAAO,GAAGK,eAAW,CAACC,2BAA2B,CAACV,MAAMI,OAAO;gBAErE,6CAA6C;gBAC7CJ,QAAQW,iBAAQ,CAACD,2BAA2B,CAC1Cb,UACAC,UACAC,SACAC;YAEJ;YAEA,2CAA2C;YAC3CY,eAAW,CAACC,wBAAwB,CAAChB,UAAUC,UAAUE;YAEzD,MAAMc,SAASC,IAAAA,gBAAS,EAAClB;YACzB,IAAI,CAACiB,QAAQ,OAAOd;YAEpBhB,GAAGgC,YAAY,CAACF,QAAS,CAACG;gBACxB,uDAAuD;gBACvD,IACEC,IAAAA,2BAAoB,EAACpB,UAAUmB,SAC/BE,IAAAA,8BAAuB,EAACF,OACxB;oBACAjB,MAAMI,OAAO,CAACgB,IAAI,IACbC,cAAY,CAACzB,wBAAwB,CACtCC,UACAoB,MACAnB;gBAGN;YACF;YAEA,OAAOE;QACT;QAEA,+BAA+B;QAC/BZ,MAAMkC,yBAAyB,GAAG,CAChCzB,UACAC,UACAyB,WACAC,eACAV,QACAW,aACAC;YAEA,MAAMC,8BAA8Bf,eAAW,CAACU,yBAAyB,CACvEC,WACAG;YAEF,IAAIC,6BAA6B,OAAOA;YAExC,MAAMC,iCAAiCjB,iBAAQ,CAACW,yBAAyB,CACvEzB,UACAC,UACAyB,WACAC,eACAV,QACAW,aACAC;YAEF,IAAIE,gCAAgC,OAAOA;YAE3C,OAAO1C,KAAKM,eAAe,CAAC8B,yBAAyB,CACnDzB,UACAC,UACAyB,WACAC,eACAV,QACAW,aACAC;QAEJ;QAEA,aAAa;QACbtC,MAAMyC,sBAAsB,GAAG,CAAChC,UAAkBC;YAChD,MAAME,QAAQd,KAAKM,eAAe,CAACqC,sBAAsB,CACvDhC,UACAC;YAEF,IAAI,CAACO,IAAAA,qBAAc,EAACR,WAAW,OAAOG;YAEtC,oEAAoE;YACpE,MAAMM,YAAYC,IAAAA,mBAAY,EAACV;YAC/B,IAAI,CAACS,UAAUE,MAAM,EAAE;gBACrB,MAAMsB,cAAc5C,KAAKM,eAAe,CAACuC,uBAAuB,CAC9DlC,UACAC;gBAEF,IACEgC,eACArB,eAAW,CAACuB,+BAA+B,CAACF,cAC5C;oBACA;gBACF;gBAEA,MAAMG,eAAetB,iBAAQ,CAACkB,sBAAsB,CAAChC,UAAUC;gBAC/D,IAAImC,cAAc,OAAOA;YAC3B;YAEA,MAAMC,aAAatB,eAAW,CAACiB,sBAAsB,CAAChC,UAAUC;YAChE,IAAIoC,YAAY,OAAOA;YAEvB,OAAOlC;QACT;QAEA,qCAAqC;QACrCZ,MAAM+C,sBAAsB,GAAG,CAACtC;YAC9B,MAAMG,QAAQd,KAAKM,eAAe,CAAC2C,sBAAsB,CAACtC;YAC1D,MAAMiB,SAASC,IAAAA,gBAAS,EAAClB;YACzB,IAAI,CAACiB,QAAQ,OAAOd;YAEpB,IAAIoC,gBAAgB;YACpB,IAAIC,gBAAgB;YACpB,MAAMC,aAAajC,IAAAA,qBAAc,EAACR;YAElC,IAAI;gBACF,MAAMS,YAAYC,IAAAA,mBAAY,EAACV,UAAU;gBACzCuC,gBAAgB9B,UAAUE,MAAM;gBAChC6B,gBAAgB/B,UAAUiC,MAAM;YAClC,EAAE,OAAOC,GAAQ;gBACfxC,MAAMoB,IAAI,CAAC;oBACTqB,MAAM3B;oBACN4B,UAAU1D,GAAG2D,kBAAkB,CAACC,KAAK;oBACrCC,MAAMC,wBAAc,CAACC,yBAAyB;oBAC9C,GAAGP,CAAC;gBACN;gBACAJ,gBAAgB;gBAChBC,gBAAgB;YAClB;YAEA,IAAIW,IAAAA,kBAAW,EAACnD,WAAW;gBACzB,MAAMoD,kBAAkBC,cAAU,CAACf,sBAAsB,CACvDrB,QACAsB;gBAEFpC,MAAMoB,IAAI,IAAI6B;YAChB;YAEAjE,GAAGgC,YAAY,CAACF,QAAS,CAACG;oBAgBtBA,iBAqEAA;gBApFF,IAAIjC,GAAGmE,mBAAmB,CAAClC,OAAO;oBAChC,aAAa;oBACb,IAAIqB,YAAY;wBACd,IAAI,CAACF,iBAAiBC,eAAe;4BACnC,oDAAoD;4BACpD,MAAMe,cACJ3C,eAAW,CAAC4C,0CAA0C,CACpDvC,QACAG;4BAEJjB,MAAMoB,IAAI,IAAIgC;wBAChB;oBACF;gBACF,OAAO,IACLpE,GAAGsE,mBAAmB,CAACrC,WACvBA,kBAAAA,KAAKsC,SAAS,qBAAdtC,gBAAgBuC,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAK1E,GAAG2E,UAAU,CAACC,aAAa,IAClE;oBACA,mBAAmB;oBACnB,IAAItB,YAAY;wBACd,yCAAyC;wBACzC,MAAMc,cACJxC,eAAW,CAACiD,gDAAgD,CAC1D/C,QACAG;wBAEJ,MAAM6C,sBAAsB1B,gBACxBzB,iBAAQ,CAACoD,6DAA6D,CACpElE,UACAoB,QAEFN,iBAAQ,CAACkD,gDAAgD,CACvDhE,UACAoB;wBAENjB,MAAMoB,IAAI,IAAIgC,gBAAgBU;oBAChC;oBAEA,IAAI1B,eAAe;wBACjBpC,MAAMoB,IAAI,IACL4C,uBAAc,CAACH,gDAAgD,CAChE/C,QACAG;oBAGN;oBAEA,IAAIoB,eAAe;wBACjBrC,MAAMoB,IAAI,IACL6C,uBAAc,CAACJ,gDAAgD,CAChE/C,QACAG;oBAGN;gBACF,OAAO,IAAIE,IAAAA,8BAAuB,EAACF,OAAO;oBACxC,8BAA8B;oBAC9B,IAAIqB,YAAY;wBACd,MAAMc,cAAc/B,cAAY,CAACc,sBAAsB,CACrDtC,UACAiB,QACAG;wBAEFjB,MAAMoB,IAAI,IAAIgC;oBAChB;oBAEA,IAAIhB,eAAe;wBACjBpC,MAAMoB,IAAI,IACL4C,uBAAc,CAACE,uCAAuC,CACvDpD,QACAG;oBAGN;oBAEA,IAAIoB,eAAe;wBACjBrC,MAAMoB,IAAI,IACL6C,uBAAc,CAACC,uCAAuC,CACvDpD,QACAG;oBAGN;gBACF,OAAO,IACLjC,GAAGmF,qBAAqB,CAAClD,WACzBA,mBAAAA,KAAKsC,SAAS,qBAAdtC,iBAAgBuC,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAK1E,GAAG2E,UAAU,CAACC,aAAa,IAClE;oBACA,sBAAsB;oBACtB,IAAItB,YAAY;wBACd,MAAMwB,sBAAsB1B,gBACxBzB,iBAAQ,CAACoD,6DAA6D,CACpElE,UACAoB,QAEFN,iBAAQ,CAACkD,gDAAgD,CACvDhE,UACAoB;wBAENjB,MAAMoB,IAAI,IAAI0C;oBAChB;oBAEA,IAAI1B,eAAe;wBACjBpC,MAAMoB,IAAI,IACL4C,uBAAc,CAACE,uCAAuC,CACvDpD,QACAG;oBAGN;oBAEA,IAAIoB,eAAe;wBACjBrC,MAAMoB,IAAI,IACL6C,uBAAc,CAACC,uCAAuC,CACvDpD,QACAG;oBAGN;gBACF,OAAO,IAAIjC,GAAGoF,mBAAmB,CAACnD,OAAO;oBACvC,iBAAiB;oBACjB,IAAIqB,YAAY;wBACd,MAAMwB,sBAAsB1B,gBACxBzB,iBAAQ,CAAC0D,uDAAuD,CAC9DxE,UACAoB,QAEFN,iBAAQ,CAAC2D,0CAA0C,CACjDzE,UACAoB;wBAENjB,MAAMoB,IAAI,IAAI0C;oBAChB;oBAEA,IAAIzB,eAAe;wBACjBrC,MAAMoB,IAAI,IACL6C,uBAAc,CAACK,0CAA0C,CAC1DxD,QACAG;oBAGN;gBACF;YACF;YAEA,OAAOjB;QACT;QAEA,4CAA4C;QAC5CZ,MAAMmF,yBAAyB,GAAG,CAAC1E,UAAkBC;YACnD,MAAMQ,YAAYC,IAAAA,mBAAY,EAACV;YAC/B,IAAIQ,IAAAA,qBAAc,EAACR,aAAa,CAACS,UAAUE,MAAM,EAAE;gBACjD,MAAMgE,qBAAqB7D,iBAAQ,CAAC4D,yBAAyB,CAC3D1E,UACAC;gBAEF,IAAI0E,oBAAoB,OAAOA;YACjC;YAEA,OAAOtF,KAAKM,eAAe,CAAC+E,yBAAyB,CAAC1E,UAAUC;QAClE;QAEA,OAAOV;IACT;IAEA,OAAO;QAAEH;IAAO;AAClB"}