{"version": 3, "sources": ["../../src/server/internal-utils.ts"], "names": ["stripInternalQueries", "stripInternalSearchParams", "INTERNAL_QUERY_NAMES", "NEXT_RSC_UNION_QUERY", "EDGE_EXTENDED_INTERNAL_QUERY_NAMES", "query", "name", "url", "isEdge", "isStringUrl", "instance", "URL", "searchParams", "delete", "toString"], "mappings": ";;;;;;;;;;;;;;;IAegBA,oBAAoB;eAApBA;;IAMAC,yBAAyB;eAAzBA;;;kCAnBqB;AAErC,MAAMC,uBAAuB;IAC3B;IACA;IACA;IACA;IACA;IACAC,sCAAoB;CACrB;AAED,MAAMC,qCAAqC;IAAC;CAAgB;AAErD,SAASJ,qBAAqBK,KAAyB;IAC5D,KAAK,MAAMC,QAAQJ,qBAAsB;QACvC,OAAOG,KAAK,CAACC,KAAK;IACpB;AACF;AAEO,SAASL,0BACdM,GAAM,EACNC,MAAe;IAEf,MAAMC,cAAc,OAAOF,QAAQ;IACnC,MAAMG,WAAWD,cAAc,IAAIE,IAAIJ,OAAQA;IAC/C,KAAK,MAAMD,QAAQJ,qBAAsB;QACvCQ,SAASE,YAAY,CAACC,MAAM,CAACP;IAC/B;IAEA,IAAIE,QAAQ;QACV,KAAK,MAAMF,QAAQF,mCAAoC;YACrDM,SAASE,YAAY,CAACC,MAAM,CAACP;QAC/B;IACF;IAEA,OAAQG,cAAcC,SAASI,QAAQ,KAAKJ;AAC9C"}