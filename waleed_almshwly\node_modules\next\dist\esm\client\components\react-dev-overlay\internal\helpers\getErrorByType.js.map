{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/getErrorByType.ts"], "names": ["ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "getOriginalStackFrames", "getErrorSource", "getErrorByType", "ev", "isAppDir", "id", "event", "type", "readyRuntimeError", "runtime", "error", "reason", "frames", "toString", "componentStackFrames", "_", "Error"], "mappings": "AAAA,SACEA,sBAAsB,EACtBC,0BAA0B,QACrB,eAAc;AAErB,SAASC,sBAAsB,QAAQ,gBAAe;AAGtD,SAASC,cAAc,QAAQ,yCAAwC;AAUvE,OAAO,eAAeC,eACpBC,EAAuB,EACvBC,QAAiB;IAEjB,MAAM,EAAEC,EAAE,EAAEC,KAAK,EAAE,GAAGH;IACtB,OAAQG,MAAMC,IAAI;QAChB,KAAKT;QACL,KAAKC;YAA4B;gBAC/B,MAAMS,oBAAuC;oBAC3CH;oBACAI,SAAS;oBACTC,OAAOJ,MAAMK,MAAM;oBACnBC,QAAQ,MAAMZ,uBACZM,MAAMM,MAAM,EACZX,eAAeK,MAAMK,MAAM,GAC3BP,UACAE,MAAMK,MAAM,CAACE,QAAQ;gBAEzB;gBACA,IAAIP,MAAMC,IAAI,KAAKT,wBAAwB;oBACzCU,kBAAkBM,oBAAoB,GAAGR,MAAMQ,oBAAoB;gBACrE;gBACA,OAAON;YACT;QACA;YAAS;gBACP;YACF;IACF;IACA,6DAA6D;IAC7D,MAAMO,IAAWT;IACjB,MAAM,IAAIU,MAAM;AAClB"}