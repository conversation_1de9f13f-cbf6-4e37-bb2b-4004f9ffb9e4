{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/hot-reloader-client.ts"], "names": ["register", "onBuildError", "onBuildOk", "onBeforeRefresh", "onRefresh", "onVersionInfo", "stripAnsi", "addMessageListener", "sendMessage", "formatWebpackMessages", "HMR_ACTIONS_SENT_TO_BROWSER", "extractModulesFromTurbopackMessage", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "RuntimeError<PERSON>andler", "window", "__nextDevClientId", "Math", "round", "random", "Date", "now", "customHmrEventHandler", "turbopackMessageListeners", "MODE", "connect", "mode", "payload", "processMessage", "err", "console", "warn", "stack", "subscribeToHmrEvent", "handler", "onUnrecoverableError", "hadRuntimeError", "addTurbopackMessageListener", "cb", "push", "sendTurbopackMessage", "msg", "handleUpdateError", "performFullReload", "isFirstCompilation", "mostRecentCompilationHash", "hasCompileErrors", "clearOutdatedErrors", "clear", "handleSuccess", "isHotUpdate", "__NEXT_DATA__", "page", "isUpdateAvailable", "tryApplyUpdates", "onBeforeFastRefresh", "onFastRefresh", "handleWarnings", "warnings", "printWarnings", "formatted", "errors", "i", "length", "handleErrors", "error", "process", "env", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "startLatency", "undefined", "updatedModules", "reportHmrLatency", "endLatency", "latency", "log", "JSON", "stringify", "event", "id", "startTime", "endTime", "location", "pathname", "isPageHidden", "document", "visibilityState", "__NEXT_HMR_LATENCY_CB", "handleAvailableHash", "hash", "obj", "action", "BUILDING", "BUILT", "SYNC", "versionInfo", "hasErrors", "Boolean", "errorCount", "clientId", "hasWarnings", "warningCount", "SERVER_COMPONENT_CHANGES", "reload", "SERVER_ERROR", "errorJSON", "message", "parse", "Error", "TURBOPACK_CONNECTED", "listener", "type", "TURBOPACK_MESSAGE", "data", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "onBeforeHotUpdate", "onHotUpdateSuccess", "handleApplyUpdates", "check", "then", "apply", "stackTrace", "split", "slice", "join", "dependency<PERSON><PERSON>n"], "mappings": "AAAA,uEAAuE;AACvE,0DAA0D,GAC1D;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED,8EAA8E;AAC9E,qBAAqB;AACrB,2GAA2G;AAE3G,SACEA,QAAQ,EACRC,YAAY,EACZC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,aAAa,QACR,WAAU;AACjB,OAAOC,eAAe,gCAA+B;AACrD,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,cAAa;AAC7D,OAAOC,2BAA2B,8CAA6C;AAC/E,SAASC,2BAA2B,QAAQ,4CAA2C;AAKvF,SAASC,kCAAkC,QAAQ,gEAA+D;AAClH,SAASC,oCAAoC,QAAQ,YAAW;AAChE,SAASC,mBAAmB,QAAQ,4CAA2C;AAkB/EC,OAAOC,iBAAiB,GAAGC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AAEpE,IAAIC;AACJ,IAAIC,4BAAsE,EAAE;AAC5E,IAAIC,OAAgC;AACpC,eAAe,SAASC,QAAQC,IAA6B;IAC3DF,OAAOE;IACPzB;IAEAO,mBAAmB,CAACmB;QAClB,IAAI,CAAE,CAAA,YAAYA,OAAM,GAAI;YAC1B;QACF;QAEA,IAAI;YACFC,eAAeD;QACjB,EAAE,OAAOE,KAAU;gBAE+BA;YADhDC,QAAQC,IAAI,CACV,4BAA4BJ,UAAU,OAAQE,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKG,KAAK,YAAVH,aAAc,EAAC;QAEjE;IACF;IAEA,OAAO;QACLI,qBAAoBC,OAAY;YAC9BZ,wBAAwBY;QAC1B;QACAC;YACErB,oBAAoBsB,eAAe,GAAG;QACxC;QACAC,6BAA4BC,EAAwC;YAClEf,0BAA0BgB,IAAI,CAACD;QACjC;QACAE,sBAAqBC,GAAW;YAC9BhC,YAAYgC;QACd;QACAC,mBAAkBb,GAAY;YAC5Bc,kBAAkBd;QACpB;IACF;AACF;AAEA,yDAAyD;AACzD,IAAIe,qBAAqB;AACzB,IAAIC,4BAA2C;AAC/C,IAAIC,mBAAmB;AAEvB,SAASC;IACP,4CAA4C;IAC5C,IAAI,OAAOjB,YAAY,eAAe,OAAOA,QAAQkB,KAAK,KAAK,YAAY;QACzE,IAAIF,kBAAkB;YACpBhB,QAAQkB,KAAK;QACf;IACF;AACF;AAEA,0BAA0B;AAC1B,SAASC;IACPF;IAEA,IAAIvB,SAAS,WAAW;QACtB,MAAM0B,cACJ,CAACN,sBACA7B,OAAOoC,aAAa,CAACC,IAAI,KAAK,aAAaC;QAC9CT,qBAAqB;QACrBE,mBAAmB;QAEnB,0CAA0C;QAC1C,IAAII,aAAa;YACfI,gBAAgBC,qBAAqBC;QACvC;IACF,OAAO;QACLrD;IACF;AACF;AAEA,2CAA2C;AAC3C,SAASsD,eAAeC,QAAa;IACnCX;IAEA,MAAMG,cAAc,CAACN;IACrBA,qBAAqB;IACrBE,mBAAmB;IAEnB,SAASa;QACP,iCAAiC;QACjC,MAAMC,YAAYlD,sBAAsB;YACtCgD,UAAUA;YACVG,QAAQ,EAAE;QACZ;QAEA,IAAI,OAAO/B,YAAY,eAAe,OAAOA,QAAQC,IAAI,KAAK,YAAY;gBACpD6B;YAApB,IAAK,IAAIE,IAAI,GAAGA,MAAIF,sBAAAA,UAAUF,QAAQ,qBAAlBE,oBAAoBG,MAAM,GAAED,IAAK;gBACnD,IAAIA,MAAM,GAAG;oBACXhC,QAAQC,IAAI,CACV,+CACE;oBAEJ;gBACF;gBACAD,QAAQC,IAAI,CAACxB,UAAUqD,UAAUF,QAAQ,CAACI,EAAE;YAC9C;QACF;IACF;IAEAH;IAEA,0CAA0C;IAC1C,IAAIT,aAAa;QACfI,gBAAgBC,qBAAqBC;IACvC;AACF;AAEA,kEAAkE;AAClE,SAASQ,aAAaH,MAAW;IAC/Bd;IAEAH,qBAAqB;IACrBE,mBAAmB;IAEnB,8BAA8B;IAC9B,IAAIc,YAAYlD,sBAAsB;QACpCmD,QAAQA;QACRH,UAAU,EAAE;IACd;IAEA,6BAA6B;IAC7BxD,aAAa0D,UAAUC,MAAM,CAAC,EAAE;IAEhC,gCAAgC;IAChC,IAAI,OAAO/B,YAAY,eAAe,OAAOA,QAAQmC,KAAK,KAAK,YAAY;QACzE,IAAK,IAAIH,IAAI,GAAGA,IAAIF,UAAUC,MAAM,CAACE,MAAM,EAAED,IAAK;YAChDhC,QAAQmC,KAAK,CAAC1D,UAAUqD,UAAUC,MAAM,CAACC,EAAE;QAC7C;IACF;IAEA,gCAAgC;IAChC,0CAA0C;IAC1C,IAAII,QAAQC,GAAG,CAACC,gBAAgB,EAAE;QAChC,IAAIC,KAAKC,aAAa,EAAE;YACtBD,KAAKC,aAAa,CAACV,UAAUC,MAAM,CAAC,EAAE;YACtCQ,KAAKC,aAAa,GAAG;QACvB;IACF;AACF;AAEA,IAAIC,eAAmCC;AAEvC,SAASjB,oBAAoBkB,cAAwB;IACnD,IAAIA,eAAeV,MAAM,GAAG,GAAG;QAC7B,2DAA2D;QAC3D,sBAAsB;QACtB3D;IACF;AACF;AAEA,SAASoD,cAAciB,cAA0C;IAA1CA,IAAAA,2BAAAA,iBAAwC,EAAE;IAC/DtE;IACA,IAAIsE,eAAeV,MAAM,KAAK,GAAG;QAC/B;IACF;IAEA1D;IAEAqE;AACF;AAEA,SAASA,iBAAiBD,cAA0C;IAA1CA,IAAAA,2BAAAA,iBAAwC,EAAE;IAClE,IAAIF,cAAc;QAChB,MAAMI,aAAavD,KAAKC,GAAG;QAC3B,MAAMuD,UAAUD,aAAaJ;QAC7BzC,QAAQ+C,GAAG,CAAC,AAAC,4BAAyBD,UAAQ;QAC9CnE,YACEqE,KAAKC,SAAS,CAAC;YACbC,OAAO;YACPC,IAAIlE,OAAOC,iBAAiB;YAC5BkE,WAAWX;YACXY,SAASR;YACTvB,MAAMrC,OAAOqE,QAAQ,CAACC,QAAQ;YAC9BZ;YACA,oEAAoE;YACpE,sDAAsD;YACtDa,cAAcC,SAASC,eAAe,KAAK;QAC7C;QAEF,IAAInB,KAAKoB,qBAAqB,EAAE;YAC9BpB,KAAKoB,qBAAqB,CAACb;QAC7B;IACF;AACF;AAEA,kDAAkD;AAClD,SAASc,oBAAoBC,IAAY;IACvC,sCAAsC;IACtC9C,4BAA4B8C;AAC9B;AAEA,2DAA2D,GAC3D,SAAS/D,eAAegE,GAAqB;IAC3C,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;QACtB;IACF;IAEA,sEAAsE;IACtE,OAAQA,IAAIC,MAAM;QAChB,KAAKlF,4BAA4BmF,QAAQ;YAAE;gBACzCvB,eAAenD,KAAKC,GAAG;gBACvBS,QAAQ+C,GAAG,CAAC;gBACZ;YACF;QACA,KAAKlE,4BAA4BoF,KAAK;QACtC,KAAKpF,4BAA4BqF,IAAI;YAAE;gBACrC,IAAIJ,IAAID,IAAI,EAAED,oBAAoBE,IAAID,IAAI;gBAE1C,MAAM,EAAE9B,MAAM,EAAEH,QAAQ,EAAE,GAAGkC;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAKtF,cAAcsF,IAAIK,WAAW;gBAEvD,MAAMC,YAAYC,QAAQtC,UAAUA,OAAOE,MAAM;gBACjD,IAAImC,WAAW;oBACbzF,YACEqE,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPoB,YAAYvC,OAAOE,MAAM;wBACzBsC,UAAUtF,OAAOC,iBAAiB;oBACpC;oBAEF,OAAOgD,aAAaH;gBACtB;gBAEA,MAAMyC,cAAcH,QAAQzC,YAAYA,SAASK,MAAM;gBACvD,IAAIuC,aAAa;oBACf7F,YACEqE,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPuB,cAAc7C,SAASK,MAAM;wBAC7BsC,UAAUtF,OAAOC,iBAAiB;oBACpC;oBAEF,OAAOyC,eAAeC;gBACxB;gBAEAjD,YACEqE,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPqB,UAAUtF,OAAOC,iBAAiB;gBACpC;gBAEF,OAAOiC;YACT;QACA,KAAKtC,4BAA4B6F,wBAAwB;YAAE;gBACzDzF,OAAOqE,QAAQ,CAACqB,MAAM;gBACtB;YACF;QACA,KAAK9F,4BAA4B+F,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGf;gBACtB,IAAIe,WAAW;oBACb,MAAM,EAAEC,OAAO,EAAE5E,KAAK,EAAE,GAAG8C,KAAK+B,KAAK,CAACF;oBACtC,MAAM1C,QAAQ,IAAI6C,MAAMF;oBACxB3C,MAAMjC,KAAK,GAAGA;oBACdgC,aAAa;wBAACC;qBAAM;gBACtB;gBACA;YACF;QACA,KAAKtD,4BAA4BoG,mBAAmB;YAAE;gBACpD,KAAK,MAAMC,YAAYzF,0BAA2B;oBAChDyF,SAAS;wBACPC,MAAMtG,4BAA4BoG,mBAAmB;oBACvD;gBACF;gBACA;YACF;QACA,KAAKpG,4BAA4BuG,iBAAiB;YAAE;gBAClD,MAAMzC,iBAAiB7D,mCAAmCgF,IAAIuB,IAAI;gBAClE5D,oBAAoBkB;gBACpB,KAAK,MAAMuC,YAAYzF,0BAA2B;oBAChDyF,SAAS;wBACPC,MAAMtG,4BAA4BuG,iBAAiB;wBACnDC,MAAMvB,IAAIuB,IAAI;oBAChB;gBACF;gBACA,IAAIrG,oBAAoBsB,eAAe,EAAE;oBACvCN,QAAQC,IAAI,CAAClB;oBACb8B,kBAAkB;gBACpB;gBACAtC;gBACAqE,iBAAiBD;gBACjB;YACF;QACA;YAAS;gBACP,IAAInD,uBAAuB;oBACzBA,sBAAsBsE;oBACtB;gBACF;gBACA;YACF;IACF;AACF;AAEA,mDAAmD;AACnD,SAASvC;IACP,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOR,8BAA8BuE;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,yIAAyI;IACzI,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAc;IACvC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASxF,QAAQsF,MAAc;YAC7B,IAAIA,WAAW,QAAQ;gBACrB,yIAAyI;gBACzIF,OAAOC,GAAG,CAACI,mBAAmB,CAACzF;gBAC/BwF;YACF;QACF;QACA,yIAAyI;QACzIJ,OAAOC,GAAG,CAACK,gBAAgB,CAAC1F;IAC9B;AACF;AAEA,iEAAiE;AACjE,SAASoB,gBACPuE,iBAAsE,EACtEC,kBAAyD;IAEzD,yIAAyI;IACzI,IAAI,CAACR,OAAOC,GAAG,EAAE;QACf,8DAA8D;QAC9DzF,QAAQmC,KAAK,CAAC;QACd,4BAA4B;QAC5B;IACF;IAEA,IAAI,CAACZ,uBAAuB,CAACgE,mBAAmB;QAC9ClH;QACA;IACF;IAEA,SAAS4H,mBAAmBlG,GAAQ,EAAE4C,cAA+B;QACnE,IAAI5C,OAAOf,oBAAoBsB,eAAe,IAAI,CAACqC,gBAAgB;YACjE,IAAI5C,KAAK;gBACPC,QAAQC,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;YAEN,OAAO,IAAIjB,oBAAoBsB,eAAe,EAAE;gBAC9CN,QAAQC,IAAI,CACV;YAEJ;YACAY,kBAAkBd;YAClB;QACF;QAEA,IAAI,OAAOiG,uBAAuB,YAAY;YAC5C,iCAAiC;YACjCA,mBAAmBrD;QACrB;QAEA,IAAIpB,qBAAqB;YACvB,+DAA+D;YAC/D,6DAA6D;YAC7DC,gBACEmB,eAAeV,MAAM,GAAG,IAAIS,YAAYqD,mBACxCpD,eAAeV,MAAM,GAAG,IAAI5D,YAAY2H;QAE5C,OAAO;YACL3H;YACA,IAAI+D,QAAQC,GAAG,CAACC,gBAAgB,EAAE;gBAChCqD,kBAAkB;oBAChB,IAAIpD,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,yIAAyI;IACzIgD,OAAOC,GAAG,CACPS,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAACxD;QACL,IAAI,CAACA,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,OAAOoD,sBAAsB,YAAY;YAC3CA,kBAAkBpD;QACpB;QACA,yIAAyI;QACzI,OAAO6C,OAAOC,GAAG,CAACW,KAAK;IACzB,GACCD,IAAI,CACH,CAACxD;QACCsD,mBAAmB,MAAMtD;IAC3B,GACA,CAAC5C;QACCkG,mBAAmBlG,KAAK;IAC1B;AAEN;AAEA,OAAO,SAASc,kBAAkBd,GAAQ;IACxC,MAAMsG,aACJtG,OACC,CAAA,AAACA,IAAIG,KAAK,IAAIH,IAAIG,KAAK,CAACoG,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDzG,IAAI+E,OAAO,IACX/E,MAAM,EAAC;IAEXpB,YACEqE,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPmD;QACA/F,iBAAiB,CAAC,CAACtB,oBAAoBsB,eAAe;QACtDmG,iBAAiB1G,MAAMA,IAAI0G,eAAe,GAAG/D;IAC/C;IAGFzD,OAAOqE,QAAQ,CAACqB,MAAM;AACxB"}