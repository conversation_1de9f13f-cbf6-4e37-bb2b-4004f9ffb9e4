import { useTranslation } from "@/hooks/useTranslation";

export const About = () => {
  const { t } = useTranslation();

  return (
    <section id="about" className="py-20 bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{t("aboutMe")}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t("aboutSubtitle")}
          </p>
        </div>

        <div className="max-w-3xl mx-auto text-center">
          <p className="text-gray-600 mb-8 leading-relaxed text-lg">
            {t("aboutDescription1")}
          </p>
          <p className="text-gray-600 mb-8 leading-relaxed text-lg">
            {t("aboutDescription2")}
          </p>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 mb-1">5+</div>
              <div className="text-sm text-gray-600">{t("yearsExperience")}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 mb-1">50+</div>
              <div className="text-sm text-gray-600">{t("projectsCompleted")}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 mb-1">30+</div>
              <div className="text-sm text-gray-600">{t("happyClients")}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 mb-1">15+</div>
              <div className="text-sm text-gray-600">{t("awards")}</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
