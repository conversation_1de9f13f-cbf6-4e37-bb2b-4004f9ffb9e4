import { useEffect, useRef, useState } from 'react';

interface UseScrollRevealOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
}

export const useScrollReveal = (options: UseScrollRevealOptions = {}) => {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  const {
    threshold = 0.1,
    rootMargin = '0px 0px -50px 0px',
    triggerOnce = true
  } = options;

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          if (triggerOnce) {
            observer.unobserve(element);
          }
        } else if (!triggerOnce) {
          setIsVisible(false);
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, rootMargin, triggerOnce]);

  return { elementRef, isVisible };
};

// Animation variants
export const getAnimationClass = (animation: string, isVisible: boolean, delay: number = 0) => {
  const baseClasses = 'transition-all duration-700 ease-out';
  const delayClass = delay > 0 ? `animate-stagger-${Math.min(delay, 5)}` : '';
  
  if (!isVisible) {
    return `${baseClasses} opacity-0 ${getInitialState(animation)}`;
  }
  
  return `${baseClasses} opacity-100 ${getAnimatedState(animation)} ${delayClass}`;
};

const getInitialState = (animation: string) => {
  switch (animation) {
    case 'fadeInUp':
      return 'translate-y-8';
    case 'fadeInLeft':
      return '-translate-x-8';
    case 'fadeInRight':
      return 'translate-x-8';
    case 'scaleIn':
      return 'scale-95';
    default:
      return 'translate-y-8';
  }
};

const getAnimatedState = (animation: string) => {
  switch (animation) {
    case 'fadeInUp':
    case 'fadeInLeft':
    case 'fadeInRight':
      return 'translate-x-0 translate-y-0';
    case 'scaleIn':
      return 'scale-100';
    default:
      return 'translate-x-0 translate-y-0';
  }
};
