import { Button } from "@/components/ui/button";
import { ExternalLink, Github } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";

export const Projects = () => {
  const { t } = useTranslation();

  const projects = [
    {
      title: t("ecommercePlatform"),
      description: t("ecommerceDescription"),
      technologies: ["Next.js", "TypeScript", "Stripe", "PostgreSQL", "Tailwind"],
      liveUrl: "#",
      githubUrl: "#"
    },
    {
      title: t("taskManagementApp"),
      description: t("taskDescription"),
      technologies: ["React", "Node.js", "Socket.io", "MongoDB", "Express"],
      liveUrl: "#",
      githubUrl: "#"
    },
    {
      title: t("socialMediaDashboard"),
      description: t("socialDescription"),
      technologies: ["Next.js", "Chart.js", "Firebase", "Tailwind", "TypeScript"],
      liveUrl: "#",
      githubUrl: "#"
    }
  ];

  return (
    <section id="projects" className="py-20 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{t("projects")}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t("projectsDescription")}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <div key={index} className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">{project.title}</h3>
              <p className="text-gray-600 mb-4 leading-relaxed">{project.description}</p>
              
              <div className="flex flex-wrap gap-2 mb-6">
                {project.technologies.map((tech, techIndex) => (
                  <span
                    key={techIndex}
                    className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                  >
                    {tech}
                  </span>
                ))}
              </div>
              
              <div className="flex gap-3">
                <Button size="sm" className="flex-1 bg-gray-900 hover:bg-gray-800">
                  <a href={project.liveUrl} className="flex items-center gap-2">
                    <ExternalLink className="h-4 w-4" />
                    {t("liveDemo")}
                  </a>
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  <a href={project.githubUrl} className="flex items-center gap-2">
                    <Github className="h-4 w-4" />
                    {t("code")}
                  </a>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
