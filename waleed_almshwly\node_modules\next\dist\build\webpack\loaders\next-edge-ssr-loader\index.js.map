{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-edge-ssr-loader/index.ts"], "names": ["swapDistFolderWithEsmDistFolder", "path", "replace", "getRouteModuleOptions", "page", "options", "definition", "kind", "RouteKind", "PAGES", "normalizePagePath", "pathname", "bundlePath", "filename", "edgeSSRLoader", "dev", "absolutePagePath", "absoluteAppPath", "absoluteDocumentPath", "absolute500Path", "absoluteErrorPath", "isServerComponent", "stringifiedConfig", "stringifiedConfigBase64", "appDirLoader", "appDirLoaderBase64", "pagesType", "sriEnabled", "cache<PERSON><PERSON><PERSON>", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "serverActions", "getOptions", "JSON", "parse", "<PERSON><PERSON><PERSON>", "from", "toString", "isAppDir", "buildInfo", "getModuleBuildInfo", "_module", "nextEdgeSSR", "route", "pagePath", "utils", "contextify", "context", "rootContext", "appPath", "errorPath", "documentPath", "userland500Path", "stringifiedPagePath", "stringify", "pageModPath", "substring", "length", "WEBPACK_RESOURCE_QUERIES", "edgeSSREntry", "loadEntrypoint", "VAR_USERLAND", "VAR_PAGE", "nextConfig", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "VAR_MODULE_DOCUMENT", "VAR_MODULE_APP", "VAR_MODULE_GLOBAL_ERROR", "pageRouteModuleOptions", "errorRouteModuleOptions", "user500RouteModuleOptions", "userland500Page"], "mappings": ";;;;+BA8LA;;;eAAA;;;oCAzLmC;2BACM;2BACf;mCACQ;gCACH;AAyB/B;;;;;;;AAOA,GACA,SAASA,gCAAgCC,IAAY;IACnD,OAAOA,KAAKC,OAAO,CAAC,mBAAmB;AACzC;AAEA,SAASC,sBAAsBC,IAAY;IACzC,MAAMC,UAAoE;QACxEC,YAAY;YACVC,MAAMC,oBAAS,CAACC,KAAK;YACrBL,MAAMM,IAAAA,oCAAiB,EAACN;YACxBO,UAAUP;YACV,2CAA2C;YAC3CQ,YAAY;YACZC,UAAU;QACZ;IACF;IAEA,OAAOR;AACT;AAEA,MAAMS,gBACJ,eAAeA;IACb,MAAM,EACJC,GAAG,EACHX,IAAI,EACJY,gBAAgB,EAChBC,eAAe,EACfC,oBAAoB,EACpBC,eAAe,EACfC,iBAAiB,EACjBC,iBAAiB,EACjBC,mBAAmBC,uBAAuB,EAC1CC,cAAcC,kBAAkB,EAChCC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,eAAe,EACfC,kBAAkBC,sBAAsB,EACxCC,aAAa,EACd,GAAG,IAAI,CAACC,UAAU;IAEnB,MAAMH,mBAAqCI,KAAKC,KAAK,CACnDC,OAAOC,IAAI,CAACN,wBAAwB,UAAUO,QAAQ;IAGxD,MAAMhB,oBAAoBc,OAAOC,IAAI,CACnCd,2BAA2B,IAC3B,UACAe,QAAQ;IACV,MAAMd,eAAeY,OAAOC,IAAI,CAC9BZ,sBAAsB,IACtB,UACAa,QAAQ;IACV,MAAMC,WAAWb,cAAc;IAE/B,MAAMc,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACC,OAAO;IACjDF,UAAUG,WAAW,GAAG;QACtBtB;QACAjB,MAAMA;QACNmC;IACF;IACAC,UAAUI,KAAK,GAAG;QAChBxC;QACAY;QACAa;QACAC;IACF;IAEA,MAAMe,WAAW,IAAI,CAACC,KAAK,CAACC,UAAU,CACpC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCjC;IAEF,MAAMkC,UAAU,IAAI,CAACJ,KAAK,CAACC,UAAU,CACnC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCjD,gCAAgCiB;IAElC,MAAMkC,YAAY,IAAI,CAACL,KAAK,CAACC,UAAU,CACrC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCjD,gCAAgCoB;IAElC,MAAMgC,eAAe,IAAI,CAACN,KAAK,CAACC,UAAU,CACxC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCjD,gCAAgCkB;IAElC,MAAMmC,kBAAkBlC,kBACpB,IAAI,CAAC2B,KAAK,CAACC,UAAU,CACnB,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCjD,gCAAgCmB,oBAElC;IAEJ,MAAMmC,sBAAsBpB,KAAKqB,SAAS,CAACV;IAE3C,MAAMW,cAAc,CAAC,EAAEhC,aAAa,EAAE8B,oBAAoBG,SAAS,CACjE,GACAH,oBAAoBI,MAAM,GAAG,GAC7B,EAAEnB,WAAW,CAAC,CAAC,EAAEoB,mCAAwB,CAACC,YAAY,CAAC,CAAC,GAAG,GAAG,CAAC;IAEjE,IAAIrB,UAAU;QACZ,OAAO,MAAMsB,IAAAA,8BAAc,EACzB,gBACA;YACEC,cAAcN;YACdO,UAAU3D;QACZ,GACA;YACEuB,YAAYO,KAAKqB,SAAS,CAAC5B;YAC3BqC,YAAY1C;YACZD,mBAAmBa,KAAKqB,SAAS,CAAClC;YAClCN,KAAKmB,KAAKqB,SAAS,CAACxC;YACpBiB,eACE,OAAOA,kBAAkB,cACrB,cACAE,KAAKqB,SAAS,CAACvB;QACvB,GACA;YACEiC,yBAAyBrC,gBAAgB;QAC3C;IAEJ,OAAO;QACL,OAAO,MAAMiC,IAAAA,8BAAc,EACzB,YACA;YACEC,cAAcN;YACdO,UAAU3D;YACV8D,qBAAqBd;YACrBe,gBAAgBjB;YAChBkB,yBAAyBjB;QAC3B,GACA;YACEzB,WAAWQ,KAAKqB,SAAS,CAAC7B;YAC1BC,YAAYO,KAAKqB,SAAS,CAAC5B;YAC3BqC,YAAY1C;YACZP,KAAKmB,KAAKqB,SAAS,CAACxC;YACpBsD,wBAAwBnC,KAAKqB,SAAS,CAACpD,sBAAsBC;YAC7DkE,yBAAyBpC,KAAKqB,SAAS,CACrCpD,sBAAsB;YAExBoE,2BAA2BrC,KAAKqB,SAAS,CACvCpD,sBAAsB;QAE1B,GACA;YACEqE,iBAAiBnB;YACjBY,yBAAyBrC,gBAAgB;QAC3C;IAEJ;AACF;MACF,WAAed"}