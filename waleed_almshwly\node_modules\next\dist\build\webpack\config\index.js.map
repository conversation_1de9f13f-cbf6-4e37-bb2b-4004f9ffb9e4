{"version": 3, "sources": ["../../../../src/build/webpack/config/index.ts"], "names": ["buildConfiguration", "config", "hasAppDir", "supportedBrowsers", "rootDirectory", "customAppFile", "isDevelopment", "isServer", "isEdgeRuntime", "targetWeb", "assetPrefix", "sassOptions", "productionBrowserSourceMaps", "future", "transpilePackages", "experimental", "disableStaticImages", "serverSourceMaps", "ctx", "isProduction", "isClient", "endsWith", "slice", "fns", "base", "css", "push", "images", "fn", "pipe"], "mappings": ";;;;+BASsBA;;;eAAAA;;;sBALD;qBACD;wBACG;uBACF;AAEd,eAAeA,mBACpBC,MAA6B,EAC7B,EACEC,SAAS,EACTC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,WAAW,EACXC,2BAA2B,EAC3BC,MAAM,EACNC,iBAAiB,EACjBC,YAAY,EACZC,mBAAmB,EACnBC,gBAAgB,EAkBjB;IAED,MAAMC,MAA4B;QAChChB;QACAC;QACAC;QACAC;QACAC;QACAa,cAAc,CAACb;QACfC;QACAC;QACAY,UAAU,CAACb;QACXE;QACAC,aAAaA,cACTA,YAAYW,QAAQ,CAAC,OACnBX,YAAYY,KAAK,CAAC,GAAG,CAAC,KACtBZ,cACF;QACJC;QACAC;QACAE;QACAD;QACAE;QACAE,kBAAkBA,oBAAoB;IACxC;IAEA,IAAIM,MAAM;QAACC,IAAAA,UAAI,EAACN;QAAMO,IAAAA,QAAG,EAACP;KAAK;IAC/B,IAAI,CAACF,qBAAqB;QACxBO,IAAIG,IAAI,CAACC,IAAAA,cAAM,EAACT;IAClB;IACA,MAAMU,KAAKC,IAAAA,WAAI,KAAIN;IACnB,OAAOK,GAAG3B;AACZ"}