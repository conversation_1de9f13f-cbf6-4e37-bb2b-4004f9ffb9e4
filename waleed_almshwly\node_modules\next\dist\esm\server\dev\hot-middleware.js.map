{"version": 3, "sources": ["../../../src/server/dev/hot-middleware.ts"], "names": ["isMiddlewareFilename", "HMR_ACTIONS_SENT_TO_BROWSER", "isMiddlewareStats", "stats", "key", "compilation", "entrypoints", "keys", "stats<PERSON><PERSON><PERSON><PERSON>", "to<PERSON><PERSON>", "all", "errors", "hash", "warnings", "getStatsForSyncEvent", "clientStats", "serverStats", "hasErrors", "ts", "EventStream", "constructor", "clients", "Set", "everyClient", "fn", "client", "close", "clear", "handler", "add", "addEventListener", "delete", "publish", "payload", "send", "JSON", "stringify", "WebpackHotMiddleware", "compilers", "versionInfo", "onClientInvalid", "closed", "serverLatestStats", "action", "BUILDING", "onClientDone", "statsResult", "clientLatestStats", "Date", "now", "publishStats", "onServerInvalid", "onServerDone", "onEdgeServerInvalid", "middlewareLatestStats", "onEdgeServerDone", "onHMR", "eventStream", "syncStats", "middlewareStats", "SYNC", "moduleTrace", "BUILT", "hooks", "invalid", "tap", "done"], "mappings": "AAAA,iIAAiI;AACjI,yBAAyB;AAEzB,iDAAiD;AAEjD,wEAAwE;AACxE,kEAAkE;AAClE,sEAAsE;AACtE,sEAAsE;AACtE,qEAAqE;AACrE,wEAAwE;AACxE,4BAA4B;AAE5B,iEAAiE;AACjE,kEAAkE;AAElE,kEAAkE;AAClE,qEAAqE;AACrE,yEAAyE;AACzE,uEAAuE;AACvE,uEAAuE;AACvE,oEAAoE;AACpE,yDAAyD;AAGzD,SAASA,oBAAoB,QAAQ,oBAAmB;AAGxD,SAASC,2BAA2B,QAAQ,uBAAsB;AAElE,SAASC,kBAAkBC,KAAoB;IAC7C,KAAK,MAAMC,OAAOD,MAAME,WAAW,CAACC,WAAW,CAACC,IAAI,GAAI;QACtD,IAAIP,qBAAqBI,MAAM;YAC7B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASI,YAAYL,KAA4B;IAC/C,IAAI,CAACA,OAAO,OAAO,CAAC;IACpB,OAAOA,MAAMM,MAAM,CAAC;QAClBC,KAAK;QACLC,QAAQ;QACRC,MAAM;QACNC,UAAU;IACZ;AACF;AAEA,SAASC,qBACPC,WAAwD,EACxDC,WAAwD;IAExD,IAAI,CAACD,aAAa,OAAOC,+BAAAA,YAAab,KAAK;IAC3C,IAAI,CAACa,aAAa,OAAOD,+BAAAA,YAAaZ,KAAK;IAE3C,qDAAqD;IACrD,oGAAoG;IACpG,gEAAgE;IAChE,IAAIa,YAAYb,KAAK,CAACc,SAAS,IAAI;QACjC,OAAOD,YAAYb,KAAK;IAC1B;IAEA,0BAA0B;IAC1B,OAAOa,YAAYE,EAAE,GAAGH,YAAYG,EAAE,GAAGF,YAAYb,KAAK,GAAGY,YAAYZ,KAAK;AAChF;AAEA,MAAMgB;IAEJC,aAAc;QACZ,IAAI,CAACC,OAAO,GAAG,IAAIC;IACrB;IAEAC,YAAYC,EAAwB,EAAE;QACpC,KAAK,MAAMC,UAAU,IAAI,CAACJ,OAAO,CAAE;YACjCG,GAAGC;QACL;IACF;IAEAC,QAAQ;QACN,IAAI,CAACH,WAAW,CAAC,CAACE;YAChBA,OAAOC,KAAK;QACd;QACA,IAAI,CAACL,OAAO,CAACM,KAAK;IACpB;IAEAC,QAAQH,MAAU,EAAE;QAClB,IAAI,CAACJ,OAAO,CAACQ,GAAG,CAACJ;QACjBA,OAAOK,gBAAgB,CAAC,SAAS;YAC/B,IAAI,CAACT,OAAO,CAACU,MAAM,CAACN;QACtB;IACF;IAEAO,QAAQC,OAAY,EAAE;QACpB,IAAI,CAACV,WAAW,CAAC,CAACE;YAChBA,OAAOS,IAAI,CAACC,KAAKC,SAAS,CAACH;QAC7B;IACF;AACF;AAEA,OAAO,MAAMI;IAQXjB,YAAYkB,SAA6B,EAAEC,WAAwB,CAAE;aAyBrEC,kBAAkB;gBACG;YAAnB,IAAI,IAAI,CAACC,MAAM,MAAI,0BAAA,IAAI,CAACC,iBAAiB,qBAAtB,wBAAwBvC,KAAK,CAACc,SAAS,KAAI;YAC9D,IAAI,CAACe,OAAO,CAAC;gBACXW,QAAQ1C,4BAA4B2C,QAAQ;YAC9C;QACF;aAEAC,eAAe,CAACC;gBAEK;YADnB,IAAI,CAACC,iBAAiB,GAAG;gBAAE7B,IAAI8B,KAAKC,GAAG;gBAAI9C,OAAO2C;YAAY;YAC9D,IAAI,IAAI,CAACL,MAAM,MAAI,0BAAA,IAAI,CAACC,iBAAiB,qBAAtB,wBAAwBvC,KAAK,CAACc,SAAS,KAAI;YAC9D,IAAI,CAACiC,YAAY,CAACJ;QACpB;aAEAK,kBAAkB;gBACX,yBAED;YAFJ,IAAI,GAAC,0BAAA,IAAI,CAACT,iBAAiB,qBAAtB,wBAAwBvC,KAAK,CAACc,SAAS,KAAI;YAChD,IAAI,CAACyB,iBAAiB,GAAG;YACzB,KAAI,0BAAA,IAAI,CAACK,iBAAiB,qBAAtB,wBAAwB5C,KAAK,EAAE;gBACjC,IAAI,CAAC+C,YAAY,CAAC,IAAI,CAACH,iBAAiB,CAAC5C,KAAK;YAChD;QACF;aAEAiD,eAAe,CAACN;YACd,IAAI,IAAI,CAACL,MAAM,EAAE;YACjB,IAAIK,YAAY7B,SAAS,IAAI;gBAC3B,IAAI,CAACyB,iBAAiB,GAAG;oBAAExB,IAAI8B,KAAKC,GAAG;oBAAI9C,OAAO2C;gBAAY;gBAC9D,IAAI,CAACI,YAAY,CAACJ;YACpB;QACF;aAEAO,sBAAsB;gBACf,6BAED;YAFJ,IAAI,GAAC,8BAAA,IAAI,CAACC,qBAAqB,qBAA1B,4BAA4BnD,KAAK,CAACc,SAAS,KAAI;YACpD,IAAI,CAACqC,qBAAqB,GAAG;YAC7B,KAAI,0BAAA,IAAI,CAACP,iBAAiB,qBAAtB,wBAAwB5C,KAAK,EAAE;gBACjC,IAAI,CAAC+C,YAAY,CAAC,IAAI,CAACH,iBAAiB,CAAC5C,KAAK;YAChD;QACF;aAEAoD,mBAAmB,CAACT;YAClB,IAAI,CAAC5C,kBAAkB4C,cAAc;gBACnC,IAAI,CAACK,eAAe;gBACpB,IAAI,CAACC,YAAY,CAACN;gBAClB;YACF;YAEA,IAAIA,YAAY7B,SAAS,IAAI;gBAC3B,IAAI,CAACqC,qBAAqB,GAAG;oBAAEpC,IAAI8B,KAAKC,GAAG;oBAAI9C,OAAO2C;gBAAY;gBAClE,IAAI,CAACI,YAAY,CAACJ;YACpB;QACF;QAEA;;;;;GAKC,QACDU,QAAQ,CAAC/B;YACP,IAAI,IAAI,CAACgB,MAAM,EAAE;YACjB,IAAI,CAACgB,WAAW,CAAC7B,OAAO,CAACH;YAEzB,MAAMiC,YAAY5C,qBAChB,IAAI,CAACiC,iBAAiB,EACtB,IAAI,CAACL,iBAAiB;YAGxB,IAAIgB,WAAW;oBAEuB;gBADpC,MAAMvD,QAAQK,YAAYkD;gBAC1B,MAAMC,kBAAkBnD,aAAY,8BAAA,IAAI,CAAC8C,qBAAqB,qBAA1B,4BAA4BnD,KAAK;gBAErE,IAAI,CAAC6B,OAAO,CAAC;oBACXW,QAAQ1C,4BAA4B2D,IAAI;oBACxChD,MAAMT,MAAMS,IAAI;oBAChBD,QAAQ;2BAAKR,MAAMQ,MAAM,IAAI,EAAE;2BAAOgD,gBAAgBhD,MAAM,IAAI,EAAE;qBAAE;oBACpEE,UAAU;2BACJV,MAAMU,QAAQ,IAAI,EAAE;2BACpB8C,gBAAgB9C,QAAQ,IAAI,EAAE;qBACnC;oBACD0B,aAAa,IAAI,CAACA,WAAW;gBAC/B;YACF;QACF;aAEAW,eAAe,CAACJ;YACd,MAAM3C,QAAQ2C,YAAYrC,MAAM,CAAC;gBAC/BC,KAAK;gBACLE,MAAM;gBACNC,UAAU;gBACVF,QAAQ;gBACRkD,aAAa;YACf;YAEA,IAAI,CAAC7B,OAAO,CAAC;gBACXW,QAAQ1C,4BAA4B6D,KAAK;gBACzClD,MAAMT,MAAMS,IAAI;gBAChBC,UAAUV,MAAMU,QAAQ,IAAI,EAAE;gBAC9BF,QAAQR,MAAMQ,MAAM,IAAI,EAAE;YAC5B;QACF;aAEAqB,UAAU,CAACC;YACT,IAAI,IAAI,CAACQ,MAAM,EAAE;YACjB,IAAI,CAACgB,WAAW,CAACzB,OAAO,CAACC;QAC3B;aACAP,QAAQ;YACN,IAAI,IAAI,CAACe,MAAM,EAAE;YACjB,0EAA0E;YAC1E,sEAAsE;YACtE,IAAI,CAACA,MAAM,GAAG;YACd,IAAI,CAACgB,WAAW,CAAC/B,KAAK;QACxB;QArIE,IAAI,CAAC+B,WAAW,GAAG,IAAItC;QACvB,IAAI,CAAC4B,iBAAiB,GAAG;QACzB,IAAI,CAACO,qBAAqB,GAAG;QAC7B,IAAI,CAACZ,iBAAiB,GAAG;QACzB,IAAI,CAACD,MAAM,GAAG;QACd,IAAI,CAACF,WAAW,GAAGA;QAEnBD,SAAS,CAAC,EAAE,CAACyB,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,0BACA,IAAI,CAACzB,eAAe;QAEtBF,SAAS,CAAC,EAAE,CAACyB,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,0BAA0B,IAAI,CAACpB,YAAY;QACvEP,SAAS,CAAC,EAAE,CAACyB,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,0BACA,IAAI,CAACd,eAAe;QAEtBb,SAAS,CAAC,EAAE,CAACyB,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,0BAA0B,IAAI,CAACb,YAAY;QACvEd,SAAS,CAAC,EAAE,CAACyB,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,0BAA0B,IAAI,CAACV,gBAAgB;QAC3EjB,SAAS,CAAC,EAAE,CAACyB,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,0BACA,IAAI,CAACZ,mBAAmB;IAE5B;AAgHF"}