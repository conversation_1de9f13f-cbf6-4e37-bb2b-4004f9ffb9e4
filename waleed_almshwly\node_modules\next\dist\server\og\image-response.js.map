{"version": 3, "sources": ["../../../src/server/og/image-response.ts"], "names": ["ImageResponse", "importModule", "process", "env", "NEXT_RUNTIME", "Response", "displayName", "constructor", "args", "readable", "ReadableStream", "start", "controller", "OGImageResponse", "imageResponse", "body", "close", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "value", "read", "enqueue", "options", "headers", "NODE_ENV", "status", "statusText"], "mappings": ";;;;+BAkBaA;;;eAAAA;;;AAhBb,SAASC;IAGP,OAAO,MAAM,CACXC,QAAQC,GAAG,CAACC,YAAY,KAAK,SACzB,gDACA;AAER;AAQO,MAAMJ,sBAAsBK;qBACnBC,cAAc;IAC5BC,YAAY,GAAGC,IAAsD,CAAE;QACrE,MAAMC,WAAW,IAAIC,eAAe;YAClC,MAAMC,OAAMC,UAAU;gBACpB,MAAMC,kBAGJ,AAFA,2DAA2D;gBAC3D,uCAAuC;gBACtC,CAAA,MAAMZ,cAAa,EAAGD,aAAa;gBACtC,MAAMc,gBAAgB,IAAID,mBAAmBL;gBAE7C,IAAI,CAACM,cAAcC,IAAI,EAAE;oBACvB,OAAOH,WAAWI,KAAK;gBACzB;gBAEA,MAAMC,SAASH,cAAcC,IAAI,CAAEG,SAAS;gBAC5C,MAAO,KAAM;oBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMH,OAAOI,IAAI;oBACzC,IAAIF,MAAM;wBACR,OAAOP,WAAWI,KAAK;oBACzB;oBACAJ,WAAWU,OAAO,CAACF;gBACrB;YACF;QACF;QAEA,MAAMG,UAAUf,IAAI,CAAC,EAAE,IAAI,CAAC;QAE5B,KAAK,CAACC,UAAU;YACde,SAAS;gBACP,gBAAgB;gBAChB,iBACEtB,QAAQC,GAAG,CAACsB,QAAQ,KAAK,gBACrB,uBACA;gBACN,GAAGF,QAAQC,OAAO;YACpB;YACAE,QAAQH,QAAQG,MAAM;YACtBC,YAAYJ,QAAQI,UAAU;QAChC;IACF;AACF"}