"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hero: function() { return /* binding */ Hero; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/avatar */ \"./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Download,Github,Linkedin,Mail!=!lucide-react */ \"__barrel_optimize__?names=ArrowDown,Download,Github,Linkedin,Mail!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useTranslation */ \"./src/hooks/useTranslation.tsx\");\n/* harmony import */ var _hooks_useRTL__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useRTL */ \"./src/hooks/useRTL.tsx\");\n/* harmony import */ var _hooks_useScrollReveal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useScrollReveal */ \"./src/hooks/useScrollReveal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Hero = ()=>{\n    _s();\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const { iconLeft, isRTL } = (0,_hooks_useRTL__WEBPACK_IMPORTED_MODULE_4__.useRTL)();\n    const { elementRef: heroRef, isVisible: heroVisible } = (0,_hooks_useScrollReveal__WEBPACK_IMPORTED_MODULE_5__.useScrollReveal)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"home\",\n        className: \"pt-20 min-h-screen flex items-center justify-center bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                        className: \"w-24 h-24 mx-auto mb-6 ring-2 ring-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                src: \"/lovable-uploads/6d3fd0a4-5be1-42cf-ada4-ae04ab1679d3.png\",\n                                alt: \"Waleed Almshwly\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                className: \"text-xl font-semibold bg-gray-900 text-white\",\n                                children: \"WA\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n                    children: t(\"fullStackDeveloper\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-8 \".concat(isRTL ? \"sm:flex-row-reverse\" : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            className: \"bg-gray-900 hover:bg-gray-800 text-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#projects\",\n                                className: \"flex items-center gap-2\",\n                                children: t(\"viewMyWork\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#contact\",\n                                children: t(\"getInTouch\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/resume.pdf\",\n                                download: true,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Download, {\n                                        className: \"w-4 h-4 \".concat(iconLeft)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    t(\"downloadCV\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center gap-6 mb-12 \".concat(isRTL ? \"flex-row-reverse\" : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://github.com\",\n                            className: \"p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-900 transition-colors\",\n                            \"aria-label\": \"GitHub Profile\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Github, {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://linkedin.com\",\n                            className: \"p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-900 transition-colors\",\n                            \"aria-label\": \"LinkedIn Profile\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Linkedin, {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"mailto:<EMAIL>\",\n                            className: \"p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-900 transition-colors\",\n                            \"aria-label\": \"Send Email\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Mail, {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"#about\",\n                        className: \"inline-block p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__.ArrowDown, {\n                            className: \"h-5 w-5 text-gray-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Hero, \"KHjJFxTHqhLiX5T7XNv8c5aBywI=\", false, function() {\n    return [\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        _hooks_useRTL__WEBPACK_IMPORTED_MODULE_4__.useRTL,\n        _hooks_useScrollReveal__WEBPACK_IMPORTED_MODULE_5__.useScrollReveal\n    ];\n});\n_c = Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./src/components/Hero.tsx\n"));

/***/ }),

/***/ "./src/hooks/useScrollReveal.tsx":
/*!***************************************!*\
  !*** ./src/hooks/useScrollReveal.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAnimationClass: function() { return /* binding */ getAnimationClass; },\n/* harmony export */   useScrollReveal: function() { return /* binding */ useScrollReveal; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nvar _s = $RefreshSig$();\n\nconst useScrollReveal = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { threshold = 0.1, rootMargin = \"0px 0px -50px 0px\", triggerOnce = true } = options;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const element = elementRef.current;\n        if (!element) return;\n        const observer = new IntersectionObserver((param)=>{\n            let [entry] = param;\n            if (entry.isIntersecting) {\n                setIsVisible(true);\n                if (triggerOnce) {\n                    observer.unobserve(element);\n                }\n            } else if (!triggerOnce) {\n                setIsVisible(false);\n            }\n        }, {\n            threshold,\n            rootMargin\n        });\n        observer.observe(element);\n        return ()=>{\n            observer.unobserve(element);\n        };\n    }, [\n        threshold,\n        rootMargin,\n        triggerOnce\n    ]);\n    return {\n        elementRef,\n        isVisible\n    };\n};\n_s(useScrollReveal, \"ars/gJ7qRrRI4qdM8DRo1FWAkKE=\");\n// Animation variants\nconst getAnimationClass = function(animation, isVisible) {\n    let delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n    const baseClasses = \"transition-all duration-700 ease-out\";\n    const delayClass = delay > 0 ? \"animate-stagger-\".concat(Math.min(delay, 5)) : \"\";\n    if (!isVisible) {\n        return \"\".concat(baseClasses, \" opacity-0 \").concat(getInitialState(animation));\n    }\n    return \"\".concat(baseClasses, \" opacity-100 \").concat(getAnimatedState(animation), \" \").concat(delayClass);\n};\nconst getInitialState = (animation)=>{\n    switch(animation){\n        case \"fadeInUp\":\n            return \"translate-y-8\";\n        case \"fadeInLeft\":\n            return \"-translate-x-8\";\n        case \"fadeInRight\":\n            return \"translate-x-8\";\n        case \"scaleIn\":\n            return \"scale-95\";\n        default:\n            return \"translate-y-8\";\n    }\n};\nconst getAnimatedState = (animation)=>{\n    switch(animation){\n        case \"fadeInUp\":\n        case \"fadeInLeft\":\n        case \"fadeInRight\":\n            return \"translate-x-0 translate-y-0\";\n        case \"scaleIn\":\n            return \"scale-100\";\n        default:\n            return \"translate-x-0 translate-y-0\";\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useScrollReveal.tsx\n"));

/***/ })

});