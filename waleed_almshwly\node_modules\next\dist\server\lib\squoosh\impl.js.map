{"version": 3, "sources": ["../../../../src/server/lib/squoosh/impl.ts"], "names": ["decodeBuffer", "encodeAvif", "encodeJpeg", "encodePng", "encodeWebp", "resize", "rotate", "_buffer", "Object", "buffer", "<PERSON><PERSON><PERSON>", "from", "firstChunk", "slice", "firstChunkString", "Array", "map", "v", "String", "fromCodePoint", "join", "key", "entries", "supportedFormats", "find", "detectors", "some", "detector", "exec", "Error", "encoder", "mod", "dec", "rgba", "decode", "Uint8Array", "image", "numRotations", "ImageData", "m", "preprocessors", "instantiate", "data", "width", "height", "p", "defaultOptions", "quality", "e", "enc", "r", "encode", "defaultEncoderOptions", "val", "autoOptimize", "min", "cqLevel", "Math", "round"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAKsBA,YAAY;eAAZA;;IA8EAC,UAAU;eAAVA;;IA9BAC,UAAU;eAAVA;;IAgDAC,SAAS;eAATA;;IAjCAC,UAAU;eAAVA;;IA3BAC,MAAM;eAANA;;IAhBAC,MAAM;eAANA;;;wBAzBoC;mEACpC;;;;;;AAIf,eAAeN,aACpBO,OAA4B;QAOhBC;IALZ,MAAMC,SAASC,OAAOC,IAAI,CAACJ;IAC3B,MAAMK,aAAaH,OAAOI,KAAK,CAAC,GAAG;IACnC,MAAMC,mBAAmBC,MAAMJ,IAAI,CAACC,YACjCI,GAAG,CAAC,CAACC,IAAMC,OAAOC,aAAa,CAACF,IAChCG,IAAI,CAAC;IACR,MAAMC,OAAMb,uBAAAA,OAAOc,OAAO,CAACC,cAAgB,EAAEC,IAAI,CAAC,CAAC,GAAG,EAAEC,SAAS,EAAE,CAAC,GAClEA,UAAUC,IAAI,CAAC,CAACC,WAAaA,SAASC,IAAI,CAACd,wCADjCN,oBAET,CAAC,EAAE;IACN,IAAI,CAACa,KAAK;QACR,MAAMQ,MAAM,CAAC,gCAAgC,CAAC;IAChD;IACA,MAAMC,UAAUP,cAAgB,CAACF,IAAI;IACrC,MAAMU,MAAM,MAAMD,QAAQE,GAAG;IAC7B,MAAMC,OAAOF,IAAIG,MAAM,CAAC,IAAIC,WAAW1B;IACvC,OAAOwB;AACT;AAEO,eAAe3B,OACpB8B,KAAgB,EAChBC,YAAoB;IAEpBD,QAAQE,mBAAS,CAAC3B,IAAI,CAACyB;IAEvB,MAAMG,IAAI,MAAMC,qBAAa,CAAC,SAAS,CAACC,WAAW;IACnD,OAAO,MAAMF,EAAEH,MAAMM,IAAI,EAAEN,MAAMO,KAAK,EAAEP,MAAMQ,MAAM,EAAE;QAAEP;IAAa;AACvE;AAQO,eAAehC,OAAO,EAAE+B,KAAK,EAAEO,KAAK,EAAEC,MAAM,EAAc;IAC/DR,QAAQE,mBAAS,CAAC3B,IAAI,CAACyB;IAEvB,MAAMS,IAAIL,qBAAa,CAAC,SAAS;IACjC,MAAMD,IAAI,MAAMM,EAAEJ,WAAW;IAC7B,OAAO,MAAMF,EAAEH,MAAMM,IAAI,EAAEN,MAAMO,KAAK,EAAEP,MAAMQ,MAAM,EAAE;QACpD,GAAGC,EAAEC,cAAc;QACnBH;QACAC;IACF;AACF;AAEO,eAAe1C,WACpBkC,KAAgB,EAChB,EAAEW,OAAO,EAAuB;IAEhCX,QAAQE,mBAAS,CAAC3B,IAAI,CAACyB;IAEvB,MAAMY,IAAIzB,cAAgB,CAAC,UAAU;IACrC,MAAMgB,IAAI,MAAMS,EAAEC,GAAG;IACrB,MAAMC,IAAI,MAAMX,EAAEY,MAAM,CAACf,MAAMM,IAAI,EAAEN,MAAMO,KAAK,EAAEP,MAAMQ,MAAM,EAAE;QAC9D,GAAGI,EAAEI,qBAAqB;QAC1BL;IACF;IACA,OAAOrC,OAAOC,IAAI,CAACuC;AACrB;AAEO,eAAe9C,WACpBgC,KAAgB,EAChB,EAAEW,OAAO,EAAuB;IAEhCX,QAAQE,mBAAS,CAAC3B,IAAI,CAACyB;IAEvB,MAAMY,IAAIzB,cAAgB,CAAC,OAAO;IAClC,MAAMgB,IAAI,MAAMS,EAAEC,GAAG;IACrB,MAAMC,IAAI,MAAMX,EAAEY,MAAM,CAACf,MAAMM,IAAI,EAAEN,MAAMO,KAAK,EAAEP,MAAMQ,MAAM,EAAE;QAC9D,GAAGI,EAAEI,qBAAqB;QAC1BL;IACF;IACA,OAAOrC,OAAOC,IAAI,CAACuC;AACrB;AAEO,eAAejD,WACpBmC,KAAgB,EAChB,EAAEW,OAAO,EAAuB;IAEhCX,QAAQE,mBAAS,CAAC3B,IAAI,CAACyB;IAEvB,MAAMY,IAAIzB,cAAgB,CAAC,OAAO;IAClC,MAAMgB,IAAI,MAAMS,EAAEC,GAAG;IACrB,MAAMI,MAAML,EAAEM,YAAY,CAACC,GAAG,IAAI;IAClC,MAAML,IAAI,MAAMX,EAAEY,MAAM,CAACf,MAAMM,IAAI,EAAEN,MAAMO,KAAK,EAAEP,MAAMQ,MAAM,EAAE;QAC9D,GAAGI,EAAEI,qBAAqB;QAC1B,8DAA8D;QAC9D,qDAAqD;QACrDI,SAASC,KAAKC,KAAK,CAACL,MAAM,AAACN,UAAU,MAAOM;IAC9C;IACA,OAAO3C,OAAOC,IAAI,CAACuC;AACrB;AAEO,eAAe/C,UACpBiC,KAAgB;IAEhBA,QAAQE,mBAAS,CAAC3B,IAAI,CAACyB;IAEvB,MAAMY,IAAIzB,cAAgB,CAAC,SAAS;IACpC,MAAMgB,IAAI,MAAMS,EAAEC,GAAG;IACrB,MAAMC,IAAI,MAAMX,EAAEY,MAAM,CAACf,MAAMM,IAAI,EAAEN,MAAMO,KAAK,EAAEP,MAAMQ,MAAM,EAAE;QAC9D,GAAGI,EAAEI,qBAAqB;IAC5B;IACA,OAAO1C,OAAOC,IAAI,CAACuC;AACrB"}