{"version": 3, "sources": ["../../../../src/server/typescript/rules/metadata.ts"], "names": ["NEXT_TS_ERRORS", "getInfo", "getSource", "getTs", "getType<PERSON><PERSON>cker", "isPositionInsideNode", "TYPE_ANOTATION", "TYPE_ANOTATION_ASYNC", "TYPE_IMPORT", "getMetadataExport", "fileName", "position", "source", "metadataExport", "ts", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "visit", "node", "isVariableStatement", "modifiers", "some", "m", "kind", "SyntaxKind", "ExportKeyword", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "name", "getText", "cachedProxiedLanguageService", "cachedProxiedLanguageServiceHost", "getProxiedLanguageService", "languageService", "languageServiceHost", "ProxiedLanguageServiceHost", "getScriptFileNames", "names", "Set", "files", "hasOwnProperty", "add", "file", "addFile", "body", "snap", "ScriptSnapshot", "fromString", "getChangeRange", "_", "undefined", "existing", "ver", "readFile", "<PERSON><PERSON><PERSON><PERSON>", "fileExists", "log", "trace", "error", "getCompilationSettings", "getScriptIsOpen", "getCurrentDirectory", "getDefaultLibFileName", "o", "getScriptVersion", "toString", "getScriptSnapshot", "createLanguageService", "createDocumentRegistry", "updateVirtualFileWithType", "isGenerateMetadata", "sourceText", "getFullText", "nodeEnd", "annotation", "isFunctionDeclaration", "getFullStart", "isAsync", "AsyncKeyword", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "newSource", "slice", "length", "isTyped", "type", "proxyDiagnostics", "pos", "n", "diagnostics", "getSemanticDiagnostics", "filter", "d", "start", "map", "category", "code", "messageText", "metadata", "filterCompletionsAtPosition", "_options", "prior", "newPos", "completions", "getCompletionsAtPosition", "isIncomplete", "entries", "e", "ScriptElementKind", "memberVariableElement", "typeElement", "string", "includes", "insertText", "test", "kindModifiers", "sortText", "labelDetails", "description", "data", "getSemanticDiagnosticsForExportVariableStatementInClientEntry", "DiagnosticCategory", "Error", "INVALID_METADATA_EXPORT", "getStart", "getWidth", "getSemanticDiagnosticsForExportVariableStatement", "getSemanticDiagnosticsForExportDeclarationInClientEntry", "exportClause", "isNamedExports", "elements", "push", "getSemanticDiagnosticsForExportDeclaration", "typeC<PERSON>cker", "symbol", "getSymbolAtLocation", "metadataSymbol", "getAliasedSymbol", "isVariableDeclaration", "declarationFileName", "getSourceFile", "isSameFile", "getCompletionEntryDetails", "entryName", "formatOptions", "preferences", "details", "getQuickInfoAtPosition", "insight", "getDefinitionAndBoundSpan", "definitionInfoAndBoundSpan", "textSpan"], "mappings": "AAAA,SAASA,cAAc,QAAQ,cAAa;AAC5C,SACEC,OAAO,EACPC,SAAS,EACTC,KAAK,EACLC,cAAc,EACdC,oBAAoB,QACf,WAAU;AAIjB,MAAMC,iBAAiB;AACvB,MAAMC,uBAAuB;AAC7B,MAAMC,cAAc,CAAC,wCAAwC,CAAC;AAE9D,+CAA+C;AAC/C,SAASC,kBAAkBC,QAAgB,EAAEC,QAAgB;IAC3D,MAAMC,SAASV,UAAUQ;IACzB,IAAIG;IAEJ,IAAID,QAAQ;QACV,MAAME,KAAKX;QACXW,GAAGC,YAAY,CAACH,QAAQ,SAASI,MAAMC,IAAI;YACzC,IAAIJ,gBAAgB;YAEpB,uBAAuB;YACvB,IAAIR,qBAAqBM,UAAUM,OAAO;oBAItCA;gBAHF,kBAAkB;gBAClB,IACEH,GAAGI,mBAAmB,CAACD,WACvBA,kBAAAA,KAAKE,SAAS,qBAAdF,gBAAgBG,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKR,GAAGS,UAAU,CAACC,aAAa,IAClE;oBACA,IAAIV,GAAGW,yBAAyB,CAACR,KAAKS,eAAe,GAAG;wBACtD,KAAK,MAAMC,eAAeV,KAAKS,eAAe,CAACE,YAAY,CAAE;4BAC3D,IACEvB,qBAAqBM,UAAUgB,gBAC/BA,YAAYE,IAAI,CAACC,OAAO,OAAO,YAC/B;gCACA,gCAAgC;gCAChCjB,iBAAiBc;gCACjB;4BACF;wBACF;oBACF;gBACF;YACF;QACF;IACF;IACA,OAAOd;AACT;AAEA,IAAIkB;AACJ,IAAIC;AACJ,SAASC;IACP,IAAIF,8BACF,OAAO;QACLG,iBAAiBH;QACjBI,qBACEH;IAGJ;IAEF,MAAMG,sBAAsBlC,UAAUkC,mBAAmB;IAEzD,MAAMrB,KAAKX;IACX,MAAMiC;QA0BJC,qBAA+B;YAC7B,MAAMC,QAAqB,IAAIC;YAC/B,IAAK,IAAIV,QAAQ,IAAI,CAACW,KAAK,CAAE;gBAC3B,IAAI,IAAI,CAACA,KAAK,CAACC,cAAc,CAACZ,OAAO;oBACnCS,MAAMI,GAAG,CAACb;gBACZ;YACF;YACA,MAAMW,QAAQL,oBAAoBE,kBAAkB;YACpD,KAAK,MAAMM,QAAQH,MAAO;gBACxBF,MAAMI,GAAG,CAACC;YACZ;YACA,OAAO;mBAAIL;aAAM;QACnB;QAEAM,QAAQlC,QAAgB,EAAEmC,IAAY,EAAE;YACtC,MAAMC,OAAOhC,GAAGiC,cAAc,CAACC,UAAU,CAACH;YAC1CC,KAAKG,cAAc,GAAG,CAACC,IAAMC;YAC7B,MAAMC,WAAW,IAAI,CAACZ,KAAK,CAAC9B,SAAS;YACrC,IAAI0C,UAAU;gBACZ,IAAI,CAACZ,KAAK,CAAC9B,SAAS,CAAC2C,GAAG;gBACxB,IAAI,CAACb,KAAK,CAAC9B,SAAS,CAACiC,IAAI,GAAGG;YAC9B,OAAO;gBACL,IAAI,CAACN,KAAK,CAAC9B,SAAS,GAAG;oBAAE2C,KAAK;oBAAGV,MAAMG;gBAAK;YAC9C;QACF;QAEAQ,SAAS5C,QAAgB,EAAE;YACzB,MAAMiC,OAAO,IAAI,CAACH,KAAK,CAAC9B,SAAS;YACjC,OAAOiC,OACHA,KAAKA,IAAI,CAACb,OAAO,CAAC,GAAGa,KAAKA,IAAI,CAACY,SAAS,MACxCpB,oBAAoBmB,QAAQ,CAAC5C;QACnC;QACA8C,WAAW9C,QAAgB,EAAE;YAC3B,OACE,IAAI,CAAC8B,KAAK,CAAC9B,SAAS,KAAKyC,aACzBhB,oBAAoBqB,UAAU,CAAC9C;QAEnC;;iBA9DA8B,QAEI,CAAC;iBAELiB,MAAM,KAAO;iBACbC,QAAQ,KAAO;iBACfC,QAAQ,KAAO;iBACfC,yBAAyB,IAAMzB,oBAAoByB,sBAAsB;iBACzEC,kBAAkB,IAAM;iBACxBC,sBAAsB,IAAM3B,oBAAoB2B,mBAAmB;iBACnEC,wBAAwB,CAACC,IACvB7B,oBAAoB4B,qBAAqB,CAACC;iBAE5CC,mBAAmB,CAACvD;gBAClB,MAAMiC,OAAO,IAAI,CAACH,KAAK,CAAC9B,SAAS;gBACjC,IAAI,CAACiC,MAAM,OAAOR,oBAAoB8B,gBAAgB,CAACvD;gBACvD,OAAOiC,KAAKU,GAAG,CAACa,QAAQ;YAC1B;iBAEAC,oBAAoB,CAACzD;gBACnB,MAAMiC,OAAO,IAAI,CAACH,KAAK,CAAC9B,SAAS;gBACjC,IAAI,CAACiC,MAAM,OAAOR,oBAAoBgC,iBAAiB,CAACzD;gBACxD,OAAOiC,KAAKA,IAAI;YAClB;;IAwCF;IAEAX,mCAAmC,IAAII;IACvCL,+BAA+BjB,GAAGsD,qBAAqB,CACrDpC,kCACAlB,GAAGuD,sBAAsB;IAE3B,OAAO;QACLnC,iBAAiBH;QACjBI,qBACEH;IAGJ;AACF;AAEA,SAASsC,0BACP5D,QAAgB,EAChBO,IAAiE,EACjEsD,kBAA4B;IAE5B,MAAM3D,SAASV,UAAUQ;IACzB,IAAI,CAACE,QAAQ;IAEb,0DAA0D;IAC1D,MAAM4D,aAAa5D,OAAO6D,WAAW;IACrC,IAAIC;IACJ,IAAIC;IAEJ,MAAM7D,KAAKX;IACX,IAAIW,GAAG8D,qBAAqB,CAAC3D,OAAO;QAClC,IAAIsD,oBAAoB;gBAENtD;YADhByD,UAAUzD,KAAK4B,IAAI,CAAEgC,YAAY;YACjC,MAAMC,WAAU7D,kBAAAA,KAAKE,SAAS,qBAAdF,gBAAgBG,IAAI,CAClC,CAACC,IAAMA,EAAEC,IAAI,KAAKR,GAAGS,UAAU,CAACwD,YAAY;YAE9CJ,aAAaG,UAAUvE,uBAAuBD;QAChD,OAAO;YACL;QACF;IACF,OAAO;QACLoE,UAAUzD,KAAKY,IAAI,CAACgD,YAAY,KAAK5D,KAAKY,IAAI,CAACmD,YAAY;QAC3DL,aAAarE;IACf;IAEA,MAAM2E,YACJT,WAAWU,KAAK,CAAC,GAAGR,WACpBC,aACAH,WAAWU,KAAK,CAACR,WACjBlE;IACF,MAAM,EAAE2B,mBAAmB,EAAE,GAAGF;IAChCE,oBAAoBS,OAAO,CAAClC,UAAUuE;IAEtC,OAAO;QAACP;QAASC,WAAWQ,MAAM;KAAC;AACrC;AAEA,SAASC,QACPnE,IAAiE;IAEjE,OAAOA,KAAKoE,IAAI,KAAKlC;AACvB;AAEA,SAASmC,iBACP5E,QAAgB,EAChB6E,GAAa,EACbC,CAA8D;IAE9D,kBAAkB;IAClB,MAAM,EAAEtD,eAAe,EAAE,GAAGD;IAC5B,MAAMwD,cAAcvD,gBAAgBwD,sBAAsB,CAAChF;IAC3D,MAAME,SAASV,UAAUQ;IAEzB,6BAA6B;IAC7B,OAAO+E,YACJE,MAAM,CAAC,CAACC;QACP,IAAIA,EAAEC,KAAK,KAAK1C,aAAayC,EAAET,MAAM,KAAKhC,WAAW,OAAO;QAC5D,IAAIyC,EAAEC,KAAK,GAAGL,EAAEX,YAAY,IAAI,OAAO;QACvC,IAAIe,EAAEC,KAAK,GAAGD,EAAET,MAAM,IAAIK,EAAEX,YAAY,KAAKW,EAAER,YAAY,KAAKO,GAAG,CAAC,EAAE,EACpE,OAAO;QACT,OAAO;IACT,GACCO,GAAG,CAAC,CAACF;QACJ,OAAO;YACLjD,MAAM/B;YACNmF,UAAUH,EAAEG,QAAQ;YACpBC,MAAMJ,EAAEI,IAAI;YACZC,aAAaL,EAAEK,WAAW;YAC1BJ,OAAOD,EAAEC,KAAK,GAAIN,GAAG,CAAC,EAAE,GAAGK,EAAEC,KAAK,GAAGD,EAAEC,KAAK,GAAIN,GAAG,CAAC,EAAE;YACtDJ,QAAQS,EAAET,MAAM;QAClB;IACF;AACJ;AAEA,MAAMe,WAAW;IACfC,6BACEzF,QAAgB,EAChBC,QAAgB,EAChByF,QAAa,EACbC,KAAqD;QAErD,MAAMpF,OAAOR,kBAAkBC,UAAUC;QACzC,IAAI,CAACM,MAAM,OAAOoF;QAClB,IAAIjB,QAAQnE,OAAO,OAAOoF;QAE1B,MAAMvF,KAAKX;QAEX,0DAA0D;QAC1D,MAAMoF,MAAMjB,0BAA0B5D,UAAUO;QAChD,IAAIsE,QAAQpC,WAAW,OAAOkD;QAE9B,kBAAkB;QAClB,MAAM,EAAEnE,eAAe,EAAE,GAAGD;QAC5B,MAAMqE,SAAS3F,YAAY4E,GAAG,CAAC,EAAE,GAAG5E,WAAWA,WAAW4E,GAAG,CAAC,EAAE;QAChE,MAAMgB,cAAcrE,gBAAgBsE,wBAAwB,CAC1D9F,UACA4F,QACAnD;QAGF,IAAIoD,aAAa;YACfA,YAAYE,YAAY,GAAG;YAE3BF,YAAYG,OAAO,GAAGH,YAAYG,OAAO,CACtCf,MAAM,CAAC,CAACgB;gBACP,OAAO;oBACL7F,GAAG8F,iBAAiB,CAACC,qBAAqB;oBAC1C/F,GAAG8F,iBAAiB,CAACE,WAAW;oBAChChG,GAAG8F,iBAAiB,CAACG,MAAM;iBAC5B,CAACC,QAAQ,CAACL,EAAErF,IAAI;YACnB,GACCwE,GAAG,CAAC,CAACa;gBACJ,MAAMM,aACJN,EAAErF,IAAI,KAAKR,GAAG8F,iBAAiB,CAACC,qBAAqB,IACrD,kBAAkBK,IAAI,CAACP,EAAE9E,IAAI,IACzB8E,EAAE9E,IAAI,GAAG,OACT8E,EAAE9E,IAAI;gBAEZ,OAAO;oBACLA,MAAM8E,EAAE9E,IAAI;oBACZoF;oBACA3F,MAAMqF,EAAErF,IAAI;oBACZ6F,eAAeR,EAAEQ,aAAa;oBAC9BC,UAAU,MAAMT,EAAE9E,IAAI;oBACtBwF,cAAc;wBACZC,aAAa,CAAC,gBAAgB,CAAC;oBACjC;oBACAC,MAAMZ,EAAEY,IAAI;gBACd;YACF;YAEF,OAAOhB;QACT;QAEA,OAAOF;IACT;IAEAmB,+DACE9G,QAAgB,EAChBO,IAA+D;QAE/D,MAAML,SAASV,UAAUQ;QACzB,MAAMI,KAAKX;QAEX,+EAA+E;QAC/E,IAAIW,GAAG8D,qBAAqB,CAAC3D,OAAO;gBAC9BA;YAAJ,IAAIA,EAAAA,aAAAA,KAAKY,IAAI,qBAATZ,WAAWa,OAAO,QAAO,oBAAoB;gBAC/C,OAAO;oBACL;wBACEa,MAAM/B;wBACNmF,UAAUjF,GAAG2G,kBAAkB,CAACC,KAAK;wBACrC1B,MAAMhG,eAAe2H,uBAAuB;wBAC5C1B,aAAa,CAAC,wEAAwE,CAAC;wBACvFJ,OAAO5E,KAAKY,IAAI,CAAC+F,QAAQ;wBACzBzC,QAAQlE,KAAKY,IAAI,CAACgG,QAAQ;oBAC5B;iBACD;YACH;QACF,OAAO;YACL,KAAK,MAAMlG,eAAeV,KAAKS,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAMC,OAAOF,YAAYE,IAAI,CAACC,OAAO;gBACrC,IAAID,SAAS,YAAY;oBACvB,OAAO;wBACL;4BACEc,MAAM/B;4BACNmF,UAAUjF,GAAG2G,kBAAkB,CAACC,KAAK;4BACrC1B,MAAMhG,eAAe2H,uBAAuB;4BAC5C1B,aAAa,CAAC,gEAAgE,CAAC;4BAC/EJ,OAAOlE,YAAYE,IAAI,CAAC+F,QAAQ;4BAChCzC,QAAQxD,YAAYE,IAAI,CAACgG,QAAQ;wBACnC;qBACD;gBACH;YACF;QACF;QACA,OAAO,EAAE;IACX;IAEAC,kDACEpH,QAAgB,EAChBO,IAA+D;QAE/D,MAAMH,KAAKX;QAEX,IAAIW,GAAG8D,qBAAqB,CAAC3D,OAAO;gBAC9BA;YAAJ,IAAIA,EAAAA,aAAAA,KAAKY,IAAI,qBAATZ,WAAWa,OAAO,QAAO,oBAAoB;gBAC/C,IAAIsD,QAAQnE,OAAO,OAAO,EAAE;gBAE5B,0DAA0D;gBAC1D,MAAMsE,MAAMjB,0BAA0B5D,UAAUO,MAAM;gBACtD,IAAI,CAACsE,KAAK,OAAO,EAAE;gBAEnB,OAAOD,iBAAiB5E,UAAU6E,KAAKtE;YACzC;QACF,OAAO;YACL,KAAK,MAAMU,eAAeV,KAAKS,eAAe,CAACE,YAAY,CAAE;gBAC3D,IAAID,YAAYE,IAAI,CAACC,OAAO,OAAO,YAAY;oBAC7C,IAAIsD,QAAQzD,cAAc;oBAE1B,0DAA0D;oBAC1D,MAAM4D,MAAMjB,0BAA0B5D,UAAUiB;oBAChD,IAAI,CAAC4D,KAAK;oBAEV,OAAOD,iBAAiB5E,UAAU6E,KAAK5D;gBACzC;YACF;QACF;QACA,OAAO,EAAE;IACX;IAEAoG,yDACErH,QAAgB,EAChBO,IAAgC;QAEhC,MAAMH,KAAKX;QACX,MAAMS,SAASV,UAAUQ;QACzB,MAAM+E,cAAqC,EAAE;QAE7C,MAAMuC,eAAe/G,KAAK+G,YAAY;QACtC,IAAIA,gBAAgBlH,GAAGmH,cAAc,CAACD,eAAe;YACnD,KAAK,MAAMrB,KAAKqB,aAAaE,QAAQ,CAAE;gBACrC,IAAI;oBAAC;oBAAoB;iBAAW,CAAClB,QAAQ,CAACL,EAAE9E,IAAI,CAACC,OAAO,KAAK;oBAC/D2D,YAAY0C,IAAI,CAAC;wBACfxF,MAAM/B;wBACNmF,UAAUjF,GAAG2G,kBAAkB,CAACC,KAAK;wBACrC1B,MAAMhG,eAAe2H,uBAAuB;wBAC5C1B,aAAa,CAAC,aAAa,EAAEU,EAAE9E,IAAI,CAACC,OAAO,GAAG,2CAA2C,CAAC;wBAC1F+D,OAAOc,EAAE9E,IAAI,CAAC+F,QAAQ;wBACtBzC,QAAQwB,EAAE9E,IAAI,CAACgG,QAAQ;oBACzB;gBACF;YACF;QACF;QAEA,OAAOpC;IACT;IAEA2C,4CACE1H,QAAgB,EAChBO,IAAgC;QAEhC,MAAMH,KAAKX;QAEX,MAAM6H,eAAe/G,KAAK+G,YAAY;QACtC,IAAIA,gBAAgBlH,GAAGmH,cAAc,CAACD,eAAe;YACnD,KAAK,MAAMrB,KAAKqB,aAAaE,QAAQ,CAAE;gBACrC,IAAIvB,EAAE9E,IAAI,CAACC,OAAO,OAAO,YAAY;oBACnC,+CAA+C;oBAC/C,MAAMuG,cAAcjI;oBACpB,IAAIiI,aAAa;wBACf,MAAMC,SAASD,YAAYE,mBAAmB,CAAC5B,EAAE9E,IAAI;wBACrD,IAAIyG,QAAQ;4BACV,MAAME,iBAAiBH,YAAYI,gBAAgB,CAACH;4BACpD,IAAIE,kBAAkBA,eAAe5G,YAAY,EAAE;gCACjD,MAAMD,cAAc6G,eAAe5G,YAAY,CAAC,EAAE;gCAClD,IAAID,eAAeb,GAAG4H,qBAAqB,CAAC/G,cAAc;oCACxD,IAAIyD,QAAQzD,cAAc;oCAE1B,MAAMgH,sBACJhH,YAAYiH,aAAa,GAAGlI,QAAQ;oCACtC,MAAMmI,aAAaF,wBAAwBjI;oCAE3C,0DAA0D;oCAC1D,MAAM6E,MAAMjB,0BACVqE,qBACAhH;oCAEF,IAAI,CAAC4D,KAAK;oCAEV,MAAME,cAAcH,iBAClBqD,qBACApD,KACA5D;oCAEF,IAAI8D,YAAYN,MAAM,EAAE;wCACtB,IAAI0D,YAAY;4CACd,OAAOpD;wCACT,OAAO;4CACL,OAAO;gDACL;oDACE9C,MAAMzC,UAAUQ;oDAChBqF,UAAUjF,GAAG2G,kBAAkB,CAACC,KAAK;oDACrC1B,MAAMhG,eAAe2H,uBAAuB;oDAC5C1B,aAAa,CAAC,0LAA0L,CAAC;oDACzMJ,OAAOc,EAAE9E,IAAI,CAAC+F,QAAQ;oDACtBzC,QAAQwB,EAAE9E,IAAI,CAACgG,QAAQ;gDACzB;6CACD;wCACH;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,EAAE;IACX;IAEAiB,2BACEpI,QAAgB,EAChBC,QAAgB,EAChBoI,SAAiB,EACjBC,aAAyC,EACzCpI,MAAc,EACdqI,WAAqC,EACrC1B,IAAkC;QAElC,MAAMtG,OAAOR,kBAAkBC,UAAUC;QACzC,IAAI,CAACM,MAAM;QACX,IAAImE,QAAQnE,OAAO;QAEnB,0DAA0D;QAC1D,MAAMsE,MAAMjB,0BAA0B5D,UAAUO;QAChD,IAAIsE,QAAQpC,WAAW;QAEvB,MAAM,EAAEjB,eAAe,EAAE,GAAGD;QAC5B,MAAMqE,SAAS3F,YAAY4E,GAAG,CAAC,EAAE,GAAG5E,WAAWA,WAAW4E,GAAG,CAAC,EAAE;QAEhE,MAAM2D,UAAUhH,gBAAgB4G,yBAAyB,CACvDpI,UACA4F,QACAyC,WACAC,eACApI,QACAqI,aACA1B;QAEF,OAAO2B;IACT;IAEAC,wBAAuBzI,QAAgB,EAAEC,QAAgB;QACvD,MAAMM,OAAOR,kBAAkBC,UAAUC;QACzC,IAAI,CAACM,MAAM;QACX,IAAImE,QAAQnE,OAAO;QAEnB,0DAA0D;QAC1D,MAAMsE,MAAMjB,0BAA0B5D,UAAUO;QAChD,IAAIsE,QAAQpC,WAAW;QAEvB,MAAM,EAAEjB,eAAe,EAAE,GAAGD;QAC5B,MAAMqE,SAAS3F,YAAY4E,GAAG,CAAC,EAAE,GAAG5E,WAAWA,WAAW4E,GAAG,CAAC,EAAE;QAChE,MAAM6D,UAAUlH,gBAAgBiH,sBAAsB,CAACzI,UAAU4F;QACjE,OAAO8C;IACT;IAEAC,2BAA0B3I,QAAgB,EAAEC,QAAgB;QAC1D,MAAMM,OAAOR,kBAAkBC,UAAUC;QACzC,IAAI,CAACM,MAAM;QACX,IAAImE,QAAQnE,OAAO;QACnB,IAAI,CAACZ,qBAAqBM,UAAUM,OAAO;QAC3C,0DAA0D;QAC1D,MAAMsE,MAAMjB,0BAA0B5D,UAAUO;QAChD,IAAIsE,QAAQpC,WAAW;QACvB,MAAM,EAAEjB,eAAe,EAAE,GAAGD;QAC5B,MAAMqE,SAAS3F,YAAY4E,GAAG,CAAC,EAAE,GAAG5E,WAAWA,WAAW4E,GAAG,CAAC,EAAE;QAEhE,MAAM+D,6BACJpH,gBAAgBmH,yBAAyB,CAAC3I,UAAU4F;QAEtD,IAAIgD,4BAA4B;YAC9B,6CAA6C;YAC7C,IAAIA,2BAA2BC,QAAQ,CAAC1D,KAAK,GAAGN,GAAG,CAAC,EAAE,EAAE;gBACtD+D,2BAA2BC,QAAQ,CAAC1D,KAAK,IAAIN,GAAG,CAAC,EAAE;YACrD;QACF;QACA,OAAO+D;IACT;AACF;AAEA,eAAepD,SAAQ"}