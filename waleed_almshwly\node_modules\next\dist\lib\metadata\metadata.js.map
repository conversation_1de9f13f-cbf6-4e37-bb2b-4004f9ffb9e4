{"version": 3, "sources": ["../../../src/lib/metadata/metadata.tsx"], "names": ["createMetadataComponents", "createMetadataContext", "urlPathname", "renderOpts", "pathname", "split", "trailingSlash", "isStandaloneMode", "nextConfigOutput", "tree", "query", "metadataContext", "getDynamicParamFromSegment", "appUsingSizeAdjustment", "errorType", "createDynamicallyTrackedSearchParams", "resolve", "metadataErrorResolving", "Promise", "res", "MetadataTree", "defaultMetadata", "createDefaultMetadata", "defaultViewport", "createDefaultViewport", "metadata", "viewport", "error", "errorMetadataItem", "errorConvention", "undefined", "searchParams", "resolvedError", "resolvedMetadata", "resolvedViewport", "resolveMetadata", "parentParams", "metadataItems", "isNotFoundError", "notFoundMetadataError", "notFoundMetadata", "notFoundViewport", "elements", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportMeta", "BasicMeta", "AlternatesMetadata", "alternates", "ItunesMeta", "itunes", "FacebookMeta", "facebook", "FormatDetectionMeta", "formatDetection", "VerificationMeta", "verification", "AppleWebAppMeta", "appleWebApp", "OpenGraphMetadata", "openGraph", "TwitterMetadata", "twitter", "AppLinksMeta", "appLinks", "IconsMetadata", "icons", "push", "meta", "name", "map", "el", "index", "React", "cloneElement", "key", "MetadataOutlet"], "mappings": ";;;;;;;;;;;;;;;IAsDgBA,wBAAwB;eAAxBA;;IAjBAC,qBAAqB;eAArBA;;;;8DA9BE;uBASX;2BAC4B;2BAK5B;uBACuB;iCACE;sBACL;iCAQpB;0BACyB;;;;;;AAGzB,SAASA,sBACdC,WAAmB,EACnBC,UAA0C;IAE1C,OAAO;QACLC,UAAUF,YAAYG,KAAK,CAAC,IAAI,CAAC,EAAE;QACnCC,eAAeH,WAAWG,aAAa;QACvCC,kBAAkBJ,WAAWK,gBAAgB,KAAK;IACpD;AACF;AAQO,SAASR,yBAAyB,EACvCS,IAAI,EACJC,KAAK,EACLC,eAAe,EACfC,0BAA0B,EAC1BC,sBAAsB,EACtBC,SAAS,EACTC,oCAAoC,EAWrC;IACC,IAAIC;IACJ,8DAA8D;IAC9D,MAAMC,yBAAyB,IAAIC,QAA2B,CAACC;QAC7DH,UAAUG;IACZ;IAEA,eAAeC;QACb,MAAMC,kBAAkBC,IAAAA,sCAAqB;QAC7C,MAAMC,kBAAkBC,IAAAA,sCAAqB;QAC7C,IAAIC,WAAyCJ;QAC7C,IAAIK,WAAyCH;QAC7C,IAAII;QACJ,MAAMC,oBAAwC;YAAC;YAAM;YAAM;SAAK;QAChE,MAAMC,kBAAkBf,cAAc,aAAagB,YAAYhB;QAC/D,MAAMiB,eAAehB,qCAAqCL;QAE1D,MAAM,CAACsB,eAAeC,kBAAkBC,iBAAiB,GACvD,MAAMC,IAAAA,gCAAe,EAAC;YACpB1B;YACA2B,cAAc,CAAC;YACfC,eAAe,EAAE;YACjBT;YACAG;YACAnB;YACAiB;YACAlB;QACF;QACF,IAAI,CAACqB,eAAe;YAClBN,WAAWQ;YACXT,WAAWQ;YACXjB,QAAQc;QACV,OAAO;YACLH,QAAQK;YACR,gGAAgG;YAChG,sGAAsG;YACtG,0FAA0F;YAC1F,+FAA+F;YAC/F,IAAI,CAAClB,aAAawB,IAAAA,yBAAe,EAACN,gBAAgB;gBAChD,MAAM,CAACO,uBAAuBC,kBAAkBC,iBAAiB,GAC/D,MAAMN,IAAAA,gCAAe,EAAC;oBACpB1B;oBACA2B,cAAc,CAAC;oBACfC,eAAe,EAAE;oBACjBT;oBACAG;oBACAnB;oBACAiB,iBAAiB;oBACjBlB;gBACF;gBACFe,WAAWe;gBACXhB,WAAWe;gBACXb,QAAQY,yBAAyBZ;YACnC;YACAX,QAAQW;QACV;QAEA,MAAMe,WAAWC,IAAAA,gBAAU,EAAC;YAC1BC,IAAAA,mBAAY,EAAC;gBAAElB,UAAUA;YAAS;YAClCmB,IAAAA,gBAAS,EAAC;gBAAEpB;YAAS;YACrBqB,IAAAA,6BAAkB,EAAC;gBAAEC,YAAYtB,SAASsB,UAAU;YAAC;YACrDC,IAAAA,iBAAU,EAAC;gBAAEC,QAAQxB,SAASwB,MAAM;YAAC;YACrCC,IAAAA,mBAAY,EAAC;gBAAEC,UAAU1B,SAAS0B,QAAQ;YAAC;YAC3CC,IAAAA,0BAAmB,EAAC;gBAAEC,iBAAiB5B,SAAS4B,eAAe;YAAC;YAChEC,IAAAA,uBAAgB,EAAC;gBAAEC,cAAc9B,SAAS8B,YAAY;YAAC;YACvDC,IAAAA,sBAAe,EAAC;gBAAEC,aAAahC,SAASgC,WAAW;YAAC;YACpDC,IAAAA,4BAAiB,EAAC;gBAAEC,WAAWlC,SAASkC,SAAS;YAAC;YAClDC,IAAAA,0BAAe,EAAC;gBAAEC,SAASpC,SAASoC,OAAO;YAAC;YAC5CC,IAAAA,uBAAY,EAAC;gBAAEC,UAAUtC,SAASsC,QAAQ;YAAC;YAC3CC,IAAAA,oBAAa,EAAC;gBAAEC,OAAOxC,SAASwC,KAAK;YAAC;SACvC;QAED,IAAIpD,wBAAwB6B,SAASwB,IAAI,eAAC,qBAACC;YAAKC,MAAK;;QAErD,qBACE;sBACG1B,SAAS2B,GAAG,CAAC,CAACC,IAAIC;gBACjB,qBAAOC,cAAK,CAACC,YAAY,CAACH,IAA0B;oBAAEI,KAAKH;gBAAM;YACnE;;IAGN;IAEA,eAAeI;QACb,MAAMhD,QAAQ,MAAMV;QACpB,IAAIU,OAAO;YACT,MAAMA;QACR;QACA,OAAO;IACT;IAEA,OAAO;QAACP;QAAcuD;KAAe;AACvC"}