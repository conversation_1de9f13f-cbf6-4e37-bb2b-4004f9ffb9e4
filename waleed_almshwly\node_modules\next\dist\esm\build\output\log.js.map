{"version": 3, "sources": ["../../../src/build/output/log.ts"], "names": ["bold", "green", "magenta", "red", "yellow", "white", "prefixes", "wait", "error", "warn", "ready", "info", "event", "trace", "LOGGING_METHOD", "log", "prefixedLog", "prefixType", "message", "undefined", "length", "shift", "consoleMethod", "prefix", "console", "bootstrap", "warnOnceMessages", "Set", "warnOnce", "has", "add", "join"], "mappings": "AAAA,SAASA,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,QAAQ,uBAAsB;AAE/E,OAAO,MAAMC,WAAW;IACtBC,MAAMF,MAAML,KAAK;IACjBQ,OAAOL,IAAIH,KAAK;IAChBS,MAAML,OAAOJ,KAAK;IAClBU,OAAO;IACPC,MAAMN,MAAML,KAAK;IACjBY,OAAOX,MAAMD,KAAK;IAClBa,OAAOX,QAAQF,KAAK;AACtB,EAAU;AAEV,MAAMc,iBAAiB;IACrBC,KAAK;IACLN,MAAM;IACND,OAAO;AACT;AAEA,SAASQ,YAAYC,UAAiC,EAAE,GAAGC,OAAc;IACvE,IAAI,AAACA,CAAAA,OAAO,CAAC,EAAE,KAAK,MAAMA,OAAO,CAAC,EAAE,KAAKC,SAAQ,KAAMD,QAAQE,MAAM,KAAK,GAAG;QAC3EF,QAAQG,KAAK;IACf;IAEA,MAAMC,gBACJL,cAAcH,iBACVA,cAAc,CAACG,WAA0C,GACzD;IAEN,MAAMM,SAASjB,QAAQ,CAACW,WAAW;IACnC,+DAA+D;IAC/D,IAAIC,QAAQE,MAAM,KAAK,GAAG;QACxBI,OAAO,CAACF,cAAc,CAAC;IACzB,OAAO;QACLE,OAAO,CAACF,cAAc,CAAC,MAAMC,WAAWL;IAC1C;AACF;AAEA,OAAO,SAASO,UAAU,GAAGP,OAAc;IACzCM,QAAQT,GAAG,CAAC,QAAQG;AACtB;AAEA,OAAO,SAASX,KAAK,GAAGW,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEA,OAAO,SAASV,MAAM,GAAGU,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,OAAO,SAAST,KAAK,GAAGS,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEA,OAAO,SAASR,MAAM,GAAGQ,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,OAAO,SAASP,KAAK,GAAGO,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEA,OAAO,SAASN,MAAM,GAAGM,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,OAAO,SAASL,MAAM,GAAGK,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,MAAMQ,mBAAmB,IAAIC;AAC7B,OAAO,SAASC,SAAS,GAAGV,OAAc;IACxC,IAAI,CAACQ,iBAAiBG,GAAG,CAACX,OAAO,CAAC,EAAE,GAAG;QACrCQ,iBAAiBI,GAAG,CAACZ,QAAQa,IAAI,CAAC;QAElCtB,QAAQS;IACV;AACF"}