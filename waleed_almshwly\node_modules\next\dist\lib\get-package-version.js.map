{"version": 3, "sources": ["../../src/lib/get-package-version.ts"], "names": ["getDependencies", "getPackageVersion", "cachedDeps", "cwd", "configurationPath", "findUp", "dependencies", "devDependencies", "content", "fs", "readFile", "packageJson", "JSON5", "parse", "name", "cwd2", "endsWith", "path", "posix", "sep", "win32", "targetPath", "require", "resolve", "paths", "targetContent", "version"], "mappings": ";;;;;;;;;;;;;;;IAYgBA,eAAe;eAAfA;;IAyBMC,iBAAiB;eAAjBA;;;oBArCS;+DACZ;8DACD;8DACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB,IAAIC;AAEG,SAASF,gBAAgB,EAC9BG,GAAG,EAGJ;IACC,IAAID,YAAY;QACd,OAAOA;IACT;IAEA,OAAQA,aAAa,AAAC,CAAA;QACpB,MAAME,oBAAwC,MAAMC,IAAAA,eAAM,EAAC,gBAAgB;YACzEF;QACF;QACA,IAAI,CAACC,mBAAmB;YACtB,OAAO;gBAAEE,cAAc,CAAC;gBAAGC,iBAAiB,CAAC;YAAE;QACjD;QAEA,MAAMC,UAAU,MAAMC,YAAE,CAACC,QAAQ,CAACN,mBAAmB;QACrD,MAAMO,cAAmBC,cAAK,CAACC,KAAK,CAACL;QAErC,MAAM,EAAEF,eAAe,CAAC,CAAC,EAAEC,kBAAkB,CAAC,CAAC,EAAE,GAAGI,eAAe,CAAC;QACpE,OAAO;YAAEL;YAAcC;QAAgB;IACzC,CAAA;AACF;AAEO,eAAeN,kBAAkB,EACtCE,GAAG,EACHW,IAAI,EAIL;IACC,MAAM,EAAER,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAMP,gBAAgB;QAAEG;IAAI;IACtE,IAAI,CAAEG,CAAAA,YAAY,CAACQ,KAAK,IAAIP,eAAe,CAACO,KAAK,AAAD,GAAI;QAClD,OAAO;IACT;IAEA,MAAMC,OACJZ,IAAIa,QAAQ,CAACC,MAAKC,KAAK,CAACC,GAAG,KAAKhB,IAAIa,QAAQ,CAACC,MAAKG,KAAK,CAACD,GAAG,IACvDhB,MACA,CAAC,EAAEA,IAAI,CAAC,CAAC;IAEf,IAAI;QACF,MAAMkB,aAAaC,QAAQC,OAAO,CAAC,CAAC,EAAET,KAAK,aAAa,CAAC,EAAE;YACzDU,OAAO;gBAACT;aAAK;QACf;QACA,MAAMU,gBAAgB,MAAMhB,YAAE,CAACC,QAAQ,CAACW,YAAY;QACpD,OAAOT,cAAK,CAACC,KAAK,CAACY,eAAeC,OAAO,IAAI;IAC/C,EAAE,OAAM;QACN,OAAO;IACT;AACF"}