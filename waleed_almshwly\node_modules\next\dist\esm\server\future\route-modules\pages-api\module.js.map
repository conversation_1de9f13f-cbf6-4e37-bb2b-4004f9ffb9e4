{"version": 3, "sources": ["../../../../../src/server/future/route-modules/pages-api/module.ts"], "names": ["wrapApiHandler", "RouteModule", "apiResolver", "PagesAPIRouteModule", "constructor", "options", "userland", "default", "Error", "definition", "page", "apiResolverWrapped", "render", "req", "res", "context", "query", "previewProps", "revalidate", "trustHostHeader", "allowedRevalidateHeaderKeys", "hostname", "multiZoneDraftMode", "minimalMode", "dev"], "mappings": "AAIA,SAASA,cAAc,QAAgC,qBAAoB;AAG3E,SAASC,WAAW,QAAuC,kBAAiB;AAC5E,SAASC,WAAW,QAAQ,uCAAsC;AAmGlE,OAAO,MAAMC,4BAA4BF;IAMvCG,YAAYC,OAAmC,CAAE;QAC/C,KAAK,CAACA;QAEN,IAAI,OAAOA,QAAQC,QAAQ,CAACC,OAAO,KAAK,YAAY;YAClD,MAAM,IAAIC,MACR,CAAC,KAAK,EAAEH,QAAQI,UAAU,CAACC,IAAI,CAAC,oCAAoC,CAAC;QAEzE;QAEA,IAAI,CAACC,kBAAkB,GAAGX,eACxBK,QAAQI,UAAU,CAACC,IAAI,EACvBR;IAEJ;IAEA;;;;;GAKC,GACD,MAAaU,OACXC,GAAoB,EACpBC,GAAmB,EACnBC,OAAoC,EACrB;QACf,MAAM,EAAEJ,kBAAkB,EAAE,GAAG,IAAI;QACnC,MAAMA,mBACJE,KACAC,KACAC,QAAQC,KAAK,EACb,IAAI,CAACV,QAAQ,EACb;YACE,GAAGS,QAAQE,YAAY;YACvBC,YAAYH,QAAQG,UAAU;YAC9BC,iBAAiBJ,QAAQI,eAAe;YACxCC,6BAA6BL,QAAQK,2BAA2B;YAChEC,UAAUN,QAAQM,QAAQ;YAC1BC,oBAAoBP,QAAQO,kBAAkB;QAChD,GACAP,QAAQQ,WAAW,EACnBR,QAAQS,GAAG,EACXT,QAAQL,IAAI;IAEhB;AACF;AAEA,eAAeP,oBAAmB"}