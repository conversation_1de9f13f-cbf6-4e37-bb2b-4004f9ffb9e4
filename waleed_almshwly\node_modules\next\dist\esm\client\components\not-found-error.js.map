{"version": 3, "sources": ["../../../src/client/components/not-found-error.tsx"], "names": ["React", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "h1", "margin", "padding", "fontSize", "fontWeight", "verticalAlign", "lineHeight", "h2", "NotFound", "title", "div", "style", "dangerouslySetInnerHTML", "__html", "className"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AAEzB,MAAMC,SAA8C;IAClDC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IAEAC,MAAM;QACJJ,SAAS;IACX;IAEAK,IAAI;QACFL,SAAS;QACTM,QAAQ;QACRC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,eAAe;QACfC,YAAY;IACd;IAEAC,IAAI;QACFJ,UAAU;QACVC,YAAY;QACZE,YAAY;QACZL,QAAQ;IACV;AACF;AAEA,eAAe,SAASO;IACtB,qBACE;;0BAEE,KAACC;0BAAM;;0BAEP,KAACC;gBAAIC,OAAOrB,OAAOC,KAAK;0BACtB,cAAA,MAACmB;;sCACC,KAACC;4BACCC,yBAAyB;gCACvB;;;;;;;;;;;;cAYA,GACAC,QAAS;4BACX;;sCAEF,KAACb;4BAAGc,WAAU;4BAAgBH,OAAOrB,OAAOU,EAAE;sCAAE;;sCAGhD,KAACU;4BAAIC,OAAOrB,OAAOS,IAAI;sCACrB,cAAA,KAACQ;gCAAGI,OAAOrB,OAAOiB,EAAE;0CAAE;;;;;;;;AAMlC"}