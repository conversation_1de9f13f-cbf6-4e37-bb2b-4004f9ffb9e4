{"version": 3, "sources": ["../../src/client/head-manager.ts"], "names": ["DOMAttributeNames", "acceptCharset", "className", "htmlFor", "httpEquiv", "noModule", "reactElementToDOM", "type", "props", "el", "document", "createElement", "p", "hasOwnProperty", "undefined", "attr", "toLowerCase", "setAttribute", "children", "dangerouslySetInnerHTML", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "isEqualNode", "oldTag", "newTag", "HTMLElement", "nonce", "getAttribute", "cloneTag", "cloneNode", "updateElements", "process", "env", "__NEXT_STRICT_NEXT_HEAD", "components", "headEl", "querySelector", "headMetaTags", "querySelectorAll", "oldTags", "metaCharset", "push", "i", "length", "headTag", "metaTag", "nextS<PERSON>ling", "tagName", "newTags", "map", "filter", "k", "len", "splice", "for<PERSON>ach", "t", "previousSibling", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "meta", "name", "content", "append<PERSON><PERSON><PERSON>", "getElementsByTagName", "headCountEl", "NODE_ENV", "console", "error", "headCount", "Number", "j", "previousElementSibling", "insertBefore", "toString", "initHeadManager", "mountedInstances", "Set", "updateHead", "head", "tags", "h", "href", "titleComponent", "title"], "mappings": "AAAA,OAAO,MAAMA,oBAA4C;IACvDC,eAAe;IACfC,WAAW;IACXC,SAAS;IACTC,WAAW;IACXC,UAAU;AACZ,EAAC;AAED,SAASC,kBAAkB,KAA4B;IAA5B,IAAA,EAAEC,IAAI,EAAEC,KAAK,EAAe,GAA5B;IACzB,MAAMC,KAAkBC,SAASC,aAAa,CAACJ;IAC/C,IAAK,MAAMK,KAAKJ,MAAO;QACrB,IAAI,CAACA,MAAMK,cAAc,CAACD,IAAI;QAC9B,IAAIA,MAAM,cAAcA,MAAM,2BAA2B;QAEzD,6CAA6C;QAC7C,IAAIJ,KAAK,CAACI,EAAE,KAAKE,WAAW;QAE5B,MAAMC,OAAOf,iBAAiB,CAACY,EAAE,IAAIA,EAAEI,WAAW;QAClD,IACET,SAAS,YACRQ,CAAAA,SAAS,WAAWA,SAAS,WAAWA,SAAS,UAAS,GAC3D;YACEN,EAAwB,CAACM,KAAK,GAAG,CAAC,CAACP,KAAK,CAACI,EAAE;QAC/C,OAAO;YACLH,GAAGQ,YAAY,CAACF,MAAMP,KAAK,CAACI,EAAE;QAChC;IACF;IAEA,MAAM,EAAEM,QAAQ,EAAEC,uBAAuB,EAAE,GAAGX;IAC9C,IAAIW,yBAAyB;QAC3BV,GAAGW,SAAS,GAAGD,wBAAwBE,MAAM,IAAI;IACnD,OAAO,IAAIH,UAAU;QACnBT,GAAGa,WAAW,GACZ,OAAOJ,aAAa,WAChBA,WACAK,MAAMC,OAAO,CAACN,YACdA,SAASO,IAAI,CAAC,MACd;IACR;IACA,OAAOhB;AACT;AAEA;;;;;;;;;;;;;CAaC,GACD,OAAO,SAASiB,YAAYC,MAAe,EAAEC,MAAe;IAC1D,IAAID,kBAAkBE,eAAeD,kBAAkBC,aAAa;QAClE,MAAMC,QAAQF,OAAOG,YAAY,CAAC;QAClC,8FAA8F;QAC9F,4FAA4F;QAC5F,IAAID,SAAS,CAACH,OAAOI,YAAY,CAAC,UAAU;YAC1C,MAAMC,WAAWJ,OAAOK,SAAS,CAAC;YAClCD,SAASf,YAAY,CAAC,SAAS;YAC/Be,SAASF,KAAK,GAAGA;YACjB,OAAOA,UAAUH,OAAOG,KAAK,IAAIH,OAAOD,WAAW,CAACM;QACtD;IACF;IAEA,OAAOL,OAAOD,WAAW,CAACE;AAC5B;AAEA,IAAIM;AAEJ,IAAIC,QAAQC,GAAG,CAACC,uBAAuB,EAAE;IACvCH,iBAAiB,CAAC3B,MAAc+B;QAC9B,MAAMC,SAAS7B,SAAS8B,aAAa,CAAC;QACtC,IAAI,CAACD,QAAQ;QAEb,MAAME,eAAeF,OAAOG,gBAAgB,CAAC,6BAA6B,EAAE;QAC5E,MAAMC,UAAqB,EAAE;QAE7B,IAAIpC,SAAS,QAAQ;YACnB,MAAMqC,cAAcL,OAAOC,aAAa,CAAC;YACzC,IAAII,aAAa;gBACfD,QAAQE,IAAI,CAACD;YACf;QACF;QAEA,IAAK,IAAIE,IAAI,GAAGA,IAAIL,aAAaM,MAAM,EAAED,IAAK;gBAIxCE;YAHJ,MAAMC,UAAUR,YAAY,CAACK,EAAE;YAC/B,MAAME,UAAUC,QAAQC,WAAW;YAEnC,IAAIF,CAAAA,4BAAAA,mBAAAA,QAASG,OAAO,qBAAhBH,iBAAkBhC,WAAW,QAAOT,MAAM;gBAC5CoC,QAAQE,IAAI,CAACG;YACf;QACF;QACA,MAAMI,UAAU,AAACd,WAAWe,GAAG,CAAC/C,mBAAqCgD,MAAM,CACzE,CAAC1B;YACC,IAAK,IAAI2B,IAAI,GAAGC,MAAMb,QAAQI,MAAM,EAAEQ,IAAIC,KAAKD,IAAK;gBAClD,MAAM5B,SAASgB,OAAO,CAACY,EAAE;gBACzB,IAAI7B,YAAYC,QAAQC,SAAS;oBAC/Be,QAAQc,MAAM,CAACF,GAAG;oBAClB,OAAO;gBACT;YACF;YACA,OAAO;QACT;QAGFZ,QAAQe,OAAO,CAAC,CAACC;gBAKfA;YAJA,MAAMV,UAAUU,EAAEC,eAAe;YACjC,IAAIX,WAAWA,QAAQlB,YAAY,CAAC,YAAY,aAAa;oBAC3D4B;iBAAAA,iBAAAA,EAAEE,UAAU,qBAAZF,eAAcG,WAAW,CAACb;YAC5B;aACAU,gBAAAA,EAAEE,UAAU,qBAAZF,cAAcG,WAAW,CAACH;QAC5B;QACAP,QAAQM,OAAO,CAAC,CAACC;gBAMTA;YALN,MAAMI,OAAOrD,SAASC,aAAa,CAAC;YACpCoD,KAAKC,IAAI,GAAG;YACZD,KAAKE,OAAO,GAAG;YAEf,sDAAsD;YACtD,IAAI,CAAEN,CAAAA,EAAAA,aAAAA,EAAER,OAAO,qBAATQ,WAAW3C,WAAW,QAAO,UAAU2C,EAAE5B,YAAY,CAAC,UAAS,GAAI;gBACvEQ,OAAO2B,WAAW,CAACH;YACrB;YACAxB,OAAO2B,WAAW,CAACP;QACrB;IACF;AACF,OAAO;IACLzB,iBAAiB,CAAC3B,MAAc+B;QAC9B,MAAMC,SAAS7B,SAASyD,oBAAoB,CAAC,OAAO,CAAC,EAAE;QACvD,MAAMC,cAA+B7B,OAAOC,aAAa,CACvD;QAEF,IAAIL,QAAQC,GAAG,CAACiC,QAAQ,KAAK,cAAc;YACzC,IAAI,CAACD,aAAa;gBAChBE,QAAQC,KAAK,CACX;gBAEF;YACF;QACF;QAEA,MAAMC,YAAYC,OAAOL,YAAYH,OAAO;QAC5C,MAAMtB,UAAqB,EAAE;QAE7B,IACE,IAAIG,IAAI,GAAG4B,IAAIN,YAAYO,sBAAsB,EACjD7B,IAAI0B,WACJ1B,KAAK4B,IAAIA,CAAAA,qBAAAA,EAAGC,sBAAsB,KAAI,KACtC;gBACID;YAAJ,IAAIA,CAAAA,sBAAAA,aAAAA,EAAGvB,OAAO,qBAAVuB,WAAY1D,WAAW,QAAOT,MAAM;gBACtCoC,QAAQE,IAAI,CAAC6B;YACf;QACF;QACA,MAAMtB,UAAU,AAACd,WAAWe,GAAG,CAAC/C,mBAAqCgD,MAAM,CACzE,CAAC1B;YACC,IAAK,IAAI2B,IAAI,GAAGC,MAAMb,QAAQI,MAAM,EAAEQ,IAAIC,KAAKD,IAAK;gBAClD,MAAM5B,SAASgB,OAAO,CAACY,EAAE;gBACzB,IAAI7B,YAAYC,QAAQC,SAAS;oBAC/Be,QAAQc,MAAM,CAACF,GAAG;oBAClB,OAAO;gBACT;YACF;YACA,OAAO;QACT;QAGFZ,QAAQe,OAAO,CAAC,CAACC;gBAAMA;oBAAAA,gBAAAA,EAAEE,UAAU,qBAAZF,cAAcG,WAAW,CAACH;;QACjDP,QAAQM,OAAO,CAAC,CAACC,IAAMpB,OAAOqC,YAAY,CAACjB,GAAGS;QAC9CA,YAAYH,OAAO,GAAG,AACpBO,CAAAA,YACA7B,QAAQI,MAAM,GACdK,QAAQL,MAAM,AAAD,EACb8B,QAAQ;IACZ;AACF;AAEA,eAAe,SAASC;IAItB,OAAO;QACLC,kBAAkB,IAAIC;QACtBC,YAAY,CAACC;YACX,MAAMC,OAAsC,CAAC;YAE7CD,KAAKxB,OAAO,CAAC,CAAC0B;gBACZ,IACE,sDAAsD;gBACtD,oEAAoE;gBACpEA,EAAE7E,IAAI,KAAK,UACX6E,EAAE5E,KAAK,CAAC,uBAAuB,EAC/B;oBACA,IACEE,SAAS8B,aAAa,CAAC,AAAC,sBAAmB4C,EAAE5E,KAAK,CAAC,YAAY,GAAC,OAChE;wBACA;oBACF,OAAO;wBACL4E,EAAE5E,KAAK,CAAC6E,IAAI,GAAGD,EAAE5E,KAAK,CAAC,YAAY;wBACnC4E,EAAE5E,KAAK,CAAC,YAAY,GAAGM;oBACzB;gBACF;gBAEA,MAAMwB,aAAa6C,IAAI,CAACC,EAAE7E,IAAI,CAAC,IAAI,EAAE;gBACrC+B,WAAWO,IAAI,CAACuC;gBAChBD,IAAI,CAACC,EAAE7E,IAAI,CAAC,GAAG+B;YACjB;YAEA,MAAMgD,iBAAiBH,KAAKI,KAAK,GAAGJ,KAAKI,KAAK,CAAC,EAAE,GAAG;YACpD,IAAIA,QAAQ;YACZ,IAAID,gBAAgB;gBAClB,MAAM,EAAEpE,QAAQ,EAAE,GAAGoE,eAAe9E,KAAK;gBACzC+E,QACE,OAAOrE,aAAa,WAChBA,WACAK,MAAMC,OAAO,CAACN,YACdA,SAASO,IAAI,CAAC,MACd;YACR;YACA,IAAI8D,UAAU7E,SAAS6E,KAAK,EAAE7E,SAAS6E,KAAK,GAAGA;YAC9C;gBAAC;gBAAQ;gBAAQ;gBAAQ;gBAAS;aAAS,CAAC7B,OAAO,CAAC,CAACnD;gBACpD2B,eAAe3B,MAAM4E,IAAI,CAAC5E,KAAK,IAAI,EAAE;YACvC;QACF;IACF;AACF"}