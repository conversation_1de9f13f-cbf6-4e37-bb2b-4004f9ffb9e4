"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/index.css":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/index.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\r\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');\\r\\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');\\r\\n\\r\\n*, ::before, ::after{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\r\\n\\r\\n::backdrop{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\r\\n\\r\\n/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*/\\r\\n\\r\\n/*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\r\\n\\r\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\r\\n\\r\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\r\\n\\r\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\r\\n\\r\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\r\\n\\r\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\r\\n\\r\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\r\\n\\r\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\r\\n\\r\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\r\\n\\r\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\r\\n\\r\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\r\\n\\r\\n/*\\nRemove the default font size and weight for headings.\\n*/\\r\\n\\r\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\r\\n\\r\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\r\\n\\r\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\r\\n\\r\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\r\\n\\r\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\r\\n\\r\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\r\\n\\r\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\r\\n\\r\\n/*\\nAdd the correct font size in all browsers.\\n*/\\r\\n\\r\\nsmall {\\n  font-size: 80%;\\n}\\r\\n\\r\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\r\\n\\r\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\r\\n\\r\\nsub {\\n  bottom: -0.25em;\\n}\\r\\n\\r\\nsup {\\n  top: -0.5em;\\n}\\r\\n\\r\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\r\\n\\r\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\r\\n\\r\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\r\\n\\r\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\r\\n\\r\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\r\\n\\r\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\r\\n\\r\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\r\\n\\r\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\r\\n\\r\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\r\\n\\r\\n:-moz-focusring {\\n  outline: auto;\\n}\\r\\n\\r\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\r\\n\\r\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\r\\n\\r\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\r\\n\\r\\nprogress {\\n  vertical-align: baseline;\\n}\\r\\n\\r\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\r\\n\\r\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\r\\n\\r\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\r\\n\\r\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\r\\n\\r\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\r\\n\\r\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\r\\n\\r\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\r\\n\\r\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\r\\n\\r\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\r\\n\\r\\nsummary {\\n  display: list-item;\\n}\\r\\n\\r\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\r\\n\\r\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\r\\n\\r\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\r\\n\\r\\nlegend {\\n  padding: 0;\\n}\\r\\n\\r\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\r\\n\\r\\n/*\\nReset default styling for dialogs.\\n*/\\r\\n\\r\\ndialog {\\n  padding: 0;\\n}\\r\\n\\r\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\r\\n\\r\\ntextarea {\\n  resize: vertical;\\n}\\r\\n\\r\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\r\\n\\r\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\r\\n\\r\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\r\\n\\r\\n/*\\nSet the default cursor for buttons.\\n*/\\r\\n\\r\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\r\\n\\r\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\r\\n\\r\\n:disabled {\\n  cursor: default;\\n}\\r\\n\\r\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\r\\n\\r\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\r\\n\\r\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\r\\n\\r\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\r\\n\\r\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\r\\n\\r\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\r\\n\\r\\n:root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --primary: 222.2 47.4% 11.2%;\\r\\n    --primary-foreground: 210 40% 98%;\\r\\n\\r\\n    --secondary: 210 40% 96.1%;\\r\\n    --secondary-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --muted: 210 40% 96.1%;\\r\\n    --muted-foreground: 215.4 16.3% 46.9%;\\r\\n\\r\\n    --accent: 210 40% 96.1%;\\r\\n    --accent-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 210 40% 98%;\\r\\n\\r\\n    --border: 214.3 31.8% 91.4%;\\r\\n    --input: 214.3 31.8% 91.4%;\\r\\n    --ring: 222.2 84% 4.9%;\\r\\n\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    --sidebar-background: 0 0% 98%;\\r\\n    --sidebar-foreground: 240 5.3% 26.1%;\\r\\n    --sidebar-primary: 240 5.9% 10%;\\r\\n    --sidebar-primary-foreground: 0 0% 98%;\\r\\n    --sidebar-accent: 240 4.8% 95.9%;\\r\\n    --sidebar-accent-foreground: 240 5.9% 10%;\\r\\n    --sidebar-border: 220 13% 91%;\\r\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\r\\n  }\\r\\n\\r\\n.dark {\\r\\n    --background: 222.2 84% 4.9%;\\r\\n    --foreground: 210 40% 98%;\\r\\n\\r\\n    --card: 222.2 84% 4.9%;\\r\\n    --card-foreground: 210 40% 98%;\\r\\n\\r\\n    --popover: 222.2 84% 4.9%;\\r\\n    --popover-foreground: 210 40% 98%;\\r\\n\\r\\n    --primary: 210 40% 98%;\\r\\n    --primary-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --secondary: 217.2 32.6% 17.5%;\\r\\n    --secondary-foreground: 210 40% 98%;\\r\\n\\r\\n    --muted: 217.2 32.6% 17.5%;\\r\\n    --muted-foreground: 215 20.2% 65.1%;\\r\\n\\r\\n    --accent: 217.2 32.6% 17.5%;\\r\\n    --accent-foreground: 210 40% 98%;\\r\\n\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 210 40% 98%;\\r\\n\\r\\n    --border: 217.2 32.6% 17.5%;\\r\\n    --input: 217.2 32.6% 17.5%;\\r\\n    --ring: 212.7 26.8% 83.9%;\\r\\n    --sidebar-background: 240 5.9% 10%;\\r\\n    --sidebar-foreground: 240 4.8% 95.9%;\\r\\n    --sidebar-primary: 224.3 76.3% 48%;\\r\\n    --sidebar-primary-foreground: 0 0% 100%;\\r\\n    --sidebar-accent: 240 3.7% 15.9%;\\r\\n    --sidebar-accent-foreground: 240 4.8% 95.9%;\\r\\n    --sidebar-border: 240 3.7% 15.9%;\\r\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\r\\n  }\\r\\n\\r\\n*{\\n  border-color: hsl(var(--border));\\n}\\r\\n\\r\\nbody{\\n  background-color: hsl(var(--background));\\n  color: hsl(var(--foreground));\\n}\\r\\n.sr-only{\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\r\\n.pointer-events-none{\\n  pointer-events: none;\\n}\\r\\n.pointer-events-auto{\\n  pointer-events: auto;\\n}\\r\\n.visible{\\n  visibility: visible;\\n}\\r\\n.invisible{\\n  visibility: hidden;\\n}\\r\\n.fixed{\\n  position: fixed;\\n}\\r\\n.absolute{\\n  position: absolute;\\n}\\r\\n.relative{\\n  position: relative;\\n}\\r\\n.inset-0{\\n  inset: 0px;\\n}\\r\\n.inset-x-0{\\n  left: 0px;\\n  right: 0px;\\n}\\r\\n.inset-y-0{\\n  top: 0px;\\n  bottom: 0px;\\n}\\r\\n.-bottom-12{\\n  bottom: -3rem;\\n}\\r\\n.-left-12{\\n  left: -3rem;\\n}\\r\\n.-right-12{\\n  right: -3rem;\\n}\\r\\n.-top-12{\\n  top: -3rem;\\n}\\r\\n.bottom-0{\\n  bottom: 0px;\\n}\\r\\n.bottom-8{\\n  bottom: 2rem;\\n}\\r\\n.left-0{\\n  left: 0px;\\n}\\r\\n.left-1{\\n  left: 0.25rem;\\n}\\r\\n.left-1\\\\/2{\\n  left: 50%;\\n}\\r\\n.left-2{\\n  left: 0.5rem;\\n}\\r\\n.left-\\\\[50\\\\%\\\\]{\\n  left: 50%;\\n}\\r\\n.right-0{\\n  right: 0px;\\n}\\r\\n.right-1{\\n  right: 0.25rem;\\n}\\r\\n.right-2{\\n  right: 0.5rem;\\n}\\r\\n.right-3{\\n  right: 0.75rem;\\n}\\r\\n.right-4{\\n  right: 1rem;\\n}\\r\\n.right-8{\\n  right: 2rem;\\n}\\r\\n.top-0{\\n  top: 0px;\\n}\\r\\n.top-1\\\\.5{\\n  top: 0.375rem;\\n}\\r\\n.top-1\\\\/2{\\n  top: 50%;\\n}\\r\\n.top-2{\\n  top: 0.5rem;\\n}\\r\\n.top-3{\\n  top: 0.75rem;\\n}\\r\\n.top-3\\\\.5{\\n  top: 0.875rem;\\n}\\r\\n.top-4{\\n  top: 1rem;\\n}\\r\\n.top-\\\\[1px\\\\]{\\n  top: 1px;\\n}\\r\\n.top-\\\\[50\\\\%\\\\]{\\n  top: 50%;\\n}\\r\\n.top-\\\\[60\\\\%\\\\]{\\n  top: 60%;\\n}\\r\\n.top-full{\\n  top: 100%;\\n}\\r\\n.z-10{\\n  z-index: 10;\\n}\\r\\n.z-20{\\n  z-index: 20;\\n}\\r\\n.z-50{\\n  z-index: 50;\\n}\\r\\n.z-\\\\[100\\\\]{\\n  z-index: 100;\\n}\\r\\n.z-\\\\[1\\\\]{\\n  z-index: 1;\\n}\\r\\n.float-right{\\n  float: right;\\n}\\r\\n.float-left{\\n  float: left;\\n}\\r\\n.-mx-1{\\n  margin-left: -0.25rem;\\n  margin-right: -0.25rem;\\n}\\r\\n.mx-2{\\n  margin-left: 0.5rem;\\n  margin-right: 0.5rem;\\n}\\r\\n.mx-3\\\\.5{\\n  margin-left: 0.875rem;\\n  margin-right: 0.875rem;\\n}\\r\\n.mx-auto{\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\r\\n.my-0\\\\.5{\\n  margin-top: 0.125rem;\\n  margin-bottom: 0.125rem;\\n}\\r\\n.my-1{\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\r\\n.-ml-4{\\n  margin-left: -1rem;\\n}\\r\\n.-mt-4{\\n  margin-top: -1rem;\\n}\\r\\n.mb-1{\\n  margin-bottom: 0.25rem;\\n}\\r\\n.mb-12{\\n  margin-bottom: 3rem;\\n}\\r\\n.mb-16{\\n  margin-bottom: 4rem;\\n}\\r\\n.mb-2{\\n  margin-bottom: 0.5rem;\\n}\\r\\n.mb-3{\\n  margin-bottom: 0.75rem;\\n}\\r\\n.mb-4{\\n  margin-bottom: 1rem;\\n}\\r\\n.mb-6{\\n  margin-bottom: 1.5rem;\\n}\\r\\n.mb-8{\\n  margin-bottom: 2rem;\\n}\\r\\n.ml-1{\\n  margin-left: 0.25rem;\\n}\\r\\n.ml-2{\\n  margin-left: 0.5rem;\\n}\\r\\n.ml-auto{\\n  margin-left: auto;\\n}\\r\\n.mr-2{\\n  margin-right: 0.5rem;\\n}\\r\\n.mr-3{\\n  margin-right: 0.75rem;\\n}\\r\\n.mt-1\\\\.5{\\n  margin-top: 0.375rem;\\n}\\r\\n.mt-12{\\n  margin-top: 3rem;\\n}\\r\\n.mt-2{\\n  margin-top: 0.5rem;\\n}\\r\\n.mt-2\\\\.5{\\n  margin-top: 0.625rem;\\n}\\r\\n.mt-24{\\n  margin-top: 6rem;\\n}\\r\\n.mt-4{\\n  margin-top: 1rem;\\n}\\r\\n.mt-6{\\n  margin-top: 1.5rem;\\n}\\r\\n.mt-auto{\\n  margin-top: auto;\\n}\\r\\n.line-clamp-3{\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 3;\\n}\\r\\n.block{\\n  display: block;\\n}\\r\\n.inline-block{\\n  display: inline-block;\\n}\\r\\n.flex{\\n  display: flex;\\n}\\r\\n.inline-flex{\\n  display: inline-flex;\\n}\\r\\n.table{\\n  display: table;\\n}\\r\\n.grid{\\n  display: grid;\\n}\\r\\n.hidden{\\n  display: none;\\n}\\r\\n.aspect-square{\\n  aspect-ratio: 1 / 1;\\n}\\r\\n.aspect-video{\\n  aspect-ratio: 16 / 9;\\n}\\r\\n.size-4{\\n  width: 1rem;\\n  height: 1rem;\\n}\\r\\n.h-1{\\n  height: 0.25rem;\\n}\\r\\n.h-1\\\\.5{\\n  height: 0.375rem;\\n}\\r\\n.h-10{\\n  height: 2.5rem;\\n}\\r\\n.h-11{\\n  height: 2.75rem;\\n}\\r\\n.h-12{\\n  height: 3rem;\\n}\\r\\n.h-14{\\n  height: 3.5rem;\\n}\\r\\n.h-2{\\n  height: 0.5rem;\\n}\\r\\n.h-2\\\\.5{\\n  height: 0.625rem;\\n}\\r\\n.h-24{\\n  height: 6rem;\\n}\\r\\n.h-3{\\n  height: 0.75rem;\\n}\\r\\n.h-3\\\\.5{\\n  height: 0.875rem;\\n}\\r\\n.h-4{\\n  height: 1rem;\\n}\\r\\n.h-5{\\n  height: 1.25rem;\\n}\\r\\n.h-6{\\n  height: 1.5rem;\\n}\\r\\n.h-7{\\n  height: 1.75rem;\\n}\\r\\n.h-8{\\n  height: 2rem;\\n}\\r\\n.h-9{\\n  height: 2.25rem;\\n}\\r\\n.h-96{\\n  height: 24rem;\\n}\\r\\n.h-\\\\[1px\\\\]{\\n  height: 1px;\\n}\\r\\n.h-\\\\[var\\\\(--radix-navigation-menu-viewport-height\\\\)\\\\]{\\n  height: var(--radix-navigation-menu-viewport-height);\\n}\\r\\n.h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\]{\\n  height: var(--radix-select-trigger-height);\\n}\\r\\n.h-auto{\\n  height: auto;\\n}\\r\\n.h-full{\\n  height: 100%;\\n}\\r\\n.h-px{\\n  height: 1px;\\n}\\r\\n.h-svh{\\n  height: 100svh;\\n}\\r\\n.max-h-96{\\n  max-height: 24rem;\\n}\\r\\n.max-h-\\\\[300px\\\\]{\\n  max-height: 300px;\\n}\\r\\n.max-h-screen{\\n  max-height: 100vh;\\n}\\r\\n.min-h-0{\\n  min-height: 0px;\\n}\\r\\n.min-h-\\\\[80px\\\\]{\\n  min-height: 80px;\\n}\\r\\n.min-h-screen{\\n  min-height: 100vh;\\n}\\r\\n.min-h-svh{\\n  min-height: 100svh;\\n}\\r\\n.w-0{\\n  width: 0px;\\n}\\r\\n.w-1{\\n  width: 0.25rem;\\n}\\r\\n.w-10{\\n  width: 2.5rem;\\n}\\r\\n.w-11{\\n  width: 2.75rem;\\n}\\r\\n.w-12{\\n  width: 3rem;\\n}\\r\\n.w-14{\\n  width: 3.5rem;\\n}\\r\\n.w-2{\\n  width: 0.5rem;\\n}\\r\\n.w-2\\\\.5{\\n  width: 0.625rem;\\n}\\r\\n.w-24{\\n  width: 6rem;\\n}\\r\\n.w-3{\\n  width: 0.75rem;\\n}\\r\\n.w-3\\\\.5{\\n  width: 0.875rem;\\n}\\r\\n.w-3\\\\/4{\\n  width: 75%;\\n}\\r\\n.w-4{\\n  width: 1rem;\\n}\\r\\n.w-5{\\n  width: 1.25rem;\\n}\\r\\n.w-6{\\n  width: 1.5rem;\\n}\\r\\n.w-64{\\n  width: 16rem;\\n}\\r\\n.w-7{\\n  width: 1.75rem;\\n}\\r\\n.w-72{\\n  width: 18rem;\\n}\\r\\n.w-8{\\n  width: 2rem;\\n}\\r\\n.w-9{\\n  width: 2.25rem;\\n}\\r\\n.w-96{\\n  width: 24rem;\\n}\\r\\n.w-\\\\[--sidebar-width\\\\]{\\n  width: var(--sidebar-width);\\n}\\r\\n.w-\\\\[100px\\\\]{\\n  width: 100px;\\n}\\r\\n.w-\\\\[1px\\\\]{\\n  width: 1px;\\n}\\r\\n.w-auto{\\n  width: auto;\\n}\\r\\n.w-full{\\n  width: 100%;\\n}\\r\\n.w-max{\\n  width: -moz-max-content;\\n  width: max-content;\\n}\\r\\n.w-px{\\n  width: 1px;\\n}\\r\\n.min-w-0{\\n  min-width: 0px;\\n}\\r\\n.min-w-5{\\n  min-width: 1.25rem;\\n}\\r\\n.min-w-\\\\[12rem\\\\]{\\n  min-width: 12rem;\\n}\\r\\n.min-w-\\\\[8rem\\\\]{\\n  min-width: 8rem;\\n}\\r\\n.min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\]{\\n  min-width: var(--radix-select-trigger-width);\\n}\\r\\n.max-w-2xl{\\n  max-width: 42rem;\\n}\\r\\n.max-w-3xl{\\n  max-width: 48rem;\\n}\\r\\n.max-w-4xl{\\n  max-width: 56rem;\\n}\\r\\n.max-w-6xl{\\n  max-width: 72rem;\\n}\\r\\n.max-w-7xl{\\n  max-width: 80rem;\\n}\\r\\n.max-w-\\\\[--skeleton-width\\\\]{\\n  max-width: var(--skeleton-width);\\n}\\r\\n.max-w-lg{\\n  max-width: 32rem;\\n}\\r\\n.max-w-max{\\n  max-width: -moz-max-content;\\n  max-width: max-content;\\n}\\r\\n.max-w-md{\\n  max-width: 28rem;\\n}\\r\\n.flex-1{\\n  flex: 1 1 0%;\\n}\\r\\n.flex-shrink-0{\\n  flex-shrink: 0;\\n}\\r\\n.shrink-0{\\n  flex-shrink: 0;\\n}\\r\\n.grow{\\n  flex-grow: 1;\\n}\\r\\n.grow-0{\\n  flex-grow: 0;\\n}\\r\\n.basis-full{\\n  flex-basis: 100%;\\n}\\r\\n.caption-bottom{\\n  caption-side: bottom;\\n}\\r\\n.border-collapse{\\n  border-collapse: collapse;\\n}\\r\\n.origin-left{\\n  transform-origin: left;\\n}\\r\\n.-translate-x-1\\\\/2{\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-x-12{\\n  --tw-translate-x: -3rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-x-px{\\n  --tw-translate-x: -1px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-y-1\\\\/2{\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-y-12{\\n  --tw-translate-y: -3rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-0{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-1\\\\/2{\\n  --tw-translate-x: 50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-12{\\n  --tw-translate-x: 3rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-\\\\[-50\\\\%\\\\]{\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-px{\\n  --tw-translate-x: 1px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-0{\\n  --tw-translate-y: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-1\\\\/2{\\n  --tw-translate-y: 50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-12{\\n  --tw-translate-y: 3rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-16{\\n  --tw-translate-y: 4rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-20{\\n  --tw-translate-y: 5rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-\\\\[-50\\\\%\\\\]{\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-0{\\n  --tw-rotate: 0deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-45{\\n  --tw-rotate: 45deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-6{\\n  --tw-rotate: 6deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-90{\\n  --tw-rotate: 90deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-100{\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-75{\\n  --tw-scale-x: .75;\\n  --tw-scale-y: .75;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-90{\\n  --tw-scale-x: .9;\\n  --tw-scale-y: .9;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-95{\\n  --tw-scale-x: .95;\\n  --tw-scale-y: .95;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-x-0{\\n  --tw-scale-x: 0;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.transform{\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.animate-\\\\[scale-x-100_1s_ease-out_0\\\\.5s_forwards\\\\]{\\n  animation: scale-x-100 1s ease-out 0.5s forwards;\\n}\\r\\n@keyframes pulse{\\r\\n\\r\\n  50%{\\n    opacity: .5;\\n  }\\n}\\r\\n.animate-pulse{\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\r\\n@keyframes spin{\\r\\n\\r\\n  to{\\n    transform: rotate(360deg);\\n  }\\n}\\r\\n.animate-spin{\\n  animation: spin 1s linear infinite;\\n}\\r\\n.cursor-default{\\n  cursor: default;\\n}\\r\\n.cursor-pointer{\\n  cursor: pointer;\\n}\\r\\n.touch-none{\\n  touch-action: none;\\n}\\r\\n.select-none{\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\r\\n.resize-none{\\n  resize: none;\\n}\\r\\n.list-none{\\n  list-style-type: none;\\n}\\r\\n.grid-cols-1{\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\r\\n.grid-cols-2{\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\r\\n.flex-row{\\n  flex-direction: row;\\n}\\r\\n.flex-row-reverse{\\n  flex-direction: row-reverse;\\n}\\r\\n.flex-col{\\n  flex-direction: column;\\n}\\r\\n.flex-col-reverse{\\n  flex-direction: column-reverse;\\n}\\r\\n.flex-wrap{\\n  flex-wrap: wrap;\\n}\\r\\n.items-start{\\n  align-items: flex-start;\\n}\\r\\n.items-end{\\n  align-items: flex-end;\\n}\\r\\n.items-center{\\n  align-items: center;\\n}\\r\\n.items-stretch{\\n  align-items: stretch;\\n}\\r\\n.justify-center{\\n  justify-content: center;\\n}\\r\\n.justify-between{\\n  justify-content: space-between;\\n}\\r\\n.gap-1{\\n  gap: 0.25rem;\\n}\\r\\n.gap-1\\\\.5{\\n  gap: 0.375rem;\\n}\\r\\n.gap-12{\\n  gap: 3rem;\\n}\\r\\n.gap-2{\\n  gap: 0.5rem;\\n}\\r\\n.gap-3{\\n  gap: 0.75rem;\\n}\\r\\n.gap-4{\\n  gap: 1rem;\\n}\\r\\n.gap-6{\\n  gap: 1.5rem;\\n}\\r\\n.gap-8{\\n  gap: 2rem;\\n}\\r\\n.space-x-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-6 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-1\\\\.5 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-6 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-8 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-x-reverse > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 1;\\n}\\r\\n.overflow-auto{\\n  overflow: auto;\\n}\\r\\n.overflow-hidden{\\n  overflow: hidden;\\n}\\r\\n.overflow-y-auto{\\n  overflow-y: auto;\\n}\\r\\n.overflow-x-hidden{\\n  overflow-x: hidden;\\n}\\r\\n.whitespace-nowrap{\\n  white-space: nowrap;\\n}\\r\\n.break-words{\\n  overflow-wrap: break-word;\\n}\\r\\n.break-all{\\n  word-break: break-all;\\n}\\r\\n.rounded-\\\\[2px\\\\]{\\n  border-radius: 2px;\\n}\\r\\n.rounded-\\\\[inherit\\\\]{\\n  border-radius: inherit;\\n}\\r\\n.rounded-full{\\n  border-radius: 9999px;\\n}\\r\\n.rounded-lg{\\n  border-radius: var(--radius);\\n}\\r\\n.rounded-md{\\n  border-radius: calc(var(--radius) - 2px);\\n}\\r\\n.rounded-sm{\\n  border-radius: calc(var(--radius) - 4px);\\n}\\r\\n.rounded-t-\\\\[10px\\\\]{\\n  border-top-left-radius: 10px;\\n  border-top-right-radius: 10px;\\n}\\r\\n.rounded-tl-sm{\\n  border-top-left-radius: calc(var(--radius) - 4px);\\n}\\r\\n.border{\\n  border-width: 1px;\\n}\\r\\n.border-0{\\n  border-width: 0px;\\n}\\r\\n.border-2{\\n  border-width: 2px;\\n}\\r\\n.border-\\\\[1\\\\.5px\\\\]{\\n  border-width: 1.5px;\\n}\\r\\n.border-y{\\n  border-top-width: 1px;\\n  border-bottom-width: 1px;\\n}\\r\\n.border-b{\\n  border-bottom-width: 1px;\\n}\\r\\n.border-l{\\n  border-left-width: 1px;\\n}\\r\\n.border-r{\\n  border-right-width: 1px;\\n}\\r\\n.border-t{\\n  border-top-width: 1px;\\n}\\r\\n.border-dashed{\\n  border-style: dashed;\\n}\\r\\n.border-\\\\[--color-border\\\\]{\\n  border-color: var(--color-border);\\n}\\r\\n.border-border\\\\/50{\\n  border-color: hsl(var(--border) / 0.5);\\n}\\r\\n.border-destructive{\\n  border-color: hsl(var(--destructive));\\n}\\r\\n.border-destructive\\\\/50{\\n  border-color: hsl(var(--destructive) / 0.5);\\n}\\r\\n.border-gray-100{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-300{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-input{\\n  border-color: hsl(var(--input));\\n}\\r\\n.border-primary{\\n  border-color: hsl(var(--primary));\\n}\\r\\n.border-sidebar-border{\\n  border-color: hsl(var(--sidebar-border));\\n}\\r\\n.border-transparent{\\n  border-color: transparent;\\n}\\r\\n.border-white{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-l-transparent{\\n  border-left-color: transparent;\\n}\\r\\n.border-t-transparent{\\n  border-top-color: transparent;\\n}\\r\\n.bg-\\\\[--color-bg\\\\]{\\n  background-color: var(--color-bg);\\n}\\r\\n.bg-accent{\\n  background-color: hsl(var(--accent));\\n}\\r\\n.bg-background{\\n  background-color: hsl(var(--background));\\n}\\r\\n.bg-black{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-black\\\\/20{\\n  background-color: rgb(0 0 0 / 0.2);\\n}\\r\\n.bg-black\\\\/80{\\n  background-color: rgb(0 0 0 / 0.8);\\n}\\r\\n.bg-blue-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-blue-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-blue-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-blue-700{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-border{\\n  background-color: hsl(var(--border));\\n}\\r\\n.bg-card{\\n  background-color: hsl(var(--card));\\n}\\r\\n.bg-destructive{\\n  background-color: hsl(var(--destructive));\\n}\\r\\n.bg-foreground{\\n  background-color: hsl(var(--foreground));\\n}\\r\\n.bg-gray-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-200{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-200\\\\/50{\\n  background-color: rgb(229 231 235 / 0.5);\\n}\\r\\n.bg-gray-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-700{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-800{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-900{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-green-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-green-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-green-700{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-muted{\\n  background-color: hsl(var(--muted));\\n}\\r\\n.bg-muted\\\\/50{\\n  background-color: hsl(var(--muted) / 0.5);\\n}\\r\\n.bg-orange-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-orange-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-pink-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-pink-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 39 119 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-popover{\\n  background-color: hsl(var(--popover));\\n}\\r\\n.bg-primary{\\n  background-color: hsl(var(--primary));\\n}\\r\\n.bg-purple-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-purple-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-secondary{\\n  background-color: hsl(var(--secondary));\\n}\\r\\n.bg-sidebar{\\n  background-color: hsl(var(--sidebar-background));\\n}\\r\\n.bg-sidebar-border{\\n  background-color: hsl(var(--sidebar-border));\\n}\\r\\n.bg-teal-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-transparent{\\n  background-color: transparent;\\n}\\r\\n.bg-white{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-white\\\\/80{\\n  background-color: rgb(255 255 255 / 0.8);\\n}\\r\\n.bg-white\\\\/90{\\n  background-color: rgb(255 255 255 / 0.9);\\n}\\r\\n.bg-white\\\\/95{\\n  background-color: rgb(255 255 255 / 0.95);\\n}\\r\\n.bg-yellow-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-yellow-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gradient-to-br{\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\r\\n.bg-gradient-to-r{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\r\\n.from-blue-100{\\n  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-400\\\\/20{\\n  --tw-gradient-from: rgb(96 165 250 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-50\\\\/50{\\n  --tw-gradient-from: rgb(239 246 255 / 0.5) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-500{\\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-600{\\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-cyan-500{\\n  --tw-gradient-from: #06b6d4 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-emerald-500{\\n  --tw-gradient-from: #10b981 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(16 185 129 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-50{\\n  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-900{\\n  --tw-gradient-from: #111827 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-green-500{\\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-indigo-500{\\n  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-orange-500{\\n  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-purple-400\\\\/20{\\n  --tw-gradient-from: rgb(192 132 252 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-purple-500{\\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-slate-50{\\n  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-yellow-500{\\n  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.via-blue-50{\\n  --tw-gradient-to: rgb(239 246 255 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #eff6ff var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.via-blue-800{\\n  --tw-gradient-to: rgb(30 64 175 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #1e40af var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.via-purple-500{\\n  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #a855f7 var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.via-transparent{\\n  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.via-white{\\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.to-blue-50{\\n  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);\\n}\\r\\n.to-blue-500{\\n  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);\\n}\\r\\n.to-blue-600{\\n  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);\\n}\\r\\n.to-cyan-500{\\n  --tw-gradient-to: #06b6d4 var(--tw-gradient-to-position);\\n}\\r\\n.to-emerald-500{\\n  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);\\n}\\r\\n.to-green-500{\\n  --tw-gradient-to: #22c55e var(--tw-gradient-to-position);\\n}\\r\\n.to-orange-500{\\n  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);\\n}\\r\\n.to-orange-600{\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\r\\n.to-pink-400\\\\/20{\\n  --tw-gradient-to: rgb(244 114 182 / 0.2) var(--tw-gradient-to-position);\\n}\\r\\n.to-pink-500{\\n  --tw-gradient-to: #ec4899 var(--tw-gradient-to-position);\\n}\\r\\n.to-pink-600{\\n  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-100{\\n  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-400\\\\/20{\\n  --tw-gradient-to: rgb(192 132 252 / 0.2) var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-50{\\n  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-50\\\\/50{\\n  --tw-gradient-to: rgb(250 245 255 / 0.5) var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-500{\\n  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-600{\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-800{\\n  --tw-gradient-to: #6b21a8 var(--tw-gradient-to-position);\\n}\\r\\n.to-red-600{\\n  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);\\n}\\r\\n.to-teal-500{\\n  --tw-gradient-to: #14b8a6 var(--tw-gradient-to-position);\\n}\\r\\n.to-teal-600{\\n  --tw-gradient-to: #0d9488 var(--tw-gradient-to-position);\\n}\\r\\n.bg-clip-text{\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n}\\r\\n.fill-current{\\n  fill: currentColor;\\n}\\r\\n.p-0{\\n  padding: 0px;\\n}\\r\\n.p-1{\\n  padding: 0.25rem;\\n}\\r\\n.p-2{\\n  padding: 0.5rem;\\n}\\r\\n.p-3{\\n  padding: 0.75rem;\\n}\\r\\n.p-4{\\n  padding: 1rem;\\n}\\r\\n.p-6{\\n  padding: 1.5rem;\\n}\\r\\n.p-\\\\[1px\\\\]{\\n  padding: 1px;\\n}\\r\\n.px-1{\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\r\\n.px-2{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n.px-2\\\\.5{\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n}\\r\\n.px-3{\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\r\\n.px-4{\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\r\\n.px-5{\\n  padding-left: 1.25rem;\\n  padding-right: 1.25rem;\\n}\\r\\n.px-6{\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\r\\n.px-8{\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\r\\n.py-0\\\\.5{\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n}\\r\\n.py-1{\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\r\\n.py-1\\\\.5{\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\r\\n.py-2{\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\r\\n.py-20{\\n  padding-top: 5rem;\\n  padding-bottom: 5rem;\\n}\\r\\n.py-3{\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n.py-4{\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\r\\n.py-6{\\n  padding-top: 1.5rem;\\n  padding-bottom: 1.5rem;\\n}\\r\\n.pb-3{\\n  padding-bottom: 0.75rem;\\n}\\r\\n.pb-4{\\n  padding-bottom: 1rem;\\n}\\r\\n.pl-2\\\\.5{\\n  padding-left: 0.625rem;\\n}\\r\\n.pl-4{\\n  padding-left: 1rem;\\n}\\r\\n.pl-8{\\n  padding-left: 2rem;\\n}\\r\\n.pr-2{\\n  padding-right: 0.5rem;\\n}\\r\\n.pr-2\\\\.5{\\n  padding-right: 0.625rem;\\n}\\r\\n.pr-8{\\n  padding-right: 2rem;\\n}\\r\\n.pt-0{\\n  padding-top: 0px;\\n}\\r\\n.pt-1{\\n  padding-top: 0.25rem;\\n}\\r\\n.pt-2{\\n  padding-top: 0.5rem;\\n}\\r\\n.pt-20{\\n  padding-top: 5rem;\\n}\\r\\n.pt-3{\\n  padding-top: 0.75rem;\\n}\\r\\n.pt-4{\\n  padding-top: 1rem;\\n}\\r\\n.pt-6{\\n  padding-top: 1.5rem;\\n}\\r\\n.text-left{\\n  text-align: left;\\n}\\r\\n.text-center{\\n  text-align: center;\\n}\\r\\n.text-right{\\n  text-align: right;\\n}\\r\\n.align-middle{\\n  vertical-align: middle;\\n}\\r\\n.font-cairo{\\n  font-family: Cairo, sans-serif;\\n}\\r\\n.font-inter{\\n  font-family: Inter, sans-serif;\\n}\\r\\n.font-mono{\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace;\\n}\\r\\n.text-2xl{\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\r\\n.text-3xl{\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\r\\n.text-4xl{\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\r\\n.text-9xl{\\n  font-size: 8rem;\\n  line-height: 1;\\n}\\r\\n.text-\\\\[0\\\\.8rem\\\\]{\\n  font-size: 0.8rem;\\n}\\r\\n.text-base{\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\r\\n.text-lg{\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-sm{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n.text-xl{\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-xs{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n.font-bold{\\n  font-weight: 700;\\n}\\r\\n.font-medium{\\n  font-weight: 500;\\n}\\r\\n.font-normal{\\n  font-weight: 400;\\n}\\r\\n.font-semibold{\\n  font-weight: 600;\\n}\\r\\n.tabular-nums{\\n  --tw-numeric-spacing: tabular-nums;\\n  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);\\n}\\r\\n.leading-none{\\n  line-height: 1;\\n}\\r\\n.leading-relaxed{\\n  line-height: 1.625;\\n}\\r\\n.tracking-tight{\\n  letter-spacing: -0.025em;\\n}\\r\\n.tracking-widest{\\n  letter-spacing: 0.1em;\\n}\\r\\n.text-accent-foreground{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n.text-blue-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-card-foreground{\\n  color: hsl(var(--card-foreground));\\n}\\r\\n.text-current{\\n  color: currentColor;\\n}\\r\\n.text-destructive{\\n  color: hsl(var(--destructive));\\n}\\r\\n.text-destructive-foreground{\\n  color: hsl(var(--destructive-foreground));\\n}\\r\\n.text-foreground{\\n  color: hsl(var(--foreground));\\n}\\r\\n.text-foreground\\\\/50{\\n  color: hsl(var(--foreground) / 0.5);\\n}\\r\\n.text-gray-200{\\n  --tw-text-opacity: 1;\\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-muted-foreground{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n.text-popover-foreground{\\n  color: hsl(var(--popover-foreground));\\n}\\r\\n.text-primary{\\n  color: hsl(var(--primary));\\n}\\r\\n.text-primary-foreground{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n.text-secondary-foreground{\\n  color: hsl(var(--secondary-foreground));\\n}\\r\\n.text-sidebar-foreground{\\n  color: hsl(var(--sidebar-foreground));\\n}\\r\\n.text-sidebar-foreground\\\\/70{\\n  color: hsl(var(--sidebar-foreground) / 0.7);\\n}\\r\\n.text-transparent{\\n  color: transparent;\\n}\\r\\n.text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n.underline-offset-4{\\n  text-underline-offset: 4px;\\n}\\r\\n.opacity-0{\\n  opacity: 0;\\n}\\r\\n.opacity-100{\\n  opacity: 1;\\n}\\r\\n.opacity-5{\\n  opacity: 0.05;\\n}\\r\\n.opacity-50{\\n  opacity: 0.5;\\n}\\r\\n.opacity-60{\\n  opacity: 0.6;\\n}\\r\\n.opacity-70{\\n  opacity: 0.7;\\n}\\r\\n.opacity-90{\\n  opacity: 0.9;\\n}\\r\\n.shadow-\\\\[0_0_0_1px_hsl\\\\(var\\\\(--sidebar-border\\\\)\\\\)\\\\]{\\n  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));\\n  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-md{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-none{\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-sm{\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-xl{\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.outline-none{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n.outline{\\n  outline-style: solid;\\n}\\r\\n.ring-0{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n.ring-2{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n.ring-gray-200{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity, 1));\\n}\\r\\n.ring-ring{\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n.ring-sidebar-ring{\\n  --tw-ring-color: hsl(var(--sidebar-ring));\\n}\\r\\n.ring-offset-background{\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\r\\n.blur-3xl{\\n  --tw-blur: blur(64px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.filter{\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.backdrop-blur-sm{\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.transition{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-\\\\[left\\\\2c right\\\\2c width\\\\]{\\n  transition-property: left,right,width;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-\\\\[margin\\\\2c opa\\\\]{\\n  transition-property: margin,opa;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-\\\\[width\\\\2c height\\\\2c padding\\\\]{\\n  transition-property: width,height,padding;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-\\\\[width\\\\]{\\n  transition-property: width;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-all{\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-colors{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-opacity{\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-transform{\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.duration-1000{\\n  transition-duration: 1000ms;\\n}\\r\\n.duration-150{\\n  transition-duration: 150ms;\\n}\\r\\n.duration-200{\\n  transition-duration: 200ms;\\n}\\r\\n.duration-300{\\n  transition-duration: 300ms;\\n}\\r\\n.duration-500{\\n  transition-duration: 500ms;\\n}\\r\\n.ease-in-out{\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\r\\n.ease-linear{\\n  transition-timing-function: linear;\\n}\\r\\n.ease-out{\\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\r\\n@keyframes enter{\\r\\n\\r\\n  from{\\n    opacity: var(--tw-enter-opacity, 1);\\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\\n  }\\n}\\r\\n@keyframes exit{\\r\\n\\r\\n  to{\\n    opacity: var(--tw-exit-opacity, 1);\\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\\n  }\\n}\\r\\n.animate-in{\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n.fade-in-0{\\n  --tw-enter-opacity: 0;\\n}\\r\\n.fade-in-80{\\n  --tw-enter-opacity: 0.8;\\n}\\r\\n.zoom-in-95{\\n  --tw-enter-scale: .95;\\n}\\r\\n.duration-1000{\\n  animation-duration: 1000ms;\\n}\\r\\n.duration-150{\\n  animation-duration: 150ms;\\n}\\r\\n.duration-200{\\n  animation-duration: 200ms;\\n}\\r\\n.duration-300{\\n  animation-duration: 300ms;\\n}\\r\\n.duration-500{\\n  animation-duration: 500ms;\\n}\\r\\n.ease-in-out{\\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\r\\n.ease-linear{\\n  animation-timing-function: linear;\\n}\\r\\n.ease-out{\\n  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\r\\n\\r\\n/* Custom animations and effects - Enhanced for smoothness */\\r\\n@keyframes gradient-x {\\r\\n  0%, 100% {\\r\\n    background-size: 400% 400%;\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-size: 400% 400%;\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes float {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0px) rotate(0deg);\\r\\n  }\\r\\n  33% {\\r\\n    transform: translateY(-15px) rotate(1deg);\\r\\n  }\\r\\n  66% {\\r\\n    transform: translateY(-8px) rotate(-1deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes bounce-slow {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0) scale(1);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-12px) scale(1.02);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes pulse-slow {\\r\\n  0%, 100% {\\r\\n    opacity: 0.4;\\r\\n    transform: scale(1);\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 0.9;\\r\\n    transform: scale(1.05);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes fade-in {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(40px) scale(0.95);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0) scale(1);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes slide-in-smooth {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(60px) rotateX(10deg);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0) rotateX(0deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-gradient-x {\\r\\n  animation: gradient-x 6s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\r\\n}\\r\\n\\r\\n.animate-float {\\r\\n  animation: float 8s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\r\\n}\\r\\n\\r\\n.animate-bounce-slow {\\r\\n  animation: bounce-slow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\r\\n}\\r\\n\\r\\n.animate-pulse-slow {\\r\\n  animation: pulse-slow 6s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\r\\n}\\r\\n\\r\\n.animate-fade-in {\\r\\n  animation: fade-in 1.2s cubic-bezier(0.4, 0, 0.2, 1);\\r\\n}\\r\\n\\r\\n.animate-slide-in-smooth {\\r\\n  animation: slide-in-smooth 1s cubic-bezier(0.4, 0, 0.2, 1);\\r\\n}\\r\\n\\r\\n/* Grid pattern background */\\r\\n.bg-grid-pattern {\\r\\n  background-image:\\r\\n    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),\\r\\n    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n}\\r\\n\\r\\n/* Enhanced smooth scrolling */\\r\\nhtml {\\r\\n  scroll-behavior: smooth;\\r\\n  scroll-padding-top: 80px;\\r\\n}\\r\\n\\r\\n/* Language transition effects */\\r\\n[dir=\\\"rtl\\\"] {\\r\\n  transition: all 0.3s ease-in-out;\\r\\n}\\r\\n\\r\\n[dir=\\\"ltr\\\"] {\\r\\n  transition: all 0.3s ease-in-out;\\r\\n}\\r\\n\\r\\n/* Text direction transitions */\\r\\n.text-transition {\\r\\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n}\\r\\n\\r\\n/* Performance optimizations */\\r\\n* {\\r\\n  -webkit-font-smoothing: antialiased;\\r\\n  -moz-osx-font-smoothing: grayscale;\\r\\n}\\r\\n\\r\\n/* Hardware acceleration for smooth animations */\\r\\n.animate-gpu {\\r\\n  transform: translateZ(0);\\r\\n  will-change: transform, opacity;\\r\\n  backface-visibility: hidden;\\r\\n  perspective: 1000px;\\r\\n}\\r\\n\\r\\n/* Reduce motion for accessibility */\\r\\n@media (prefers-reduced-motion: reduce) {\\r\\n  *,\\r\\n  *::before,\\r\\n  *::after {\\r\\n    animation-duration: 0.01ms !important;\\r\\n    animation-iteration-count: 1 !important;\\r\\n    transition-duration: 0.01ms !important;\\r\\n    scroll-behavior: auto !important;\\r\\n  }\\r\\n\\r\\n  .animate-gpu {\\r\\n    transform: none !important;\\r\\n    will-change: auto !important;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: #f1f1f1;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: linear-gradient(45deg, #3b82f6, #8b5cf6);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: linear-gradient(45deg, #2563eb, #7c3aed);\\r\\n}\\r\\n\\r\\n/* Text selection */\\r\\n::-moz-selection {\\r\\n  background: rgba(59, 130, 246, 0.3);\\r\\n  color: inherit;\\r\\n}\\r\\n::selection {\\r\\n  background: rgba(59, 130, 246, 0.3);\\r\\n  color: inherit;\\r\\n}\\r\\n\\r\\n/* Focus styles */\\r\\n.focus-visible:focus {\\r\\n  outline: 2px solid #3b82f6;\\r\\n  outline-offset: 2px;\\r\\n}\\r\\n\\r\\n/* Line clamp utility */\\r\\n.line-clamp-3 {\\r\\n  display: -webkit-box;\\r\\n  -webkit-line-clamp: 3;\\r\\n  -webkit-box-orient: vertical;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n/* Custom avatar glow effect */\\r\\n.avatar-glow {\\r\\n  box-shadow:\\r\\n    0 0 20px rgba(59, 130, 246, 0.3),\\r\\n    0 0 40px rgba(139, 92, 246, 0.2),\\r\\n    0 0 60px rgba(236, 72, 153, 0.1);\\r\\n}\\r\\n\\r\\n.avatar-glow:hover {\\r\\n  box-shadow:\\r\\n    0 0 30px rgba(59, 130, 246, 0.4),\\r\\n    0 0 60px rgba(139, 92, 246, 0.3),\\r\\n    0 0 90px rgba(236, 72, 153, 0.2);\\r\\n}\\r\\n\\r\\n/* Scroll Animation Classes - Enhanced for smoothness */\\r\\n.scroll-animate {\\r\\n  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, opacity;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-100 {\\r\\n  transition-delay: 150ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-200 {\\r\\n  transition-delay: 300ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-300 {\\r\\n  transition-delay: 450ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-400 {\\r\\n  transition-delay: 600ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-500 {\\r\\n  transition-delay: 750ms;\\r\\n}\\r\\n\\r\\n/* Stagger animation for children - Enhanced */\\r\\n.stagger-children > * {\\r\\n  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, opacity;\\r\\n}\\r\\n\\r\\n.stagger-children > *:nth-child(1) { transition-delay: 0ms; }\\r\\n.stagger-children > *:nth-child(2) { transition-delay: 150ms; }\\r\\n.stagger-children > *:nth-child(3) { transition-delay: 300ms; }\\r\\n.stagger-children > *:nth-child(4) { transition-delay: 450ms; }\\r\\n.stagger-children > *:nth-child(5) { transition-delay: 600ms; }\\r\\n.stagger-children > *:nth-child(6) { transition-delay: 750ms; }\\r\\n.stagger-children > *:nth-child(7) { transition-delay: 900ms; }\\r\\n.stagger-children > *:nth-child(8) { transition-delay: 1050ms; }\\r\\n\\r\\n/* Parallax effect */\\r\\n.parallax-slow {\\r\\n  transform: translateY(var(--scroll-y, 0) * 0.5);\\r\\n}\\r\\n\\r\\n.parallax-fast {\\r\\n  transform: translateY(var(--scroll-y, 0) * -0.3);\\r\\n}\\r\\n\\r\\n/* Reveal animations */\\r\\n.reveal-up {\\r\\n  opacity: 0;\\r\\n  transform: translateY(50px);\\r\\n}\\r\\n\\r\\n.reveal-up.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.reveal-left {\\r\\n  opacity: 0;\\r\\n  transform: translateX(-50px);\\r\\n}\\r\\n\\r\\n.reveal-left.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateX(0);\\r\\n}\\r\\n\\r\\n.reveal-right {\\r\\n  opacity: 0;\\r\\n  transform: translateX(50px);\\r\\n}\\r\\n\\r\\n.reveal-right.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateX(0);\\r\\n}\\r\\n\\r\\n.reveal-scale {\\r\\n  opacity: 0;\\r\\n  transform: scale(0.8);\\r\\n}\\r\\n\\r\\n.reveal-scale.revealed {\\r\\n  opacity: 1;\\r\\n  transform: scale(1);\\r\\n}\\r\\n\\r\\n/* Hover effects for cards - Enhanced smoothness */\\r\\n.card-hover {\\r\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, box-shadow;\\r\\n}\\r\\n\\r\\n.card-hover:hover {\\r\\n  transform: translateY(-12px) scale(1.03) rotateX(2deg);\\r\\n  box-shadow:\\r\\n    0 32px 64px -12px rgba(0, 0, 0, 0.25),\\r\\n    0 0 0 1px rgba(255, 255, 255, 0.1);\\r\\n}\\r\\n\\r\\n/* Enhanced gradient text animation */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #ec4899, #10b981, #f59e0b);\\r\\n  background-size: 400% 400%;\\r\\n  animation: gradient-shift 8s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  will-change: background-position;\\r\\n}\\r\\n\\r\\n@keyframes gradient-shift {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Typing animation */\\r\\n.typing-animation {\\r\\n  overflow: hidden;\\r\\n  border-right: 2px solid #3b82f6;\\r\\n  white-space: nowrap;\\r\\n  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;\\r\\n}\\r\\n\\r\\n@keyframes typing {\\r\\n  from {\\r\\n    width: 0;\\r\\n  }\\r\\n  to {\\r\\n    width: 100%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes blink-caret {\\r\\n  from, to {\\r\\n    border-color: transparent;\\r\\n  }\\r\\n  50% {\\r\\n    border-color: #3b82f6;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Enhanced magnetic effect */\\r\\n.magnetic {\\r\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform;\\r\\n}\\r\\n\\r\\n.magnetic:hover {\\r\\n  transform: scale(1.08) translateY(-2px);\\r\\n  filter: brightness(1.1);\\r\\n}\\r\\n\\r\\n/* Smooth button transitions */\\r\\n.btn-smooth {\\r\\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, box-shadow, background-color;\\r\\n}\\r\\n\\r\\n.btn-smooth:hover {\\r\\n  transform: translateY(-2px) scale(1.02);\\r\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\\r\\n}\\r\\n\\r\\n.btn-smooth:active {\\r\\n  transform: translateY(0) scale(0.98);\\r\\n  transition-duration: 0.1s;\\r\\n}\\r\\n\\r\\n/* Glitch effect */\\r\\n.glitch {\\r\\n  position: relative;\\r\\n  animation: glitch 2s infinite;\\r\\n}\\r\\n\\r\\n.glitch::before,\\r\\n.glitch::after {\\r\\n  content: attr(data-text);\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n}\\r\\n\\r\\n.glitch::before {\\r\\n  animation: glitch-1 0.5s infinite;\\r\\n  color: #ff0000;\\r\\n  z-index: -1;\\r\\n}\\r\\n\\r\\n.glitch::after {\\r\\n  animation: glitch-2 0.5s infinite;\\r\\n  color: #00ff00;\\r\\n  z-index: -2;\\r\\n}\\r\\n\\r\\n@keyframes glitch {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(-2px, 2px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(-2px, -2px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(2px, 2px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(2px, -2px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes glitch-1 {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(-1px, 1px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(-1px, -1px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(1px, 1px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(1px, -1px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes glitch-2 {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(1px, -1px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(1px, 1px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(-1px, -1px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(-1px, 1px);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* RTL Support */\\r\\n[dir=\\\"rtl\\\"] .flex-row {\\r\\n  flex-direction: row-reverse;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .flex-row-reverse {\\r\\n  flex-direction: row;\\r\\n}\\r\\n\\r\\n/* RTL Space between */\\r\\n[dir=\\\"rtl\\\"] .space-x-8 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n/* RTL Gap utilities */\\r\\n[dir=\\\"rtl\\\"] .gap-2 {\\r\\n  gap: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .gap-3 {\\r\\n  gap: 0.75rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .gap-4 {\\r\\n  gap: 1rem;\\r\\n}\\r\\n\\r\\n/* RTL Icon spacing */\\r\\n[dir=\\\"rtl\\\"] .mr-2 {\\r\\n  margin-right: 0;\\r\\n  margin-left: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .ml-2 {\\r\\n  margin-left: 0;\\r\\n  margin-right: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .mr-3 {\\r\\n  margin-right: 0;\\r\\n  margin-left: 0.75rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .ml-3 {\\r\\n  margin-left: 0;\\r\\n  margin-right: 0.75rem;\\r\\n}\\r\\n\\r\\n/* RTL Text alignment */\\r\\n[dir=\\\"rtl\\\"] .text-left {\\r\\n  text-align: right;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .text-right {\\r\\n  text-align: left;\\r\\n}\\r\\n\\r\\n/* RTL Float */\\r\\n[dir=\\\"rtl\\\"] .float-left {\\r\\n  float: right;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .float-right {\\r\\n  float: left;\\r\\n}\\r\\n\\r\\n/* RTL Positioning */\\r\\n[dir=\\\"rtl\\\"] .left-0 {\\r\\n  left: auto;\\r\\n  right: 0;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .right-0 {\\r\\n  right: auto;\\r\\n  left: 0;\\r\\n}\\r\\n\\r\\n/* RTL Border radius */\\r\\n[dir=\\\"rtl\\\"] .rounded-l {\\r\\n  border-top-left-radius: 0;\\r\\n  border-bottom-left-radius: 0;\\r\\n  border-top-right-radius: 0.25rem;\\r\\n  border-bottom-right-radius: 0.25rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .rounded-r {\\r\\n  border-top-right-radius: 0;\\r\\n  border-bottom-right-radius: 0;\\r\\n  border-top-left-radius: 0.25rem;\\r\\n  border-bottom-left-radius: 0.25rem;\\r\\n}\\r\\n\\r\\n/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */\\r\\n\\r\\n/* Enhanced animations and effects */\\r\\n@keyframes fade-in {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(30px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes float {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-20px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes bounce-slow {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-25px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes pulse-slow {\\r\\n  0%, 100% {\\r\\n    opacity: 0.4;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 0.8;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes gradient-x {\\r\\n  0%, 100% {\\r\\n    background-size: 200% 200%;\\r\\n    background-position: left center;\\r\\n  }\\r\\n  50% {\\r\\n    background-size: 200% 200%;\\r\\n    background-position: right center;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes scale-x-100 {\\r\\n  from {\\r\\n    transform: scaleX(0);\\r\\n  }\\r\\n  to {\\r\\n    transform: scaleX(1);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-fade-in {\\r\\n  animation: fade-in 0.8s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-float {\\r\\n  animation: float 6s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-bounce-slow {\\r\\n  animation: bounce-slow 3s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-pulse-slow {\\r\\n  animation: pulse-slow 4s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-gradient-x {\\r\\n  animation: gradient-x 3s ease infinite;\\r\\n}\\r\\n\\r\\n/* Smooth scroll behavior */\\r\\nhtml {\\r\\n  scroll-behavior: smooth;\\r\\n}\\r\\n\\r\\n/* Grid pattern background */\\r\\n.bg-grid-pattern {\\r\\n  background-image: \\r\\n    linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),\\r\\n    linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n}\\r\\n\\r\\n/* Enhanced gradients */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Glassmorphism effect */\\r\\n.glass-effect {\\r\\n  background: rgba(255, 255, 255, 0.25);\\r\\n  -webkit-backdrop-filter: blur(10px);\\r\\n          backdrop-filter: blur(10px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.18);\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: #f1f1f1;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: linear-gradient(45deg, #667eea, #764ba2);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: linear-gradient(45deg, #5a6fd8, #6a419a);\\r\\n}\\r\\n\\r\\n.file\\\\:border-0::file-selector-button{\\n  border-width: 0px;\\n}\\r\\n\\r\\n.file\\\\:bg-transparent::file-selector-button{\\n  background-color: transparent;\\n}\\r\\n\\r\\n.file\\\\:text-sm::file-selector-button{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n\\r\\n.file\\\\:font-medium::file-selector-button{\\n  font-weight: 500;\\n}\\r\\n\\r\\n.file\\\\:text-foreground::file-selector-button{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::-moz-placeholder{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::placeholder{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.after\\\\:absolute::after{\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\r\\n\\r\\n.after\\\\:-inset-2::after{\\n  content: var(--tw-content);\\n  inset: -0.5rem;\\n}\\r\\n\\r\\n.after\\\\:inset-y-0::after{\\n  content: var(--tw-content);\\n  top: 0px;\\n  bottom: 0px;\\n}\\r\\n\\r\\n.after\\\\:left-1\\\\/2::after{\\n  content: var(--tw-content);\\n  left: 50%;\\n}\\r\\n\\r\\n.after\\\\:w-1::after{\\n  content: var(--tw-content);\\n  width: 0.25rem;\\n}\\r\\n\\r\\n.after\\\\:w-\\\\[2px\\\\]::after{\\n  content: var(--tw-content);\\n  width: 2px;\\n}\\r\\n\\r\\n.after\\\\:-translate-x-1\\\\/2::after{\\n  content: var(--tw-content);\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.first\\\\:rounded-l-md:first-child{\\n  border-top-left-radius: calc(var(--radius) - 2px);\\n  border-bottom-left-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.first\\\\:border-l:first-child{\\n  border-left-width: 1px;\\n}\\r\\n\\r\\n.last\\\\:rounded-r-md:last-child{\\n  border-top-right-radius: calc(var(--radius) - 2px);\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.focus-within\\\\:relative:focus-within{\\n  position: relative;\\n}\\r\\n\\r\\n.focus-within\\\\:z-20:focus-within{\\n  z-index: 20;\\n}\\r\\n\\r\\n.hover\\\\:scale-105:hover{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.hover\\\\:scale-110:hover{\\n  --tw-scale-x: 1.1;\\n  --tw-scale-y: 1.1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.hover\\\\:scale-\\\\[1\\\\.02\\\\]:hover{\\n  --tw-scale-x: 1.02;\\n  --tw-scale-y: 1.02;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.hover\\\\:scale-y-125:hover{\\n  --tw-scale-y: 1.25;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.hover\\\\:bg-accent:hover{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/80:hover{\\n  background-color: hsl(var(--destructive) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/90:hover{\\n  background-color: hsl(var(--destructive) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-100:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-200:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-50:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-800:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-muted:hover{\\n  background-color: hsl(var(--muted));\\n}\\r\\n\\r\\n.hover\\\\:bg-muted\\\\/50:hover{\\n  background-color: hsl(var(--muted) / 0.5);\\n}\\r\\n\\r\\n.hover\\\\:bg-primary:hover{\\n  background-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/80:hover{\\n  background-color: hsl(var(--primary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/90:hover{\\n  background-color: hsl(var(--primary) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-secondary:hover{\\n  background-color: hsl(var(--secondary));\\n}\\r\\n\\r\\n.hover\\\\:bg-secondary\\\\/80:hover{\\n  background-color: hsl(var(--secondary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:bg-sidebar-accent:hover{\\n  background-color: hsl(var(--sidebar-accent));\\n}\\r\\n\\r\\n.hover\\\\:bg-white\\\\/90:hover{\\n  background-color: rgb(255 255 255 / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:from-blue-600:hover{\\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n\\r\\n.hover\\\\:from-blue-700:hover{\\n  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n\\r\\n.hover\\\\:to-purple-700:hover{\\n  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);\\n}\\r\\n\\r\\n.hover\\\\:text-accent-foreground:hover{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-blue-600:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:text-foreground:hover{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-gray-900:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:text-muted-foreground:hover{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-primary-foreground:hover{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-sidebar-accent-foreground:hover{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.hover\\\\:underline:hover{\\n  text-decoration-line: underline;\\n}\\r\\n\\r\\n.hover\\\\:opacity-100:hover{\\n  opacity: 1;\\n}\\r\\n\\r\\n.hover\\\\:opacity-90:hover{\\n  opacity: 0.9;\\n}\\r\\n\\r\\n.hover\\\\:shadow-2xl:hover{\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.hover\\\\:shadow-\\\\[0_0_0_1px_hsl\\\\(var\\\\(--sidebar-accent\\\\)\\\\)\\\\]:hover{\\n  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));\\n  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.hover\\\\:shadow-xl:hover{\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.hover\\\\:after\\\\:bg-sidebar-border:hover::after{\\n  content: var(--tw-content);\\n  background-color: hsl(var(--sidebar-border));\\n}\\r\\n\\r\\n.focus\\\\:border-blue-500:focus{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n}\\r\\n\\r\\n.focus\\\\:bg-accent:focus{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.focus\\\\:bg-primary:focus{\\n  background-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.focus\\\\:text-accent-foreground:focus{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.focus\\\\:text-primary-foreground:focus{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n\\r\\n.focus\\\\:opacity-100:focus{\\n  opacity: 1;\\n}\\r\\n\\r\\n.focus\\\\:outline-none:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus\\\\:ring-2:focus{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus\\\\:ring-blue-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\r\\n\\r\\n.focus\\\\:ring-ring:focus{\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus\\\\:ring-offset-2:focus{\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:outline-none:focus-visible{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-1:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-2:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-ring:focus-visible{\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-sidebar-ring:focus-visible{\\n  --tw-ring-color: hsl(var(--sidebar-ring));\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-offset-1:focus-visible{\\n  --tw-ring-offset-width: 1px;\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-offset-2:focus-visible{\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-offset-background:focus-visible{\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\r\\n\\r\\n.active\\\\:bg-sidebar-accent:active{\\n  background-color: hsl(var(--sidebar-accent));\\n}\\r\\n\\r\\n.active\\\\:text-sidebar-accent-foreground:active{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.disabled\\\\:pointer-events-none:disabled{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.disabled\\\\:cursor-not-allowed:disabled{\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.disabled\\\\:opacity-50:disabled{\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.group\\\\/menu-item:focus-within .group-focus-within\\\\/menu-item\\\\:opacity-100{\\n  opacity: 1;\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:-translate-y-1{\\n  --tw-translate-y: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group\\\\/item:hover .group-hover\\\\/item\\\\:scale-125{\\n  --tw-scale-x: 1.25;\\n  --tw-scale-y: 1.25;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:scale-110{\\n  --tw-scale-x: 1.1;\\n  --tw-scale-y: 1.1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group\\\\/item:hover .group-hover\\\\/item\\\\:text-gray-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:text-blue-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:text-blue-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:text-gray-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group\\\\/menu-item:hover .group-hover\\\\/menu-item\\\\:opacity-100{\\n  opacity: 1;\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:opacity-100{\\n  opacity: 1;\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:border-muted\\\\/40{\\n  border-color: hsl(var(--muted) / 0.4);\\n}\\r\\n\\r\\n.group.toaster .group-\\\\[\\\\.toaster\\\\]\\\\:border-border{\\n  border-color: hsl(var(--border));\\n}\\r\\n\\r\\n.group.toast .group-\\\\[\\\\.toast\\\\]\\\\:bg-muted{\\n  background-color: hsl(var(--muted));\\n}\\r\\n\\r\\n.group.toast .group-\\\\[\\\\.toast\\\\]\\\\:bg-primary{\\n  background-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.group.toaster .group-\\\\[\\\\.toaster\\\\]\\\\:bg-background{\\n  background-color: hsl(var(--background));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:text-red-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group.toast .group-\\\\[\\\\.toast\\\\]\\\\:text-muted-foreground{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.group.toast .group-\\\\[\\\\.toast\\\\]\\\\:text-primary-foreground{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n\\r\\n.group.toaster .group-\\\\[\\\\.toaster\\\\]\\\\:text-foreground{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.group.toaster .group-\\\\[\\\\.toaster\\\\]\\\\:shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:border-destructive\\\\/30:hover{\\n  border-color: hsl(var(--destructive) / 0.3);\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:bg-destructive:hover{\\n  background-color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:text-destructive-foreground:hover{\\n  color: hsl(var(--destructive-foreground));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:text-red-50:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(254 242 242 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-destructive:focus{\\n  --tw-ring-color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-red-400:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-offset-red-600:focus{\\n  --tw-ring-offset-color: #dc2626;\\n}\\r\\n\\r\\n.peer\\\\/menu-button:hover ~ .peer-hover\\\\/menu-button\\\\:text-sidebar-accent-foreground{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:cursor-not-allowed{\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:opacity-70{\\n  opacity: 0.7;\\n}\\r\\n\\r\\n.has-\\\\[\\\\[data-variant\\\\=inset\\\\]\\\\]\\\\:bg-sidebar:has([data-variant=inset]){\\n  background-color: hsl(var(--sidebar-background));\\n}\\r\\n\\r\\n.has-\\\\[\\\\:disabled\\\\]\\\\:opacity-50:has(:disabled){\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.group\\\\/menu-item:has([data-sidebar=menu-action]) .group-has-\\\\[\\\\[data-sidebar\\\\=menu-action\\\\]\\\\]\\\\/menu-item\\\\:pr-8{\\n  padding-right: 2rem;\\n}\\r\\n\\r\\n.aria-disabled\\\\:pointer-events-none[aria-disabled=\\\"true\\\"]{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.aria-disabled\\\\:opacity-50[aria-disabled=\\\"true\\\"]{\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.aria-selected\\\\:bg-accent[aria-selected=\\\"true\\\"]{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.aria-selected\\\\:bg-accent\\\\/50[aria-selected=\\\"true\\\"]{\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\r\\n\\r\\n.aria-selected\\\\:text-accent-foreground[aria-selected=\\\"true\\\"]{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.aria-selected\\\\:text-muted-foreground[aria-selected=\\\"true\\\"]{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.aria-selected\\\\:opacity-100[aria-selected=\\\"true\\\"]{\\n  opacity: 1;\\n}\\r\\n\\r\\n.aria-selected\\\\:opacity-30[aria-selected=\\\"true\\\"]{\\n  opacity: 0.3;\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\=true\\\\]\\\\:pointer-events-none[data-disabled=\\\"true\\\"]{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:pointer-events-none[data-disabled]{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:h-px[data-panel-group-direction=\\\"vertical\\\"]{\\n  height: 1px;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:w-full[data-panel-group-direction=\\\"vertical\\\"]{\\n  width: 100%;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=bottom\\\\]\\\\:translate-y-1[data-side=\\\"bottom\\\"]{\\n  --tw-translate-y: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=left\\\\]\\\\:-translate-x-1[data-side=\\\"left\\\"]{\\n  --tw-translate-x: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=right\\\\]\\\\:translate-x-1[data-side=\\\"right\\\"]{\\n  --tw-translate-x: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=top\\\\]\\\\:-translate-y-1[data-side=\\\"top\\\"]{\\n  --tw-translate-y: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=checked\\\\]\\\\:translate-x-5[data-state=\\\"checked\\\"]{\\n  --tw-translate-x: 1.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=unchecked\\\\]\\\\:translate-x-0[data-state=\\\"unchecked\\\"]{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=cancel\\\\]\\\\:translate-x-0[data-swipe=\\\"cancel\\\"]{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=end\\\\]\\\\:translate-x-\\\\[var\\\\(--radix-toast-swipe-end-x\\\\)\\\\][data-swipe=\\\"end\\\"]{\\n  --tw-translate-x: var(--radix-toast-swipe-end-x);\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=move\\\\]\\\\:translate-x-\\\\[var\\\\(--radix-toast-swipe-move-x\\\\)\\\\][data-swipe=\\\"move\\\"]{\\n  --tw-translate-x: var(--radix-toast-swipe-move-x);\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n@keyframes accordion-up{\\r\\n\\r\\n  from{\\n    height: var(--radix-accordion-content-height);\\n  }\\r\\n\\r\\n  to{\\n    height: 0;\\n  }\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:animate-accordion-up[data-state=\\\"closed\\\"]{\\n  animation: accordion-up 0.2s ease-out;\\n}\\r\\n\\r\\n@keyframes accordion-down{\\r\\n\\r\\n  from{\\n    height: 0;\\n  }\\r\\n\\r\\n  to{\\n    height: var(--radix-accordion-content-height);\\n  }\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:animate-accordion-down[data-state=\\\"open\\\"]{\\n  animation: accordion-down 0.2s ease-out;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:flex-col[data-panel-group-direction=\\\"vertical\\\"]{\\n  flex-direction: column;\\n}\\r\\n\\r\\n.data-\\\\[active\\\\=true\\\\]\\\\:bg-sidebar-accent[data-active=\\\"true\\\"]{\\n  background-color: hsl(var(--sidebar-accent));\\n}\\r\\n\\r\\n.data-\\\\[active\\\\]\\\\:bg-accent\\\\/50[data-active]{\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\r\\n\\r\\n.data-\\\\[selected\\\\=\\\\'true\\\\'\\\\]\\\\:bg-accent[data-selected='true']{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:bg-background[data-state=\\\"active\\\"]{\\n  background-color: hsl(var(--background));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=checked\\\\]\\\\:bg-primary[data-state=\\\"checked\\\"]{\\n  background-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=on\\\\]\\\\:bg-accent[data-state=\\\"on\\\"]{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent[data-state=\\\"open\\\"]{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent\\\\/50[data-state=\\\"open\\\"]{\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-secondary[data-state=\\\"open\\\"]{\\n  background-color: hsl(var(--secondary));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=selected\\\\]\\\\:bg-muted[data-state=\\\"selected\\\"]{\\n  background-color: hsl(var(--muted));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=unchecked\\\\]\\\\:bg-input[data-state=\\\"unchecked\\\"]{\\n  background-color: hsl(var(--input));\\n}\\r\\n\\r\\n.data-\\\\[active\\\\=true\\\\]\\\\:font-medium[data-active=\\\"true\\\"]{\\n  font-weight: 500;\\n}\\r\\n\\r\\n.data-\\\\[active\\\\=true\\\\]\\\\:text-sidebar-accent-foreground[data-active=\\\"true\\\"]{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.data-\\\\[selected\\\\=true\\\\]\\\\:text-accent-foreground[data-selected=\\\"true\\\"]{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:text-foreground[data-state=\\\"active\\\"]{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=checked\\\\]\\\\:text-primary-foreground[data-state=\\\"checked\\\"]{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=on\\\\]\\\\:text-accent-foreground[data-state=\\\"on\\\"]{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:text-accent-foreground[data-state=\\\"open\\\"]{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:text-muted-foreground[data-state=\\\"open\\\"]{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\=true\\\\]\\\\:opacity-50[data-disabled=\\\"true\\\"]{\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:opacity-50[data-disabled]{\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:opacity-100[data-state=\\\"open\\\"]{\\n  opacity: 1;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:shadow-sm[data-state=\\\"active\\\"]{\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=move\\\\]\\\\:transition-none[data-swipe=\\\"move\\\"]{\\n  transition-property: none;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:duration-300[data-state=\\\"closed\\\"]{\\n  transition-duration: 300ms;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:duration-500[data-state=\\\"open\\\"]{\\n  transition-duration: 500ms;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\^\\\\=from-\\\\]\\\\:animate-in[data-motion^=\\\"from-\\\"]{\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:animate-in[data-state=\\\"open\\\"]{\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=visible\\\\]\\\\:animate-in[data-state=\\\"visible\\\"]{\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\^\\\\=to-\\\\]\\\\:animate-out[data-motion^=\\\"to-\\\"]{\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:animate-out[data-state=\\\"closed\\\"]{\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=hidden\\\\]\\\\:animate-out[data-state=\\\"hidden\\\"]{\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=end\\\\]\\\\:animate-out[data-swipe=\\\"end\\\"]{\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\^\\\\=from-\\\\]\\\\:fade-in[data-motion^=\\\"from-\\\"]{\\n  --tw-enter-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\^\\\\=to-\\\\]\\\\:fade-out[data-motion^=\\\"to-\\\"]{\\n  --tw-exit-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-0[data-state=\\\"closed\\\"]{\\n  --tw-exit-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-80[data-state=\\\"closed\\\"]{\\n  --tw-exit-opacity: 0.8;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=hidden\\\\]\\\\:fade-out[data-state=\\\"hidden\\\"]{\\n  --tw-exit-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:fade-in-0[data-state=\\\"open\\\"]{\\n  --tw-enter-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=visible\\\\]\\\\:fade-in[data-state=\\\"visible\\\"]{\\n  --tw-enter-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:zoom-out-95[data-state=\\\"closed\\\"]{\\n  --tw-exit-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-90[data-state=\\\"open\\\"]{\\n  --tw-enter-scale: .9;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-95[data-state=\\\"open\\\"]{\\n  --tw-enter-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\=from-end\\\\]\\\\:slide-in-from-right-52[data-motion=\\\"from-end\\\"]{\\n  --tw-enter-translate-x: 13rem;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\=from-start\\\\]\\\\:slide-in-from-left-52[data-motion=\\\"from-start\\\"]{\\n  --tw-enter-translate-x: -13rem;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\=to-end\\\\]\\\\:slide-out-to-right-52[data-motion=\\\"to-end\\\"]{\\n  --tw-exit-translate-x: 13rem;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\=to-start\\\\]\\\\:slide-out-to-left-52[data-motion=\\\"to-start\\\"]{\\n  --tw-exit-translate-x: -13rem;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=bottom\\\\]\\\\:slide-in-from-top-2[data-side=\\\"bottom\\\"]{\\n  --tw-enter-translate-y: -0.5rem;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=left\\\\]\\\\:slide-in-from-right-2[data-side=\\\"left\\\"]{\\n  --tw-enter-translate-x: 0.5rem;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=right\\\\]\\\\:slide-in-from-left-2[data-side=\\\"right\\\"]{\\n  --tw-enter-translate-x: -0.5rem;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=top\\\\]\\\\:slide-in-from-bottom-2[data-side=\\\"top\\\"]{\\n  --tw-enter-translate-y: 0.5rem;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-bottom[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-y: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-x: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left-1\\\\/2[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-right[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-x: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-right-full[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-x: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-y: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top-\\\\[48\\\\%\\\\][data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-y: -48%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-bottom[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-y: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-x: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left-1\\\\/2[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-right[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-x: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-y: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-\\\\[48\\\\%\\\\][data-state=\\\"open\\\"]{\\n  --tw-enter-translate-y: -48%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-full[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-y: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:duration-300[data-state=\\\"closed\\\"]{\\n  animation-duration: 300ms;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:duration-500[data-state=\\\"open\\\"]{\\n  animation-duration: 500ms;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:left-0[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  left: 0px;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:h-1[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  height: 0.25rem;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:w-full[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  width: 100%;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:-translate-y-1\\\\/2[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:translate-x-0[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:hover\\\\:bg-sidebar-accent:hover[data-state=\\\"open\\\"]{\\n  background-color: hsl(var(--sidebar-accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:hover\\\\:text-sidebar-accent-foreground:hover[data-state=\\\"open\\\"]{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:left-\\\\[calc\\\\(var\\\\(--sidebar-width\\\\)\\\\*-1\\\\)\\\\]{\\n  left: calc(var(--sidebar-width) * -1);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:right-\\\\[calc\\\\(var\\\\(--sidebar-width\\\\)\\\\*-1\\\\)\\\\]{\\n  right: calc(var(--sidebar-width) * -1);\\n}\\r\\n\\r\\n.group[data-side=\\\"left\\\"] .group-data-\\\\[side\\\\=left\\\\]\\\\:-right-4{\\n  right: -1rem;\\n}\\r\\n\\r\\n.group[data-side=\\\"right\\\"] .group-data-\\\\[side\\\\=right\\\\]\\\\:left-0{\\n  left: 0px;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:-mt-8{\\n  margin-top: -2rem;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:hidden{\\n  display: none;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:\\\\!size-8{\\n  width: 2rem !important;\\n  height: 2rem !important;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[--sidebar-width-icon\\\\]{\\n  width: var(--sidebar-width-icon);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[calc\\\\(var\\\\(--sidebar-width-icon\\\\)_\\\\+_theme\\\\(spacing\\\\.4\\\\)\\\\)\\\\]{\\n  width: calc(var(--sidebar-width-icon) + 1rem);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[calc\\\\(var\\\\(--sidebar-width-icon\\\\)_\\\\+_theme\\\\(spacing\\\\.4\\\\)_\\\\+2px\\\\)\\\\]{\\n  width: calc(var(--sidebar-width-icon) + 1rem + 2px);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:w-0{\\n  width: 0px;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:translate-x-0{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group[data-side=\\\"right\\\"] .group-data-\\\\[side\\\\=right\\\\]\\\\:rotate-180{\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group[data-state=\\\"open\\\"] .group-data-\\\\[state\\\\=open\\\\]\\\\:rotate-180{\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:overflow-hidden{\\n  overflow: hidden;\\n}\\r\\n\\r\\n.group[data-variant=\\\"floating\\\"] .group-data-\\\\[variant\\\\=floating\\\\]\\\\:rounded-lg{\\n  border-radius: var(--radius);\\n}\\r\\n\\r\\n.group[data-variant=\\\"floating\\\"] .group-data-\\\\[variant\\\\=floating\\\\]\\\\:border{\\n  border-width: 1px;\\n}\\r\\n\\r\\n.group[data-side=\\\"left\\\"] .group-data-\\\\[side\\\\=left\\\\]\\\\:border-r{\\n  border-right-width: 1px;\\n}\\r\\n\\r\\n.group[data-side=\\\"right\\\"] .group-data-\\\\[side\\\\=right\\\\]\\\\:border-l{\\n  border-left-width: 1px;\\n}\\r\\n\\r\\n.group[data-variant=\\\"floating\\\"] .group-data-\\\\[variant\\\\=floating\\\\]\\\\:border-sidebar-border{\\n  border-color: hsl(var(--sidebar-border));\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:\\\\!p-0{\\n  padding: 0px !important;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:\\\\!p-2{\\n  padding: 0.5rem !important;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:opacity-0{\\n  opacity: 0;\\n}\\r\\n\\r\\n.group[data-variant=\\\"floating\\\"] .group-data-\\\\[variant\\\\=floating\\\\]\\\\:shadow{\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:after\\\\:left-full::after{\\n  content: var(--tw-content);\\n  left: 100%;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:hover\\\\:bg-sidebar:hover{\\n  background-color: hsl(var(--sidebar-background));\\n}\\r\\n\\r\\n.peer\\\\/menu-button[data-size=\\\"default\\\"] ~ .peer-data-\\\\[size\\\\=default\\\\]\\\\/menu-button\\\\:top-1\\\\.5{\\n  top: 0.375rem;\\n}\\r\\n\\r\\n.peer\\\\/menu-button[data-size=\\\"lg\\\"] ~ .peer-data-\\\\[size\\\\=lg\\\\]\\\\/menu-button\\\\:top-2\\\\.5{\\n  top: 0.625rem;\\n}\\r\\n\\r\\n.peer\\\\/menu-button[data-size=\\\"sm\\\"] ~ .peer-data-\\\\[size\\\\=sm\\\\]\\\\/menu-button\\\\:top-1{\\n  top: 0.25rem;\\n}\\r\\n\\r\\n.peer[data-variant=\\\"inset\\\"] ~ .peer-data-\\\\[variant\\\\=inset\\\\]\\\\:min-h-\\\\[calc\\\\(100svh-theme\\\\(spacing\\\\.4\\\\)\\\\)\\\\]{\\n  min-height: calc(100svh - 1rem);\\n}\\r\\n\\r\\n.peer\\\\/menu-button[data-active=\\\"true\\\"] ~ .peer-data-\\\\[active\\\\=true\\\\]\\\\/menu-button\\\\:text-sidebar-accent-foreground{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.dark\\\\:border-destructive:is(.dark *){\\n  border-color: hsl(var(--destructive));\\n}\\r\\n\\r\\n@media (min-width: 640px){\\r\\n\\r\\n  .sm\\\\:bottom-0{\\n    bottom: 0px;\\n  }\\r\\n\\r\\n  .sm\\\\:right-0{\\n    right: 0px;\\n  }\\r\\n\\r\\n  .sm\\\\:top-auto{\\n    top: auto;\\n  }\\r\\n\\r\\n  .sm\\\\:mt-0{\\n    margin-top: 0px;\\n  }\\r\\n\\r\\n  .sm\\\\:flex{\\n    display: flex;\\n  }\\r\\n\\r\\n  .sm\\\\:max-w-sm{\\n    max-width: 24rem;\\n  }\\r\\n\\r\\n  .sm\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .sm\\\\:flex-row{\\n    flex-direction: row;\\n  }\\r\\n\\r\\n  .sm\\\\:flex-row-reverse{\\n    flex-direction: row-reverse;\\n  }\\r\\n\\r\\n  .sm\\\\:flex-col{\\n    flex-direction: column;\\n  }\\r\\n\\r\\n  .sm\\\\:justify-end{\\n    justify-content: flex-end;\\n  }\\r\\n\\r\\n  .sm\\\\:gap-2\\\\.5{\\n    gap: 0.625rem;\\n  }\\r\\n\\r\\n  .sm\\\\:space-x-2 > :not([hidden]) ~ :not([hidden]){\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\r\\n\\r\\n  .sm\\\\:space-x-4 > :not([hidden]) ~ :not([hidden]){\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\r\\n\\r\\n  .sm\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]){\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n  }\\r\\n\\r\\n  .sm\\\\:rounded-lg{\\n    border-radius: var(--radius);\\n  }\\r\\n\\r\\n  .sm\\\\:px-3{\\n    padding-left: 0.75rem;\\n    padding-right: 0.75rem;\\n  }\\r\\n\\r\\n  .sm\\\\:px-6{\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\r\\n\\r\\n  .sm\\\\:text-left{\\n    text-align: left;\\n  }\\r\\n\\r\\n  .data-\\\\[state\\\\=open\\\\]\\\\:sm\\\\:slide-in-from-bottom-full[data-state=\\\"open\\\"]{\\n    --tw-enter-translate-y: 100%;\\n  }\\n}\\r\\n\\r\\n@media (min-width: 768px){\\r\\n\\r\\n  .md\\\\:absolute{\\n    position: absolute;\\n  }\\r\\n\\r\\n  .md\\\\:mt-0{\\n    margin-top: 0px;\\n  }\\r\\n\\r\\n  .md\\\\:block{\\n    display: block;\\n  }\\r\\n\\r\\n  .md\\\\:flex{\\n    display: flex;\\n  }\\r\\n\\r\\n  .md\\\\:hidden{\\n    display: none;\\n  }\\r\\n\\r\\n  .md\\\\:w-\\\\[var\\\\(--radix-navigation-menu-viewport-width\\\\)\\\\]{\\n    width: var(--radix-navigation-menu-viewport-width);\\n  }\\r\\n\\r\\n  .md\\\\:w-auto{\\n    width: auto;\\n  }\\r\\n\\r\\n  .md\\\\:max-w-\\\\[420px\\\\]{\\n    max-width: 420px;\\n  }\\r\\n\\r\\n  .md\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .md\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .md\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .md\\\\:flex-row{\\n    flex-direction: row;\\n  }\\r\\n\\r\\n  .md\\\\:items-center{\\n    align-items: center;\\n  }\\r\\n\\r\\n  .md\\\\:justify-between{\\n    justify-content: space-between;\\n  }\\r\\n\\r\\n  .md\\\\:text-4xl{\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\r\\n\\r\\n  .md\\\\:text-5xl{\\n    font-size: 3rem;\\n    line-height: 1;\\n  }\\r\\n\\r\\n  .md\\\\:text-6xl{\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\r\\n\\r\\n  .md\\\\:text-sm{\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\r\\n\\r\\n  .md\\\\:opacity-0{\\n    opacity: 0;\\n  }\\r\\n\\r\\n  .after\\\\:md\\\\:hidden::after{\\n    content: var(--tw-content);\\n    display: none;\\n  }\\r\\n\\r\\n  .peer[data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:m-2{\\n    margin: 0.5rem;\\n  }\\r\\n\\r\\n  .peer[data-state=\\\"collapsed\\\"][data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[state\\\\=collapsed\\\\]\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:ml-2{\\n    margin-left: 0.5rem;\\n  }\\r\\n\\r\\n  .peer[data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:ml-0{\\n    margin-left: 0px;\\n  }\\r\\n\\r\\n  .peer[data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:rounded-xl{\\n    border-radius: 0.75rem;\\n  }\\r\\n\\r\\n  .peer[data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:shadow{\\n    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  }\\n}\\r\\n\\r\\n@media (min-width: 1024px){\\r\\n\\r\\n  .lg\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .lg\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .lg\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .lg\\\\:px-8{\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\r\\n\\r\\n  .lg\\\\:text-2xl{\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:bg-accent:has([aria-selected]){\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.first\\\\:\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:rounded-l-md:has([aria-selected]):first-child{\\n  border-top-left-radius: calc(var(--radius) - 2px);\\n  border-bottom-left-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.last\\\\:\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:rounded-r-md:has([aria-selected]):last-child{\\n  border-top-right-radius: calc(var(--radius) - 2px);\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\.day-outside\\\\)\\\\]\\\\:bg-accent\\\\/50:has([aria-selected].day-outside){\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\.day-range-end\\\\)\\\\]\\\\:rounded-r-md:has([aria-selected].day-range-end){\\n  border-top-right-radius: calc(var(--radius) - 2px);\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\:has\\\\(\\\\[role\\\\=checkbox\\\\]\\\\)\\\\]\\\\:pr-0:has([role=checkbox]){\\n  padding-right: 0px;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>button\\\\]\\\\:hidden>button{\\n  display: none;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>span\\\\:last-child\\\\]\\\\:truncate>span:last-child{\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>span\\\\]\\\\:line-clamp-1>span{\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 1;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\+div\\\\]\\\\:translate-y-\\\\[-3px\\\\]>svg+div{\\n  --tw-translate-y: -3px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:absolute>svg{\\n  position: absolute;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:left-4>svg{\\n  left: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:top-4>svg{\\n  top: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:size-3\\\\.5>svg{\\n  width: 0.875rem;\\n  height: 0.875rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:size-4>svg{\\n  width: 1rem;\\n  height: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:h-2\\\\.5>svg{\\n  height: 0.625rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:h-3>svg{\\n  height: 0.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:w-2\\\\.5>svg{\\n  width: 0.625rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:w-3>svg{\\n  width: 0.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:shrink-0>svg{\\n  flex-shrink: 0;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-destructive>svg{\\n  color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-foreground>svg{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-muted-foreground>svg{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-sidebar-accent-foreground>svg{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\~\\\\*\\\\]\\\\:pl-7>svg~*{\\n  padding-left: 1.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>tr\\\\]\\\\:last\\\\:border-b-0:last-child>tr{\\n  border-bottom-width: 0px;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\[data-panel-group-direction\\\\=vertical\\\\]\\\\>div\\\\]\\\\:rotate-90[data-panel-group-direction=vertical]>div{\\n  --tw-rotate: 90deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\[data-state\\\\=open\\\\]\\\\>svg\\\\]\\\\:rotate-180[data-state=open]>svg{\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-cartesian-axis-tick_text\\\\]\\\\:fill-muted-foreground .recharts-cartesian-axis-tick text{\\n  fill: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-cartesian-grid_line\\\\[stroke\\\\=\\\\'\\\\#ccc\\\\'\\\\]\\\\]\\\\:stroke-border\\\\/50 .recharts-cartesian-grid line[stroke='#ccc']{\\n  stroke: hsl(var(--border) / 0.5);\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-curve\\\\.recharts-tooltip-cursor\\\\]\\\\:stroke-border .recharts-curve.recharts-tooltip-cursor{\\n  stroke: hsl(var(--border));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-dot\\\\[stroke\\\\=\\\\'\\\\#fff\\\\'\\\\]\\\\]\\\\:stroke-transparent .recharts-dot[stroke='#fff']{\\n  stroke: transparent;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-layer\\\\]\\\\:outline-none .recharts-layer{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-polar-grid_\\\\[stroke\\\\=\\\\'\\\\#ccc\\\\'\\\\]\\\\]\\\\:stroke-border .recharts-polar-grid [stroke='#ccc']{\\n  stroke: hsl(var(--border));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-radial-bar-background-sector\\\\]\\\\:fill-muted .recharts-radial-bar-background-sector{\\n  fill: hsl(var(--muted));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-rectangle\\\\.recharts-tooltip-cursor\\\\]\\\\:fill-muted .recharts-rectangle.recharts-tooltip-cursor{\\n  fill: hsl(var(--muted));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-reference-line_\\\\[stroke\\\\=\\\\'\\\\#ccc\\\\'\\\\]\\\\]\\\\:stroke-border .recharts-reference-line [stroke='#ccc']{\\n  stroke: hsl(var(--border));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-sector\\\\[stroke\\\\=\\\\'\\\\#fff\\\\'\\\\]\\\\]\\\\:stroke-transparent .recharts-sector[stroke='#fff']{\\n  stroke: transparent;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-sector\\\\]\\\\:outline-none .recharts-sector{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-surface\\\\]\\\\:outline-none .recharts-surface{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:px-2 [cmdk-group-heading]{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:py-1\\\\.5 [cmdk-group-heading]{\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:text-xs [cmdk-group-heading]{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:font-medium [cmdk-group-heading]{\\n  font-weight: 500;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:text-muted-foreground [cmdk-group-heading]{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group\\\\]\\\\:not\\\\(\\\\[hidden\\\\]\\\\)_\\\\~\\\\[cmdk-group\\\\]\\\\]\\\\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group]{\\n  padding-top: 0px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group\\\\]\\\\]\\\\:px-2 [cmdk-group]{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-input-wrapper\\\\]_svg\\\\]\\\\:h-5 [cmdk-input-wrapper] svg{\\n  height: 1.25rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-input-wrapper\\\\]_svg\\\\]\\\\:w-5 [cmdk-input-wrapper] svg{\\n  width: 1.25rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-input\\\\]\\\\]\\\\:h-12 [cmdk-input]{\\n  height: 3rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-item\\\\]\\\\]\\\\:px-2 [cmdk-item]{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-item\\\\]\\\\]\\\\:py-3 [cmdk-item]{\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-item\\\\]_svg\\\\]\\\\:h-5 [cmdk-item] svg{\\n  height: 1.25rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-item\\\\]_svg\\\\]\\\\:w-5 [cmdk-item] svg{\\n  width: 1.25rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_p\\\\]\\\\:leading-relaxed p{\\n  line-height: 1.625;\\n}\\r\\n\\r\\n.\\\\[\\\\&_svg\\\\]\\\\:pointer-events-none svg{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.\\\\[\\\\&_svg\\\\]\\\\:size-4 svg{\\n  width: 1rem;\\n  height: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_svg\\\\]\\\\:shrink-0 svg{\\n  flex-shrink: 0;\\n}\\r\\n\\r\\n.\\\\[\\\\&_tr\\\\:last-child\\\\]\\\\:border-0 tr:last-child{\\n  border-width: 0px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_tr\\\\]\\\\:border-b tr{\\n  border-bottom-width: 1px;\\n}\\r\\n\\r\\n[data-side=left][data-collapsible=offcanvas] .\\\\[\\\\[data-side\\\\=left\\\\]\\\\[data-collapsible\\\\=offcanvas\\\\]_\\\\&\\\\]\\\\:-right-2{\\n  right: -0.5rem;\\n}\\r\\n\\r\\n[data-side=left][data-state=collapsed] .\\\\[\\\\[data-side\\\\=left\\\\]\\\\[data-state\\\\=collapsed\\\\]_\\\\&\\\\]\\\\:cursor-e-resize{\\n  cursor: e-resize;\\n}\\r\\n\\r\\n[data-side=left] .\\\\[\\\\[data-side\\\\=left\\\\]_\\\\&\\\\]\\\\:cursor-w-resize{\\n  cursor: w-resize;\\n}\\r\\n\\r\\n[data-side=right][data-collapsible=offcanvas] .\\\\[\\\\[data-side\\\\=right\\\\]\\\\[data-collapsible\\\\=offcanvas\\\\]_\\\\&\\\\]\\\\:-left-2{\\n  left: -0.5rem;\\n}\\r\\n\\r\\n[data-side=right][data-state=collapsed] .\\\\[\\\\[data-side\\\\=right\\\\]\\\\[data-state\\\\=collapsed\\\\]_\\\\&\\\\]\\\\:cursor-w-resize{\\n  cursor: w-resize;\\n}\\r\\n\\r\\n[data-side=right] .\\\\[\\\\[data-side\\\\=right\\\\]_\\\\&\\\\]\\\\:cursor-e-resize{\\n  cursor: e-resize;\\n}\\r\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/index.css\"],\"names\":[],\"mappings\":\";AACA,uGAAuG;AACvG,uGAAuG;;AAEvG;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;;CAAc;;AAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;IAAA,uBAAc;IAAd,4BAAc;;IAAd,iBAAc;IAAd,iCAAc;;IAAd,oBAAc;IAAd,oCAAc;;IAAd,4BAAc;IAAd,iCAAc;;IAAd,0BAAc;IAAd,yCAAc;;IAAd,sBAAc;IAAd,qCAAc;;IAAd,uBAAc;IAAd,sCAAc;;IAAd,4BAAc;IAAd,qCAAc;;IAAd,2BAAc;IAAd,0BAAc;IAAd,sBAAc;;IAAd,gBAAc;;IAAd,8BAAc;IAAd,oCAAc;IAAd,+BAAc;IAAd,sCAAc;IAAd,gCAAc;IAAd,yCAAc;IAAd,6BAAc;IAAd,iCAAc;EAAA;;AAAd;IAAA,4BAAc;IAAd,yBAAc;;IAAd,sBAAc;IAAd,8BAAc;;IAAd,yBAAc;IAAd,iCAAc;;IAAd,sBAAc;IAAd,uCAAc;;IAAd,8BAAc;IAAd,mCAAc;;IAAd,0BAAc;IAAd,mCAAc;;IAAd,2BAAc;IAAd,gCAAc;;IAAd,4BAAc;IAAd,qCAAc;;IAAd,2BAAc;IAAd,0BAAc;IAAd,yBAAc;IAAd,kCAAc;IAAd,oCAAc;IAAd,kCAAc;IAAd,uCAAc;IAAd,gCAAc;IAAd,2CAAc;IAAd,gCAAc;IAAd,iCAAc;EAAA;;AAAd;EAAA;AAAc;;AAAd;EAAA,wCAAc;EAAd;AAAc;AAEd;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,SAAmB;EAAnB;AAAmB;AAAnB;EAAA,QAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,oBAAmB;EAAnB,4BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,WAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yBAAmB;KAAnB,sBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,sEAAmB;EAAnB;AAAmB;AAAnB;EAAA,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,sEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kCAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iDAAmB;EAAnB,qDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,qCAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,yCAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,0BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA,mCAAmB;IAAnB;EAAmB;AAAA;AAAnB;;EAAA;IAAA,kCAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA,qBAAmB;EAAnB,yBAAmB;EAAnB,2BAAmB;EAAnB,yBAAmB;EAAnB,0BAAmB;EAAnB,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAEnB,4DAA4D;AAC5D;EACE;IACE,0BAA0B;IAC1B,2BAA2B;EAC7B;EACA;IACE,0BAA0B;IAC1B,6BAA6B;EAC/B;AACF;;AAEA;EACE;IACE,uCAAuC;EACzC;EACA;IACE,yCAAyC;EAC3C;EACA;IACE,yCAAyC;EAC3C;AACF;;AAEA;EACE;IACE,iCAAiC;EACnC;EACA;IACE,wCAAwC;EAC1C;AACF;;AAEA;EACE;IACE,YAAY;IACZ,mBAAmB;EACrB;EACA;IACE,YAAY;IACZ,sBAAsB;EACxB;AACF;;AAEA;EACE;IACE,UAAU;IACV,uCAAuC;EACzC;EACA;IACE,UAAU;IACV,iCAAiC;EACnC;AACF;;AAEA;EACE;IACE,UAAU;IACV,0CAA0C;EAC5C;EACA;IACE,UAAU;IACV,sCAAsC;EACxC;AACF;;AAEA;EACE,8DAA8D;AAChE;;AAEA;EACE,yDAAyD;AAC3D;;AAEA;EACE,+DAA+D;AACjE;;AAEA;EACE,8DAA8D;AAChE;;AAEA;EACE,oDAAoD;AACtD;;AAEA;EACE,0DAA0D;AAC5D;;AAEA,4BAA4B;AAC5B;EACE;;mEAEiE;EACjE,0BAA0B;AAC5B;;AAEA,8BAA8B;AAC9B;EACE,uBAAuB;EACvB,wBAAwB;AAC1B;;AAEA,gCAAgC;AAChC;EACE,gCAAgC;AAClC;;AAEA;EACE,gCAAgC;AAClC;;AAEA,+BAA+B;AAC/B;EACE,yDAAyD;AAC3D;;AAEA,8BAA8B;AAC9B;EACE,mCAAmC;EACnC,kCAAkC;AACpC;;AAEA,gDAAgD;AAChD;EACE,wBAAwB;EACxB,+BAA+B;EAC/B,2BAA2B;EAC3B,mBAAmB;AACrB;;AAEA,oCAAoC;AACpC;EACE;;;IAGE,qCAAqC;IACrC,uCAAuC;IACvC,sCAAsC;IACtC,gCAAgC;EAClC;;EAEA;IACE,0BAA0B;IAC1B,4BAA4B;EAC9B;AACF;;AAEA,qBAAqB;AACrB;EACE,UAAU;AACZ;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,oDAAoD;EACpD,kBAAkB;AACpB;;AAEA;EACE,oDAAoD;AACtD;;AAEA,mBAAmB;AACnB;EACE,mCAAmC;EACnC,cAAc;AAChB;AAHA;EACE,mCAAmC;EACnC,cAAc;AAChB;;AAEA,iBAAiB;AACjB;EACE,0BAA0B;EAC1B,mBAAmB;AACrB;;AAEA,uBAAuB;AACvB;EACE,oBAAoB;EACpB,qBAAqB;EACrB,4BAA4B;EAC5B,gBAAgB;AAClB;;AAEA,8BAA8B;AAC9B;EACE;;;oCAGkC;AACpC;;AAEA;EACE;;;oCAGkC;AACpC;;AAEA,uDAAuD;AACvD;EACE,yDAAyD;EACzD,+BAA+B;AACjC;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,uBAAuB;AACzB;;AAEA,8CAA8C;AAC9C;EACE,yDAAyD;EACzD,+BAA+B;AACjC;;AAEA,qCAAqC,qBAAqB,EAAE;AAC5D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,wBAAwB,EAAE;;AAE/D,oBAAoB;AACpB;EACE,+CAA+C;AACjD;;AAEA;EACE,gDAAgD;AAClD;;AAEA,sBAAsB;AACtB;EACE,UAAU;EACV,2BAA2B;AAC7B;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA;EACE,UAAU;EACV,4BAA4B;AAC9B;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA;EACE,UAAU;EACV,2BAA2B;AAC7B;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA;EACE,UAAU;EACV,qBAAqB;AACvB;;AAEA;EACE,UAAU;EACV,mBAAmB;AACrB;;AAEA,kDAAkD;AAClD;EACE,yDAAyD;EACzD,kCAAkC;AACpC;;AAEA;EACE,sDAAsD;EACtD;;sCAEoC;AACtC;;AAEA,qCAAqC;AACrC;EACE,gFAAgF;EAChF,0BAA0B;EAC1B,0EAA0E;EAC1E,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;EACrB,gCAAgC;AAClC;;AAEA;EACE;IACE,2BAA2B;EAC7B;EACA;IACE,6BAA6B;EAC/B;EACA;IACE,2BAA2B;EAC7B;AACF;;AAEA,qBAAqB;AACrB;EACE,gBAAgB;EAChB,+BAA+B;EAC/B,mBAAmB;EACnB,0EAA0E;AAC5E;;AAEA;EACE;IACE,QAAQ;EACV;EACA;IACE,WAAW;EACb;AACF;;AAEA;EACE;IACE,yBAAyB;EAC3B;EACA;IACE,qBAAqB;EACvB;AACF;;AAEA,6BAA6B;AAC7B;EACE,yDAAyD;EACzD,sBAAsB;AACxB;;AAEA;EACE,uCAAuC;EACvC,uBAAuB;AACzB;;AAEA,8BAA8B;AAC9B;EACE,yDAAyD;EACzD,oDAAoD;AACtD;;AAEA;EACE,uCAAuC;EACvC,2CAA2C;AAC7C;;AAEA;EACE,oCAAoC;EACpC,yBAAyB;AAC3B;;AAEA,kBAAkB;AAClB;EACE,kBAAkB;EAClB,6BAA6B;AAC/B;;AAEA;;EAEE,wBAAwB;EACxB,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;AACd;;AAEA;EACE,iCAAiC;EACjC,cAAc;EACd,WAAW;AACb;;AAEA;EACE,iCAAiC;EACjC,cAAc;EACd,WAAW;AACb;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,+BAA+B;EACjC;EACA;IACE,gCAAgC;EAClC;EACA;IACE,8BAA8B;EAChC;EACA;IACE,+BAA+B;EACjC;AACF;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,+BAA+B;EACjC;EACA;IACE,gCAAgC;EAClC;EACA;IACE,8BAA8B;EAChC;EACA;IACE,+BAA+B;EACjC;AACF;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,+BAA+B;EACjC;EACA;IACE,8BAA8B;EAChC;EACA;IACE,gCAAgC;EAClC;EACA;IACE,+BAA+B;EACjC;AACF;;AAEA,gBAAgB;AAChB;EACE,2BAA2B;AAC7B;;AAEA;EACE,mBAAmB;AACrB;;AAEA,sBAAsB;AACtB;EACE,uBAAuB;EACvB,oDAAoD;EACpD,6DAA6D;AAC/D;;AAEA;EACE,uBAAuB;EACvB,oDAAoD;EACpD,6DAA6D;AAC/D;;AAEA;EACE,uBAAuB;EACvB,sDAAsD;EACtD,+DAA+D;AACjE;;AAEA,sBAAsB;AACtB;EACE,WAAW;AACb;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,SAAS;AACX;;AAEA,qBAAqB;AACrB;EACE,eAAe;EACf,mBAAmB;AACrB;;AAEA;EACE,cAAc;EACd,oBAAoB;AACtB;;AAEA;EACE,eAAe;EACf,oBAAoB;AACtB;;AAEA;EACE,cAAc;EACd,qBAAqB;AACvB;;AAEA,uBAAuB;AACvB;EACE,iBAAiB;AACnB;;AAEA;EACE,gBAAgB;AAClB;;AAEA,cAAc;AACd;EACE,YAAY;AACd;;AAEA;EACE,WAAW;AACb;;AAEA,oBAAoB;AACpB;EACE,UAAU;EACV,QAAQ;AACV;;AAEA;EACE,WAAW;EACX,OAAO;AACT;;AAEA,sBAAsB;AACtB;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,gCAAgC;EAChC,mCAAmC;AACrC;;AAEA;EACE,0BAA0B;EAC1B,6BAA6B;EAC7B,+BAA+B;EAC/B,kCAAkC;AACpC;;AAEA,+FAA+F;;AA6F/F,oCAAoC;AACpC;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,0BAA0B;EAC5B;EACA;IACE,4BAA4B;EAC9B;AACF;;AAEA;EACE;IACE,wBAAwB;EAC1B;EACA;IACE,4BAA4B;EAC9B;AACF;;AAEA;EACE;IACE,YAAY;EACd;EACA;IACE,YAAY;EACd;AACF;;AAEA;EACE;IACE,0BAA0B;IAC1B,gCAAgC;EAClC;EACA;IACE,0BAA0B;IAC1B,iCAAiC;EACnC;AACF;;AAEA;EACE;IACE,oBAAoB;EACtB;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE,yCAAyC;AAC3C;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,sCAAsC;AACxC;;AAEA,2BAA2B;AAC3B;EACE,uBAAuB;AACzB;;AAEA,4BAA4B;AAC5B;EACE;;gEAE8D;EAC9D,0BAA0B;AAC5B;;AAEA,uBAAuB;AACvB;EACE,6DAA6D;EAC7D,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;AACvB;;AAEA,yBAAyB;AACzB;EACE,qCAAqC;EACrC,mCAA2B;UAA3B,2BAA2B;EAC3B,2CAA2C;AAC7C;;AAEA,qBAAqB;AACrB;EACE,UAAU;AACZ;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,oDAAoD;EACpD,kBAAkB;AACpB;;AAEA;EACE,oDAAoD;AACtD;;AA1xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,mBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA,QA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA,sBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,iDA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,kDA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,kBA2xBA;EA3xBA,kBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,iBA2xBA;EA3xBA,iBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,kBA2xBA;EA3xBA,kBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,kBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,kBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,kBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,kBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,kBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,4DA2xBA;EA3xBA,mEA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,4DA2xBA;EA3xBA,mEA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,gDA2xBA;EA3xBA,6DA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,iDA2xBA;EA3xBA,qDA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,gFA2xBA;EA3xBA,oGA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,sBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,8BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,2GA2xBA;EA3xBA,yGA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,8BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,2GA2xBA;EA3xBA,yGA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,2GA2xBA;EA3xBA,yGA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,kBA2xBA;EA3xBA,kBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,iBA2xBA;EA3xBA,iBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,+EA2xBA;EA3xBA,mGA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,yBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,yBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,yBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,qBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,qBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,gDA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,iDA2xBA;EA3xBA;AA2xBA;;AA3xBA;;EAAA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;AAAA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;;EAAA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;AAAA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,0CA2xBA;EA3xBA,uDA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,qBA2xBA;EA3xBA,yBA2xBA;EA3xBA,2BA2xBA;EA3xBA,yBA2xBA;EA3xBA,0BA2xBA;EA3xBA,+BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,qBA2xBA;EA3xBA,yBA2xBA;EA3xBA,2BA2xBA;EA3xBA,yBA2xBA;EA3xBA,0BA2xBA;EA3xBA,+BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,qBA2xBA;EA3xBA,yBA2xBA;EA3xBA,2BA2xBA;EA3xBA,yBA2xBA;EA3xBA,0BA2xBA;EA3xBA,+BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA,yBA2xBA;EA3xBA,0BA2xBA;EA3xBA,wBA2xBA;EA3xBA,yBA2xBA;EA3xBA,8BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA,yBA2xBA;EA3xBA,0BA2xBA;EA3xBA,wBA2xBA;EA3xBA,yBA2xBA;EA3xBA,8BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA,yBA2xBA;EA3xBA,0BA2xBA;EA3xBA,wBA2xBA;EA3xBA,yBA2xBA;EA3xBA,8BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA,yBA2xBA;EA3xBA,0BA2xBA;EA3xBA,wBA2xBA;EA3xBA,yBA2xBA;EA3xBA,8BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA,sBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA,qBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,sBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,qBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,mBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,mBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,0EA2xBA;EA3xBA,8FA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,0BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;;EAAA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA,uBA2xBA;IA3xBA,sDA2xBA;IA3xBA;EA2xBA;;EA3xBA;IAAA,uBA2xBA;IA3xBA,oDA2xBA;IA3xBA;EA2xBA;;EA3xBA;IAAA,uBA2xBA;IA3xBA,2DA2xBA;IA3xBA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA,qBA2xBA;IA3xBA;EA2xBA;;EA3xBA;IAAA,oBA2xBA;IA3xBA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;AAAA;;AA3xBA;;EAAA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA,kBA2xBA;IA3xBA;EA2xBA;;EA3xBA;IAAA,eA2xBA;IA3xBA;EA2xBA;;EA3xBA;IAAA,kBA2xBA;IA3xBA;EA2xBA;;EA3xBA;IAAA,mBA2xBA;IA3xBA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA,0BA2xBA;IA3xBA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA,0EA2xBA;IA3xBA,8FA2xBA;IA3xBA;EA2xBA;AAAA;;AA3xBA;;EAAA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA;EA2xBA;;EA3xBA;IAAA,kBA2xBA;IA3xBA;EA2xBA;;EA3xBA;IAAA,iBA2xBA;IA3xBA;EA2xBA;AAAA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,iDA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,kDA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,kDA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,gBA2xBA;EA3xBA,uBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,gBA2xBA;EA3xBA,oBA2xBA;EA3xBA,4BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,sBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,eA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,WA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,kBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,mBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,8BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,8BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,8BA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,qBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,kBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA,oBA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA,WA2xBA;EA3xBA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA;;AA3xBA;EAAA;AA2xBA\",\"sourcesContent\":[\"\\r\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');\\r\\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');\\r\\n\\r\\n@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n\\r\\n/* Custom animations and effects - Enhanced for smoothness */\\r\\n@keyframes gradient-x {\\r\\n  0%, 100% {\\r\\n    background-size: 400% 400%;\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-size: 400% 400%;\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes float {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0px) rotate(0deg);\\r\\n  }\\r\\n  33% {\\r\\n    transform: translateY(-15px) rotate(1deg);\\r\\n  }\\r\\n  66% {\\r\\n    transform: translateY(-8px) rotate(-1deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes bounce-slow {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0) scale(1);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-12px) scale(1.02);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes pulse-slow {\\r\\n  0%, 100% {\\r\\n    opacity: 0.4;\\r\\n    transform: scale(1);\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 0.9;\\r\\n    transform: scale(1.05);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes fade-in {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(40px) scale(0.95);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0) scale(1);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes slide-in-smooth {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(60px) rotateX(10deg);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0) rotateX(0deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-gradient-x {\\r\\n  animation: gradient-x 6s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\r\\n}\\r\\n\\r\\n.animate-float {\\r\\n  animation: float 8s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\r\\n}\\r\\n\\r\\n.animate-bounce-slow {\\r\\n  animation: bounce-slow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\r\\n}\\r\\n\\r\\n.animate-pulse-slow {\\r\\n  animation: pulse-slow 6s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\r\\n}\\r\\n\\r\\n.animate-fade-in {\\r\\n  animation: fade-in 1.2s cubic-bezier(0.4, 0, 0.2, 1);\\r\\n}\\r\\n\\r\\n.animate-slide-in-smooth {\\r\\n  animation: slide-in-smooth 1s cubic-bezier(0.4, 0, 0.2, 1);\\r\\n}\\r\\n\\r\\n/* Grid pattern background */\\r\\n.bg-grid-pattern {\\r\\n  background-image:\\r\\n    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),\\r\\n    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n}\\r\\n\\r\\n/* Enhanced smooth scrolling */\\r\\nhtml {\\r\\n  scroll-behavior: smooth;\\r\\n  scroll-padding-top: 80px;\\r\\n}\\r\\n\\r\\n/* Language transition effects */\\r\\n[dir=\\\"rtl\\\"] {\\r\\n  transition: all 0.3s ease-in-out;\\r\\n}\\r\\n\\r\\n[dir=\\\"ltr\\\"] {\\r\\n  transition: all 0.3s ease-in-out;\\r\\n}\\r\\n\\r\\n/* Text direction transitions */\\r\\n.text-transition {\\r\\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n}\\r\\n\\r\\n/* Performance optimizations */\\r\\n* {\\r\\n  -webkit-font-smoothing: antialiased;\\r\\n  -moz-osx-font-smoothing: grayscale;\\r\\n}\\r\\n\\r\\n/* Hardware acceleration for smooth animations */\\r\\n.animate-gpu {\\r\\n  transform: translateZ(0);\\r\\n  will-change: transform, opacity;\\r\\n  backface-visibility: hidden;\\r\\n  perspective: 1000px;\\r\\n}\\r\\n\\r\\n/* Reduce motion for accessibility */\\r\\n@media (prefers-reduced-motion: reduce) {\\r\\n  *,\\r\\n  *::before,\\r\\n  *::after {\\r\\n    animation-duration: 0.01ms !important;\\r\\n    animation-iteration-count: 1 !important;\\r\\n    transition-duration: 0.01ms !important;\\r\\n    scroll-behavior: auto !important;\\r\\n  }\\r\\n\\r\\n  .animate-gpu {\\r\\n    transform: none !important;\\r\\n    will-change: auto !important;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: #f1f1f1;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: linear-gradient(45deg, #3b82f6, #8b5cf6);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: linear-gradient(45deg, #2563eb, #7c3aed);\\r\\n}\\r\\n\\r\\n/* Text selection */\\r\\n::selection {\\r\\n  background: rgba(59, 130, 246, 0.3);\\r\\n  color: inherit;\\r\\n}\\r\\n\\r\\n/* Focus styles */\\r\\n.focus-visible:focus {\\r\\n  outline: 2px solid #3b82f6;\\r\\n  outline-offset: 2px;\\r\\n}\\r\\n\\r\\n/* Line clamp utility */\\r\\n.line-clamp-3 {\\r\\n  display: -webkit-box;\\r\\n  -webkit-line-clamp: 3;\\r\\n  -webkit-box-orient: vertical;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n/* Custom avatar glow effect */\\r\\n.avatar-glow {\\r\\n  box-shadow:\\r\\n    0 0 20px rgba(59, 130, 246, 0.3),\\r\\n    0 0 40px rgba(139, 92, 246, 0.2),\\r\\n    0 0 60px rgba(236, 72, 153, 0.1);\\r\\n}\\r\\n\\r\\n.avatar-glow:hover {\\r\\n  box-shadow:\\r\\n    0 0 30px rgba(59, 130, 246, 0.4),\\r\\n    0 0 60px rgba(139, 92, 246, 0.3),\\r\\n    0 0 90px rgba(236, 72, 153, 0.2);\\r\\n}\\r\\n\\r\\n/* Scroll Animation Classes - Enhanced for smoothness */\\r\\n.scroll-animate {\\r\\n  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, opacity;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-100 {\\r\\n  transition-delay: 150ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-200 {\\r\\n  transition-delay: 300ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-300 {\\r\\n  transition-delay: 450ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-400 {\\r\\n  transition-delay: 600ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-500 {\\r\\n  transition-delay: 750ms;\\r\\n}\\r\\n\\r\\n/* Stagger animation for children - Enhanced */\\r\\n.stagger-children > * {\\r\\n  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, opacity;\\r\\n}\\r\\n\\r\\n.stagger-children > *:nth-child(1) { transition-delay: 0ms; }\\r\\n.stagger-children > *:nth-child(2) { transition-delay: 150ms; }\\r\\n.stagger-children > *:nth-child(3) { transition-delay: 300ms; }\\r\\n.stagger-children > *:nth-child(4) { transition-delay: 450ms; }\\r\\n.stagger-children > *:nth-child(5) { transition-delay: 600ms; }\\r\\n.stagger-children > *:nth-child(6) { transition-delay: 750ms; }\\r\\n.stagger-children > *:nth-child(7) { transition-delay: 900ms; }\\r\\n.stagger-children > *:nth-child(8) { transition-delay: 1050ms; }\\r\\n\\r\\n/* Parallax effect */\\r\\n.parallax-slow {\\r\\n  transform: translateY(var(--scroll-y, 0) * 0.5);\\r\\n}\\r\\n\\r\\n.parallax-fast {\\r\\n  transform: translateY(var(--scroll-y, 0) * -0.3);\\r\\n}\\r\\n\\r\\n/* Reveal animations */\\r\\n.reveal-up {\\r\\n  opacity: 0;\\r\\n  transform: translateY(50px);\\r\\n}\\r\\n\\r\\n.reveal-up.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.reveal-left {\\r\\n  opacity: 0;\\r\\n  transform: translateX(-50px);\\r\\n}\\r\\n\\r\\n.reveal-left.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateX(0);\\r\\n}\\r\\n\\r\\n.reveal-right {\\r\\n  opacity: 0;\\r\\n  transform: translateX(50px);\\r\\n}\\r\\n\\r\\n.reveal-right.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateX(0);\\r\\n}\\r\\n\\r\\n.reveal-scale {\\r\\n  opacity: 0;\\r\\n  transform: scale(0.8);\\r\\n}\\r\\n\\r\\n.reveal-scale.revealed {\\r\\n  opacity: 1;\\r\\n  transform: scale(1);\\r\\n}\\r\\n\\r\\n/* Hover effects for cards - Enhanced smoothness */\\r\\n.card-hover {\\r\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, box-shadow;\\r\\n}\\r\\n\\r\\n.card-hover:hover {\\r\\n  transform: translateY(-12px) scale(1.03) rotateX(2deg);\\r\\n  box-shadow:\\r\\n    0 32px 64px -12px rgba(0, 0, 0, 0.25),\\r\\n    0 0 0 1px rgba(255, 255, 255, 0.1);\\r\\n}\\r\\n\\r\\n/* Enhanced gradient text animation */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #ec4899, #10b981, #f59e0b);\\r\\n  background-size: 400% 400%;\\r\\n  animation: gradient-shift 8s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  will-change: background-position;\\r\\n}\\r\\n\\r\\n@keyframes gradient-shift {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Typing animation */\\r\\n.typing-animation {\\r\\n  overflow: hidden;\\r\\n  border-right: 2px solid #3b82f6;\\r\\n  white-space: nowrap;\\r\\n  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;\\r\\n}\\r\\n\\r\\n@keyframes typing {\\r\\n  from {\\r\\n    width: 0;\\r\\n  }\\r\\n  to {\\r\\n    width: 100%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes blink-caret {\\r\\n  from, to {\\r\\n    border-color: transparent;\\r\\n  }\\r\\n  50% {\\r\\n    border-color: #3b82f6;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Enhanced magnetic effect */\\r\\n.magnetic {\\r\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform;\\r\\n}\\r\\n\\r\\n.magnetic:hover {\\r\\n  transform: scale(1.08) translateY(-2px);\\r\\n  filter: brightness(1.1);\\r\\n}\\r\\n\\r\\n/* Smooth button transitions */\\r\\n.btn-smooth {\\r\\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, box-shadow, background-color;\\r\\n}\\r\\n\\r\\n.btn-smooth:hover {\\r\\n  transform: translateY(-2px) scale(1.02);\\r\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\\r\\n}\\r\\n\\r\\n.btn-smooth:active {\\r\\n  transform: translateY(0) scale(0.98);\\r\\n  transition-duration: 0.1s;\\r\\n}\\r\\n\\r\\n/* Glitch effect */\\r\\n.glitch {\\r\\n  position: relative;\\r\\n  animation: glitch 2s infinite;\\r\\n}\\r\\n\\r\\n.glitch::before,\\r\\n.glitch::after {\\r\\n  content: attr(data-text);\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n}\\r\\n\\r\\n.glitch::before {\\r\\n  animation: glitch-1 0.5s infinite;\\r\\n  color: #ff0000;\\r\\n  z-index: -1;\\r\\n}\\r\\n\\r\\n.glitch::after {\\r\\n  animation: glitch-2 0.5s infinite;\\r\\n  color: #00ff00;\\r\\n  z-index: -2;\\r\\n}\\r\\n\\r\\n@keyframes glitch {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(-2px, 2px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(-2px, -2px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(2px, 2px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(2px, -2px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes glitch-1 {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(-1px, 1px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(-1px, -1px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(1px, 1px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(1px, -1px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes glitch-2 {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(1px, -1px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(1px, 1px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(-1px, -1px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(-1px, 1px);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* RTL Support */\\r\\n[dir=\\\"rtl\\\"] .flex-row {\\r\\n  flex-direction: row-reverse;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .flex-row-reverse {\\r\\n  flex-direction: row;\\r\\n}\\r\\n\\r\\n/* RTL Space between */\\r\\n[dir=\\\"rtl\\\"] .space-x-8 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n/* RTL Gap utilities */\\r\\n[dir=\\\"rtl\\\"] .gap-2 {\\r\\n  gap: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .gap-3 {\\r\\n  gap: 0.75rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .gap-4 {\\r\\n  gap: 1rem;\\r\\n}\\r\\n\\r\\n/* RTL Icon spacing */\\r\\n[dir=\\\"rtl\\\"] .mr-2 {\\r\\n  margin-right: 0;\\r\\n  margin-left: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .ml-2 {\\r\\n  margin-left: 0;\\r\\n  margin-right: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .mr-3 {\\r\\n  margin-right: 0;\\r\\n  margin-left: 0.75rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .ml-3 {\\r\\n  margin-left: 0;\\r\\n  margin-right: 0.75rem;\\r\\n}\\r\\n\\r\\n/* RTL Text alignment */\\r\\n[dir=\\\"rtl\\\"] .text-left {\\r\\n  text-align: right;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .text-right {\\r\\n  text-align: left;\\r\\n}\\r\\n\\r\\n/* RTL Float */\\r\\n[dir=\\\"rtl\\\"] .float-left {\\r\\n  float: right;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .float-right {\\r\\n  float: left;\\r\\n}\\r\\n\\r\\n/* RTL Positioning */\\r\\n[dir=\\\"rtl\\\"] .left-0 {\\r\\n  left: auto;\\r\\n  right: 0;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .right-0 {\\r\\n  right: auto;\\r\\n  left: 0;\\r\\n}\\r\\n\\r\\n/* RTL Border radius */\\r\\n[dir=\\\"rtl\\\"] .rounded-l {\\r\\n  border-top-left-radius: 0;\\r\\n  border-bottom-left-radius: 0;\\r\\n  border-top-right-radius: 0.25rem;\\r\\n  border-bottom-right-radius: 0.25rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .rounded-r {\\r\\n  border-top-right-radius: 0;\\r\\n  border-bottom-right-radius: 0;\\r\\n  border-top-left-radius: 0.25rem;\\r\\n  border-bottom-left-radius: 0.25rem;\\r\\n}\\r\\n\\r\\n/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */\\r\\n\\r\\n@layer base {\\r\\n  :root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --primary: 222.2 47.4% 11.2%;\\r\\n    --primary-foreground: 210 40% 98%;\\r\\n\\r\\n    --secondary: 210 40% 96.1%;\\r\\n    --secondary-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --muted: 210 40% 96.1%;\\r\\n    --muted-foreground: 215.4 16.3% 46.9%;\\r\\n\\r\\n    --accent: 210 40% 96.1%;\\r\\n    --accent-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 210 40% 98%;\\r\\n\\r\\n    --border: 214.3 31.8% 91.4%;\\r\\n    --input: 214.3 31.8% 91.4%;\\r\\n    --ring: 222.2 84% 4.9%;\\r\\n\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    --sidebar-background: 0 0% 98%;\\r\\n    --sidebar-foreground: 240 5.3% 26.1%;\\r\\n    --sidebar-primary: 240 5.9% 10%;\\r\\n    --sidebar-primary-foreground: 0 0% 98%;\\r\\n    --sidebar-accent: 240 4.8% 95.9%;\\r\\n    --sidebar-accent-foreground: 240 5.9% 10%;\\r\\n    --sidebar-border: 220 13% 91%;\\r\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\r\\n  }\\r\\n\\r\\n  .dark {\\r\\n    --background: 222.2 84% 4.9%;\\r\\n    --foreground: 210 40% 98%;\\r\\n\\r\\n    --card: 222.2 84% 4.9%;\\r\\n    --card-foreground: 210 40% 98%;\\r\\n\\r\\n    --popover: 222.2 84% 4.9%;\\r\\n    --popover-foreground: 210 40% 98%;\\r\\n\\r\\n    --primary: 210 40% 98%;\\r\\n    --primary-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --secondary: 217.2 32.6% 17.5%;\\r\\n    --secondary-foreground: 210 40% 98%;\\r\\n\\r\\n    --muted: 217.2 32.6% 17.5%;\\r\\n    --muted-foreground: 215 20.2% 65.1%;\\r\\n\\r\\n    --accent: 217.2 32.6% 17.5%;\\r\\n    --accent-foreground: 210 40% 98%;\\r\\n\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 210 40% 98%;\\r\\n\\r\\n    --border: 217.2 32.6% 17.5%;\\r\\n    --input: 217.2 32.6% 17.5%;\\r\\n    --ring: 212.7 26.8% 83.9%;\\r\\n    --sidebar-background: 240 5.9% 10%;\\r\\n    --sidebar-foreground: 240 4.8% 95.9%;\\r\\n    --sidebar-primary: 224.3 76.3% 48%;\\r\\n    --sidebar-primary-foreground: 0 0% 100%;\\r\\n    --sidebar-accent: 240 3.7% 15.9%;\\r\\n    --sidebar-accent-foreground: 240 4.8% 95.9%;\\r\\n    --sidebar-border: 240 3.7% 15.9%;\\r\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@layer base {\\r\\n  * {\\r\\n    @apply border-border;\\r\\n  }\\r\\n\\r\\n  body {\\r\\n    @apply bg-background text-foreground;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Enhanced animations and effects */\\r\\n@keyframes fade-in {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(30px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes float {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-20px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes bounce-slow {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-25px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes pulse-slow {\\r\\n  0%, 100% {\\r\\n    opacity: 0.4;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 0.8;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes gradient-x {\\r\\n  0%, 100% {\\r\\n    background-size: 200% 200%;\\r\\n    background-position: left center;\\r\\n  }\\r\\n  50% {\\r\\n    background-size: 200% 200%;\\r\\n    background-position: right center;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes scale-x-100 {\\r\\n  from {\\r\\n    transform: scaleX(0);\\r\\n  }\\r\\n  to {\\r\\n    transform: scaleX(1);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-fade-in {\\r\\n  animation: fade-in 0.8s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-float {\\r\\n  animation: float 6s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-bounce-slow {\\r\\n  animation: bounce-slow 3s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-pulse-slow {\\r\\n  animation: pulse-slow 4s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-gradient-x {\\r\\n  animation: gradient-x 3s ease infinite;\\r\\n}\\r\\n\\r\\n/* Smooth scroll behavior */\\r\\nhtml {\\r\\n  scroll-behavior: smooth;\\r\\n}\\r\\n\\r\\n/* Grid pattern background */\\r\\n.bg-grid-pattern {\\r\\n  background-image: \\r\\n    linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),\\r\\n    linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n}\\r\\n\\r\\n/* Enhanced gradients */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Glassmorphism effect */\\r\\n.glass-effect {\\r\\n  background: rgba(255, 255, 255, 0.25);\\r\\n  backdrop-filter: blur(10px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.18);\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: #f1f1f1;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: linear-gradient(45deg, #667eea, #764ba2);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: linear-gradient(45deg, #5a6fd8, #6a419a);\\r\\n}\\r\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/index.css\n"));

/***/ })

});