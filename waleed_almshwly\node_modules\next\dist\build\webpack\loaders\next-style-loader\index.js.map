{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-style-loader/index.ts"], "names": ["loaderApi", "pitch", "loader", "request", "loaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "options", "getOptions", "insert", "JSON", "stringify", "toString", "injectType", "esModule", "hmrCode", "hot", "stringifyRequest", "path", "join", "__dirname", "isSingleton", "require", "module", "exports"], "mappings": ";;;;6DAAiB;kCACgB;;;;;;AAEjC,MAAMA,YAAY,KAAO;AAEzBA,UAAUC,KAAK,GAAG,SAASC,OAAkBC,OAAY;IACvD,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IAEpD,OAAOF,WAAWG,OAAO,CAAC;QACxB,MAAMC,UAAU,IAAI,CAACC,UAAU;QAE/B,MAAMC,SACJ,OAAOF,QAAQE,MAAM,KAAK,cACtB,WACA,OAAOF,QAAQE,MAAM,KAAK,WAC1BC,KAAKC,SAAS,CAACJ,QAAQE,MAAM,IAC7BF,QAAQE,MAAM,CAACG,QAAQ;QAC7B,MAAMC,aAAaN,QAAQM,UAAU,IAAI;QACzC,MAAMC,WACJ,OAAOP,QAAQO,QAAQ,KAAK,cAAcP,QAAQO,QAAQ,GAAG;QAE/D,OAAOP,QAAQO,QAAQ;QAEvB,OAAQD;YACN,KAAK;gBAAW;oBACd,MAAME,UAAU,IAAI,CAACC,GAAG,GACpB,CAAC;;;IAGT,EAAEC,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,QAAQ,CAAC,EAAE;;KAExC,EACEY,WACI,qBACA,CAAC,kBAAkB,EAAEG,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,QAAQ,CAAC,EAAE;;;;2BAI5C,CAAC,CACtB;;;;;;;CAOL,CAAC,GACU;oBAEJ,OAAO,CAAC,EACNY,WACI,CAAC,gBAAgB,EAAEG,IAAAA,kCAAgB,EACjC,IAAI,EACJ,CAAC,CAAC,EAAEC,aAAI,CAACC,IAAI,CAACC,WAAW,sCAAsC,CAAC,EAChE;gCACgB,EAAEH,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,QAAQ,CAAC,EAAE,CAAC,CAAC,GAC7D,CAAC,kBAAkB,EAAEe,IAAAA,kCAAgB,EACnC,IAAI,EACJ,CAAC,CAAC,EAAEC,aAAI,CAACC,IAAI,CAACC,WAAW,sCAAsC,CAAC,EAChE;kCACkB,EAAEH,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,QAAQ,CAAC,EAAE;;qEAEN,CAAC,CAC7D;;cAEK,EAAEQ,KAAKC,SAAS,CAACJ,SAAS;;iBAEvB,EAAEE,OAAO;;;;AAI1B,EAAEM,QAAQ;;AAEV,EAAED,WAAW,sBAAsB,GAAG,CAAC;gBACjC;YAEA,KAAK;YACL,KAAK;gBAAyB;oBAC5B,MAAMO,cAAcR,eAAe;oBAEnC,MAAME,UAAU,IAAI,CAACC,GAAG,GACpB,CAAC;;;wBAGW,EAAEM,QAAQ,2BAA2BV,QAAQ,GAAG;;;;;MAKlE,EAAEK,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,QAAQ,CAAC,EAAE;;QAEvC,EACEY,WACI,CAAC;;;;;;;;;;eAUA,CAAC,GACF,CAAC,kBAAkB,EAAEG,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,QAAQ,CAAC,EAAE;;;;;;;;;;;;;;eAc3D,CAAC,CACP;;;;;;;;;;CAUR,CAAC,GACU;oBAEJ,OAAO,CAAC,EACNY,WACI,CAAC,gBAAgB,EAAEG,IAAAA,kCAAgB,EACjC,IAAI,EACJ,CAAC,CAAC,EAAEC,aAAI,CAACC,IAAI,CACXC,WACA,uCACA,CAAC,EACH;gCACgB,EAAEH,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,QAAQ,CAAC,EAAE,CAAC,CAAC,GAC7D,CAAC,kBAAkB,EAAEe,IAAAA,kCAAgB,EACnC,IAAI,EACJ,CAAC,CAAC,EAAEC,aAAI,CAACC,IAAI,CACXC,WACA,uCACA,CAAC,EACH;kCACkB,EAAEH,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,QAAQ,CAAC,EAAE;;;;;;aAM9D,CAAC,CACL;;;;cAIK,EAAEQ,KAAKC,SAAS,CAACJ,SAAS;;iBAEvB,EAAEE,OAAO;oBACN,EAAEY,YAAY;;;;;;;;;;;;;;;;;;;AAmBlC,EAAEN,QAAQ;;AAEV,EAAED,WAAW,mBAAmB,mBAAmB,UAAU,CAAC;gBACxD;YAEA,KAAK;YACL,KAAK;YACL;gBAAS;oBACP,MAAMO,cAAcR,eAAe;oBAEnC,MAAME,UAAU,IAAI,CAACC,GAAG,GACpB,CAAC;;;wBAGW,EAAEM,QAAQ,2BAA2BV,QAAQ,GAAG;;;;MAIlE,EAAEK,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,QAAQ,CAAC,EAAE;;QAEvC,EACEY,WACI,CAAC;;;;;;;;8BAQe,CAAC,GACjB,CAAC,kBAAkB,EAAEG,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,QAAQ,CAAC,EAAE;;;;;;;;;;;;;;;;8BAgB5C,CAAC,CACtB;;;;;;;;CAQR,CAAC,GACU;oBAEJ,OAAO,CAAC,EACNY,WACI,CAAC,gBAAgB,EAAEG,IAAAA,kCAAgB,EACjC,IAAI,EACJ,CAAC,CAAC,EAAEC,aAAI,CAACC,IAAI,CACXC,WACA,uCACA,CAAC,EACH;gCACgB,EAAEH,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,QAAQ,CAAC,EAAE,CAAC,CAAC,GAC7D,CAAC,kBAAkB,EAAEe,IAAAA,kCAAgB,EACnC,IAAI,EACJ,CAAC,CAAC,EAAEC,aAAI,CAACC,IAAI,CACXC,WACA,uCACA,CAAC,EACH;kCACkB,EAAEH,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,QAAQ,CAAC,EAAE;;;;;;aAM9D,CAAC,CACL;;cAEK,EAAEQ,KAAKC,SAAS,CAACJ,SAAS;;iBAEvB,EAAEE,OAAO;oBACN,EAAEY,YAAY;;;;AAIlC,EAAEN,QAAQ;;AAEV,EAAED,WAAW,mBAAmB,mBAAmB,sBAAsB,CAAC;gBACpE;QACF;IACF;AACF;AAEAS,OAAOC,OAAO,GAAGzB"}