import { useTranslation } from "@/hooks/useTranslation";
import { useScrollReveal, getAnimationClass } from "@/hooks/useScrollReveal";

export const About = () => {
  const { t } = useTranslation();
  const { elementRef: aboutRef, isVisible: aboutVisible } = useScrollReveal();

  return (
    <section id="about" className="py-20 bg-gray-50 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-10 right-10 w-32 h-32 bg-gradient-to-br from-blue-400/5 to-purple-400/5 rounded-full blur-2xl"></div>

      <div ref={aboutRef} className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`text-center mb-12 ${getAnimationClass('fadeInUp', aboutVisible, 1)}`}>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{t("aboutMe")}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t("aboutSubtitle")}
          </p>
        </div>

        <div className="max-w-3xl mx-auto text-center">
          <p className={`text-gray-600 mb-8 leading-relaxed text-lg ${getAnimationClass('fadeInUp', aboutVisible, 2)}`}>
            {t("aboutDescription1")}
          </p>
          <p className={`text-gray-600 mb-8 leading-relaxed text-lg ${getAnimationClass('fadeInUp', aboutVisible, 3)}`}>
            {t("aboutDescription2")}
          </p>

          <div className={`grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 ${getAnimationClass('fadeInUp', aboutVisible, 4)}`}>
            <div className="text-center hover-lift p-4 rounded-lg bg-white/50 backdrop-blur-sm">
              <div className="text-3xl font-bold text-gray-900 mb-1 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">5+</div>
              <div className="text-sm text-gray-600">{t("yearsExperience")}</div>
            </div>
            <div className="text-center hover-lift p-4 rounded-lg bg-white/50 backdrop-blur-sm">
              <div className="text-3xl font-bold text-gray-900 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">50+</div>
              <div className="text-sm text-gray-600">{t("projectsCompleted")}</div>
            </div>
            <div className="text-center hover-lift p-4 rounded-lg bg-white/50 backdrop-blur-sm">
              <div className="text-3xl font-bold text-gray-900 mb-1 bg-gradient-to-r from-pink-600 to-red-600 bg-clip-text text-transparent">30+</div>
              <div className="text-sm text-gray-600">{t("happyClients")}</div>
            </div>
            <div className="text-center hover-lift p-4 rounded-lg bg-white/50 backdrop-blur-sm">
              <div className="text-3xl font-bold text-gray-900 mb-1 bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">15+</div>
              <div className="text-sm text-gray-600">{t("awards")}</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
