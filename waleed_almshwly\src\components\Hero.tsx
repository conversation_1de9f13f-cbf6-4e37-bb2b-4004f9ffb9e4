import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { ArrowDown, Mail, Download, Github, Linkedin, Sparkles } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";
import { useRTL } from "@/hooks/useRTL";
import { useScrollReveal, getAnimationClass } from "@/hooks/useScrollReveal";

export const Hero = () => {
  const { t } = useTranslation();
  const { iconLeft, isRTL } = useRTL();
  const { elementRef: heroRef, isVisible: heroVisible } = useScrollReveal();

  return (
    <section id="home" className="pt-20 min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-20 w-48 h-48 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-blue-400/5 to-purple-400/5 rounded-full blur-3xl animate-pulse"></div>
      </div>
      
      <div ref={heroRef} className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <div className={`mb-8 ${getAnimationClass('scaleIn', heroVisible, 1)}`}>
          <div className="relative">
            <Avatar className="w-32 h-32 mx-auto mb-6 ring-4 ring-white shadow-2xl hover-lift hover-glow transition-all duration-500">
              <AvatarImage src="/lovable-uploads/6d3fd0a4-5be1-42cf-ada4-ae04ab1679d3.png" alt="Waleed Almshwly" />
              <AvatarFallback className="text-2xl font-semibold bg-gradient-to-br from-blue-600 to-purple-600 text-white">
                WA
              </AvatarFallback>
            </Avatar>
            <Sparkles className="absolute top-2 right-2 w-8 h-8 text-yellow-400 animate-pulse" />
          </div>
        </div>
        
        <h1 className={`text-4xl md:text-6xl font-bold mb-6 ${getAnimationClass('fadeInUp', heroVisible, 2)}`}>
          <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-gradient">
            {t("fullStackDeveloper")}
          </span>
        </h1>
        
        <p className={`text-lg md:text-xl text-gray-600 mb-10 max-w-2xl mx-auto leading-relaxed ${getAnimationClass('fadeInUp', heroVisible, 3)}`}>
          {t("heroDescription")}
        </p>

        <div className={`flex flex-col sm:flex-row gap-4 justify-center items-center mb-8 ${isRTL ? 'sm:flex-row-reverse' : ''} ${getAnimationClass('fadeInUp', heroVisible, 4)}`}>
          <Button size="lg" className="bg-gray-900 hover:bg-gray-800 text-white hover-lift hover-slide">
            <a href="#projects" className="flex items-center gap-2">
              {t("viewMyWork")}
            </a>
          </Button>
          <Button variant="outline" size="lg" className="hover-lift">
            <a href="#contact">{t("getInTouch")}</a>
          </Button>
          <Button variant="outline" size="lg" className="hover-lift">
            <a href="/resume.pdf" download className="flex items-center gap-2">
              <Download className={`w-4 h-4 ${iconLeft}`} />
              {t("downloadCV")}
            </a>
          </Button>
        </div>
        
        <div className={`flex justify-center gap-6 mb-12 ${isRTL ? 'flex-row-reverse' : ''} ${getAnimationClass('fadeInUp', heroVisible, 5)}`}>
          <a
            href="https://github.com"
            className="p-3 rounded-full bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 magnetic transition-all duration-300"
            aria-label="GitHub Profile"
          >
            <Github className="h-6 w-6" />
          </a>
          <a
            href="https://linkedin.com"
            className="p-3 rounded-full bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 magnetic transition-all duration-300"
            aria-label="LinkedIn Profile"
          >
            <Linkedin className="h-6 w-6" />
          </a>
          <a
            href="mailto:<EMAIL>"
            className="p-3 rounded-full bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 magnetic transition-all duration-300"
            aria-label="Send Email"
          >
            <Mail className="h-6 w-6" />
          </a>
        </div>
        
        <div className={`text-center ${getAnimationClass('fadeInUp', heroVisible, 6)}`}>
          <a href="#about" className="inline-block p-3 rounded-full bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 hover-lift animate-bounce">
            <ArrowDown className="h-6 w-6 text-gray-400 hover:text-blue-500 transition-colors duration-300" />
          </a>
        </div>
      </div>
    </section>
  );
};
