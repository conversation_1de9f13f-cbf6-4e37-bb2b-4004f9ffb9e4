{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/index.ts"], "names": ["RSC_MOD_REF_PROXY_ALIAS", "BARREL_OPTIMIZATION_PREFIX", "RSC_MODULE_TYPES", "warnOnce", "getRSCModuleInformation", "formatBarrelOptimizedResource", "getModuleBuildInfo", "noopHeadPath", "require", "resolve", "MODULE_PROXY_PATH", "getAssumedSourceType", "mod", "sourceType", "buildInfo", "detectedClientEntryType", "rsc", "clientEntryType", "clientRefs", "assumedSourceType", "length", "includes", "transformSource", "source", "sourceMap", "Error", "_module", "resourceKey", "resourcePath", "matchResource", "startsWith", "type", "client", "parser", "callback", "esmSource", "cnt", "ref", "replacedSource", "replace"], "mappings": "AACA,SAASA,uBAAuB,QAAQ,4BAA2B;AACnE,SACEC,0BAA0B,EAC1BC,gBAAgB,QACX,mCAAkC;AACzC,SAASC,QAAQ,QAAQ,yCAAwC;AACjE,SAASC,uBAAuB,QAAQ,yCAAwC;AAChF,SAASC,6BAA6B,QAAQ,cAAa;AAC3D,SAASC,kBAAkB,QAAQ,2BAA0B;AAE7D,MAAMC,eAAeC,QAAQC,OAAO,CAAC;AACrC,gEAAgE;AAChE,MAAMC,oBACJ;AAGF,OAAO,SAASC,qBACdC,GAAmB,EACnBC,UAAsB;QAGUC,gBACbA;IAFnB,MAAMA,YAAYR,mBAAmBM;IACrC,MAAMG,0BAA0BD,8BAAAA,iBAAAA,UAAWE,GAAG,qBAAdF,eAAgBG,eAAe;IAC/D,MAAMC,aAAaJ,CAAAA,8BAAAA,kBAAAA,UAAWE,GAAG,qBAAdF,gBAAgBI,UAAU,KAAI,EAAE;IAEnD,4EAA4E;IAC5E,6EAA6E;IAC7E,4DAA4D;IAC5D,IAAIC,oBAAoBN;IACxB,IAAIM,sBAAsB,UAAUJ,4BAA4B,QAAQ;QACtE,IACEG,WAAWE,MAAM,KAAK,KACrBF,WAAWE,MAAM,KAAK,KAAKF,UAAU,CAAC,EAAE,KAAK,IAC9C;YACA,uEAAuE;YACvE,yEAAyE;YACzE,oBAAoB;YACpBC,oBAAoB;QACtB,OAAO,IAAI,CAACD,WAAWG,QAAQ,CAAC,MAAM;YACpC,2CAA2C;YAC3CF,oBAAoB;QACtB;IACF;IACA,OAAOA;AACT;AAEA,eAAe,SAASG,gBAEtBC,MAAc,EACdC,SAAc;QAyBV,6BAAA,eAQAV,gBAwCAA;IAvEJ,8BAA8B;IAC9B,IAAI,OAAOS,WAAW,UAAU;QAC9B,MAAM,IAAIE,MAAM;IAClB;IAEA,gDAAgD;IAChD,mEAAmE;IACnE,MAAMX,YAAYR,mBAAmB,IAAI,CAACoB,OAAO;IACjDZ,UAAUE,GAAG,GAAGZ,wBAAwBmB,QAAQ;IAEhD,2EAA2E;IAC3E,gFAAgF;IAChF,UAAU;IACV,EAAE;IACF,6EAA6E;IAC7E,iFAAiF;IACjF,gFAAgF;IAChF,iFAAiF;IACjF,uBAAuB;IACvB,EAAE;IACF,0EAA0E;IAC1E,sBAAsB;IACtB,IAAII,cAAsB,IAAI,CAACC,YAAY;IAC3C,KAAI,gBAAA,IAAI,CAACF,OAAO,sBAAZ,8BAAA,cAAcG,aAAa,qBAA3B,4BAA6BC,UAAU,CAAC7B,6BAA6B;QACvE0B,cAActB,8BACZsB,aACA,IAAI,CAACD,OAAO,CAACG,aAAa;IAE9B;IAEA,qBAAqB;IACrB,IAAIf,EAAAA,iBAAAA,UAAUE,GAAG,qBAAbF,eAAeiB,IAAI,MAAK7B,iBAAiB8B,MAAM,EAAE;YAGjD,sBAAA;QAFF,MAAMb,oBAAoBR,qBACxB,IAAI,CAACe,OAAO,GACZ,iBAAA,IAAI,CAACA,OAAO,sBAAZ,uBAAA,eAAcO,MAAM,qBAApB,qBAAsBpB,UAAU;QAElC,MAAMK,aAAaJ,UAAUE,GAAG,CAACE,UAAU;QAE3C,IAAIC,sBAAsB,UAAU;YAClC,IAAID,WAAWG,QAAQ,CAAC,MAAM;gBAC5B,IAAI,CAACa,QAAQ,CACX,IAAIT,MACF,CAAC,oGAAoG,CAAC;gBAG1G;YACF;YAEA,IAAIU,YAAY,CAAC;6BACM,EAAEzB,kBAAkB;AACjD,CAAC;YACK,IAAI0B,MAAM;YACV,KAAK,MAAMC,OAAOnB,WAAY;gBAC5B,IAAImB,QAAQ,IAAI;oBACdF,aAAa,CAAC,wCAAwC,EAAER,YAAY,KAAK,CAAC;gBAC5E,OAAO,IAAIU,QAAQ,WAAW;oBAC5BF,aAAa,CAAC;uCACe,EAAER,YAAY;AACrD,CAAC;gBACO,OAAO;oBACLQ,aAAa,CAAC;OACjB,EAAEC,IAAI,2BAA2B,EAAET,YAAY,CAAC,EAAEU,IAAI;UACnD,EAAED,MAAM,IAAI,EAAEC,IAAI,GAAG,CAAC;gBACxB;YACF;YAEA,IAAI,CAACH,QAAQ,CAAC,MAAMC,WAAWX;YAC/B;QACF;IACF;IAEA,IAAIV,EAAAA,kBAAAA,UAAUE,GAAG,qBAAbF,gBAAeiB,IAAI,MAAK7B,iBAAiB8B,MAAM,EAAE;QACnD,IAAIzB,iBAAiB,IAAI,CAACqB,YAAY,EAAE;YACtCzB,SACE,CAAC,0OAA0O,CAAC;QAEhP;IACF;IAEA,MAAMmC,iBAAiBf,OAAOgB,OAAO,CACnCvC,yBACAU;IAEF,IAAI,CAACwB,QAAQ,CAAC,MAAMI,gBAAgBd;AACtC"}