"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/index.css":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/index.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\r\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');\\r\\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');\\r\\n\\r\\n*, ::before, ::after{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\r\\n\\r\\n::backdrop{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\r\\n\\r\\n/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*/\\r\\n\\r\\n/*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\r\\n\\r\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\r\\n\\r\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\r\\n\\r\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\r\\n\\r\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\r\\n\\r\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\r\\n\\r\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\r\\n\\r\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\r\\n\\r\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\r\\n\\r\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\r\\n\\r\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\r\\n\\r\\n/*\\nRemove the default font size and weight for headings.\\n*/\\r\\n\\r\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\r\\n\\r\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\r\\n\\r\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\r\\n\\r\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\r\\n\\r\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\r\\n\\r\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\r\\n\\r\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\r\\n\\r\\n/*\\nAdd the correct font size in all browsers.\\n*/\\r\\n\\r\\nsmall {\\n  font-size: 80%;\\n}\\r\\n\\r\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\r\\n\\r\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\r\\n\\r\\nsub {\\n  bottom: -0.25em;\\n}\\r\\n\\r\\nsup {\\n  top: -0.5em;\\n}\\r\\n\\r\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\r\\n\\r\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\r\\n\\r\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\r\\n\\r\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\r\\n\\r\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\r\\n\\r\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\r\\n\\r\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\r\\n\\r\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\r\\n\\r\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\r\\n\\r\\n:-moz-focusring {\\n  outline: auto;\\n}\\r\\n\\r\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\r\\n\\r\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\r\\n\\r\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\r\\n\\r\\nprogress {\\n  vertical-align: baseline;\\n}\\r\\n\\r\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\r\\n\\r\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\r\\n\\r\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\r\\n\\r\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\r\\n\\r\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\r\\n\\r\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\r\\n\\r\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\r\\n\\r\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\r\\n\\r\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\r\\n\\r\\nsummary {\\n  display: list-item;\\n}\\r\\n\\r\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\r\\n\\r\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\r\\n\\r\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\r\\n\\r\\nlegend {\\n  padding: 0;\\n}\\r\\n\\r\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\r\\n\\r\\n/*\\nReset default styling for dialogs.\\n*/\\r\\n\\r\\ndialog {\\n  padding: 0;\\n}\\r\\n\\r\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\r\\n\\r\\ntextarea {\\n  resize: vertical;\\n}\\r\\n\\r\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\r\\n\\r\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\r\\n\\r\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\r\\n\\r\\n/*\\nSet the default cursor for buttons.\\n*/\\r\\n\\r\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\r\\n\\r\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\r\\n\\r\\n:disabled {\\n  cursor: default;\\n}\\r\\n\\r\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\r\\n\\r\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\r\\n\\r\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\r\\n\\r\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\r\\n\\r\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\r\\n\\r\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\r\\n\\r\\n:root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --primary: 222.2 47.4% 11.2%;\\r\\n    --primary-foreground: 210 40% 98%;\\r\\n\\r\\n    --secondary: 210 40% 96.1%;\\r\\n    --secondary-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --muted: 210 40% 96.1%;\\r\\n    --muted-foreground: 215.4 16.3% 46.9%;\\r\\n\\r\\n    --accent: 210 40% 96.1%;\\r\\n    --accent-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 210 40% 98%;\\r\\n\\r\\n    --border: 214.3 31.8% 91.4%;\\r\\n    --input: 214.3 31.8% 91.4%;\\r\\n    --ring: 222.2 84% 4.9%;\\r\\n\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    --sidebar-background: 0 0% 98%;\\r\\n    --sidebar-foreground: 240 5.3% 26.1%;\\r\\n    --sidebar-primary: 240 5.9% 10%;\\r\\n    --sidebar-primary-foreground: 0 0% 98%;\\r\\n    --sidebar-accent: 240 4.8% 95.9%;\\r\\n    --sidebar-accent-foreground: 240 5.9% 10%;\\r\\n    --sidebar-border: 220 13% 91%;\\r\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\r\\n  }\\r\\n\\r\\n.dark {\\r\\n    --background: 222.2 84% 4.9%;\\r\\n    --foreground: 210 40% 98%;\\r\\n\\r\\n    --card: 222.2 84% 4.9%;\\r\\n    --card-foreground: 210 40% 98%;\\r\\n\\r\\n    --popover: 222.2 84% 4.9%;\\r\\n    --popover-foreground: 210 40% 98%;\\r\\n\\r\\n    --primary: 210 40% 98%;\\r\\n    --primary-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --secondary: 217.2 32.6% 17.5%;\\r\\n    --secondary-foreground: 210 40% 98%;\\r\\n\\r\\n    --muted: 217.2 32.6% 17.5%;\\r\\n    --muted-foreground: 215 20.2% 65.1%;\\r\\n\\r\\n    --accent: 217.2 32.6% 17.5%;\\r\\n    --accent-foreground: 210 40% 98%;\\r\\n\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 210 40% 98%;\\r\\n\\r\\n    --border: 217.2 32.6% 17.5%;\\r\\n    --input: 217.2 32.6% 17.5%;\\r\\n    --ring: 212.7 26.8% 83.9%;\\r\\n    --sidebar-background: 240 5.9% 10%;\\r\\n    --sidebar-foreground: 240 4.8% 95.9%;\\r\\n    --sidebar-primary: 224.3 76.3% 48%;\\r\\n    --sidebar-primary-foreground: 0 0% 100%;\\r\\n    --sidebar-accent: 240 3.7% 15.9%;\\r\\n    --sidebar-accent-foreground: 240 4.8% 95.9%;\\r\\n    --sidebar-border: 240 3.7% 15.9%;\\r\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\r\\n  }\\r\\n\\r\\n*{\\n  border-color: hsl(var(--border));\\n}\\r\\n\\r\\nbody{\\n  background-color: hsl(var(--background));\\n  color: hsl(var(--foreground));\\n}\\r\\n.sr-only{\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\r\\n.pointer-events-none{\\n  pointer-events: none;\\n}\\r\\n.pointer-events-auto{\\n  pointer-events: auto;\\n}\\r\\n.visible{\\n  visibility: visible;\\n}\\r\\n.invisible{\\n  visibility: hidden;\\n}\\r\\n.fixed{\\n  position: fixed;\\n}\\r\\n.absolute{\\n  position: absolute;\\n}\\r\\n.relative{\\n  position: relative;\\n}\\r\\n.inset-0{\\n  inset: 0px;\\n}\\r\\n.inset-x-0{\\n  left: 0px;\\n  right: 0px;\\n}\\r\\n.inset-y-0{\\n  top: 0px;\\n  bottom: 0px;\\n}\\r\\n.-bottom-12{\\n  bottom: -3rem;\\n}\\r\\n.-left-12{\\n  left: -3rem;\\n}\\r\\n.-right-12{\\n  right: -3rem;\\n}\\r\\n.-top-12{\\n  top: -3rem;\\n}\\r\\n.bottom-0{\\n  bottom: 0px;\\n}\\r\\n.bottom-8{\\n  bottom: 2rem;\\n}\\r\\n.left-0{\\n  left: 0px;\\n}\\r\\n.left-1{\\n  left: 0.25rem;\\n}\\r\\n.left-1\\\\/2{\\n  left: 50%;\\n}\\r\\n.left-2{\\n  left: 0.5rem;\\n}\\r\\n.left-\\\\[50\\\\%\\\\]{\\n  left: 50%;\\n}\\r\\n.right-0{\\n  right: 0px;\\n}\\r\\n.right-1{\\n  right: 0.25rem;\\n}\\r\\n.right-2{\\n  right: 0.5rem;\\n}\\r\\n.right-3{\\n  right: 0.75rem;\\n}\\r\\n.right-4{\\n  right: 1rem;\\n}\\r\\n.right-8{\\n  right: 2rem;\\n}\\r\\n.top-0{\\n  top: 0px;\\n}\\r\\n.top-1\\\\.5{\\n  top: 0.375rem;\\n}\\r\\n.top-1\\\\/2{\\n  top: 50%;\\n}\\r\\n.top-2{\\n  top: 0.5rem;\\n}\\r\\n.top-3\\\\.5{\\n  top: 0.875rem;\\n}\\r\\n.top-4{\\n  top: 1rem;\\n}\\r\\n.top-\\\\[1px\\\\]{\\n  top: 1px;\\n}\\r\\n.top-\\\\[50\\\\%\\\\]{\\n  top: 50%;\\n}\\r\\n.top-\\\\[60\\\\%\\\\]{\\n  top: 60%;\\n}\\r\\n.top-full{\\n  top: 100%;\\n}\\r\\n.z-10{\\n  z-index: 10;\\n}\\r\\n.z-20{\\n  z-index: 20;\\n}\\r\\n.z-50{\\n  z-index: 50;\\n}\\r\\n.z-\\\\[100\\\\]{\\n  z-index: 100;\\n}\\r\\n.z-\\\\[1\\\\]{\\n  z-index: 1;\\n}\\r\\n.float-right{\\n  float: right;\\n}\\r\\n.float-left{\\n  float: left;\\n}\\r\\n.-mx-1{\\n  margin-left: -0.25rem;\\n  margin-right: -0.25rem;\\n}\\r\\n.mx-2{\\n  margin-left: 0.5rem;\\n  margin-right: 0.5rem;\\n}\\r\\n.mx-3\\\\.5{\\n  margin-left: 0.875rem;\\n  margin-right: 0.875rem;\\n}\\r\\n.mx-auto{\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\r\\n.my-0\\\\.5{\\n  margin-top: 0.125rem;\\n  margin-bottom: 0.125rem;\\n}\\r\\n.my-1{\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\r\\n.-ml-4{\\n  margin-left: -1rem;\\n}\\r\\n.-mt-4{\\n  margin-top: -1rem;\\n}\\r\\n.mb-1{\\n  margin-bottom: 0.25rem;\\n}\\r\\n.mb-12{\\n  margin-bottom: 3rem;\\n}\\r\\n.mb-16{\\n  margin-bottom: 4rem;\\n}\\r\\n.mb-2{\\n  margin-bottom: 0.5rem;\\n}\\r\\n.mb-3{\\n  margin-bottom: 0.75rem;\\n}\\r\\n.mb-4{\\n  margin-bottom: 1rem;\\n}\\r\\n.mb-6{\\n  margin-bottom: 1.5rem;\\n}\\r\\n.mb-8{\\n  margin-bottom: 2rem;\\n}\\r\\n.ml-1{\\n  margin-left: 0.25rem;\\n}\\r\\n.ml-2{\\n  margin-left: 0.5rem;\\n}\\r\\n.ml-auto{\\n  margin-left: auto;\\n}\\r\\n.mr-2{\\n  margin-right: 0.5rem;\\n}\\r\\n.mr-3{\\n  margin-right: 0.75rem;\\n}\\r\\n.mt-1\\\\.5{\\n  margin-top: 0.375rem;\\n}\\r\\n.mt-12{\\n  margin-top: 3rem;\\n}\\r\\n.mt-2{\\n  margin-top: 0.5rem;\\n}\\r\\n.mt-2\\\\.5{\\n  margin-top: 0.625rem;\\n}\\r\\n.mt-24{\\n  margin-top: 6rem;\\n}\\r\\n.mt-4{\\n  margin-top: 1rem;\\n}\\r\\n.mt-6{\\n  margin-top: 1.5rem;\\n}\\r\\n.mt-auto{\\n  margin-top: auto;\\n}\\r\\n.block{\\n  display: block;\\n}\\r\\n.inline-block{\\n  display: inline-block;\\n}\\r\\n.flex{\\n  display: flex;\\n}\\r\\n.inline-flex{\\n  display: inline-flex;\\n}\\r\\n.table{\\n  display: table;\\n}\\r\\n.grid{\\n  display: grid;\\n}\\r\\n.hidden{\\n  display: none;\\n}\\r\\n.aspect-square{\\n  aspect-ratio: 1 / 1;\\n}\\r\\n.aspect-video{\\n  aspect-ratio: 16 / 9;\\n}\\r\\n.size-4{\\n  width: 1rem;\\n  height: 1rem;\\n}\\r\\n.h-1{\\n  height: 0.25rem;\\n}\\r\\n.h-1\\\\.5{\\n  height: 0.375rem;\\n}\\r\\n.h-10{\\n  height: 2.5rem;\\n}\\r\\n.h-11{\\n  height: 2.75rem;\\n}\\r\\n.h-12{\\n  height: 3rem;\\n}\\r\\n.h-14{\\n  height: 3.5rem;\\n}\\r\\n.h-2{\\n  height: 0.5rem;\\n}\\r\\n.h-2\\\\.5{\\n  height: 0.625rem;\\n}\\r\\n.h-24{\\n  height: 6rem;\\n}\\r\\n.h-3{\\n  height: 0.75rem;\\n}\\r\\n.h-3\\\\.5{\\n  height: 0.875rem;\\n}\\r\\n.h-4{\\n  height: 1rem;\\n}\\r\\n.h-5{\\n  height: 1.25rem;\\n}\\r\\n.h-6{\\n  height: 1.5rem;\\n}\\r\\n.h-7{\\n  height: 1.75rem;\\n}\\r\\n.h-8{\\n  height: 2rem;\\n}\\r\\n.h-9{\\n  height: 2.25rem;\\n}\\r\\n.h-96{\\n  height: 24rem;\\n}\\r\\n.h-\\\\[1px\\\\]{\\n  height: 1px;\\n}\\r\\n.h-\\\\[var\\\\(--radix-navigation-menu-viewport-height\\\\)\\\\]{\\n  height: var(--radix-navigation-menu-viewport-height);\\n}\\r\\n.h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\]{\\n  height: var(--radix-select-trigger-height);\\n}\\r\\n.h-auto{\\n  height: auto;\\n}\\r\\n.h-full{\\n  height: 100%;\\n}\\r\\n.h-px{\\n  height: 1px;\\n}\\r\\n.h-svh{\\n  height: 100svh;\\n}\\r\\n.max-h-96{\\n  max-height: 24rem;\\n}\\r\\n.max-h-\\\\[300px\\\\]{\\n  max-height: 300px;\\n}\\r\\n.max-h-screen{\\n  max-height: 100vh;\\n}\\r\\n.min-h-0{\\n  min-height: 0px;\\n}\\r\\n.min-h-\\\\[80px\\\\]{\\n  min-height: 80px;\\n}\\r\\n.min-h-screen{\\n  min-height: 100vh;\\n}\\r\\n.min-h-svh{\\n  min-height: 100svh;\\n}\\r\\n.w-0{\\n  width: 0px;\\n}\\r\\n.w-1{\\n  width: 0.25rem;\\n}\\r\\n.w-10{\\n  width: 2.5rem;\\n}\\r\\n.w-11{\\n  width: 2.75rem;\\n}\\r\\n.w-12{\\n  width: 3rem;\\n}\\r\\n.w-14{\\n  width: 3.5rem;\\n}\\r\\n.w-2{\\n  width: 0.5rem;\\n}\\r\\n.w-2\\\\.5{\\n  width: 0.625rem;\\n}\\r\\n.w-24{\\n  width: 6rem;\\n}\\r\\n.w-3{\\n  width: 0.75rem;\\n}\\r\\n.w-3\\\\.5{\\n  width: 0.875rem;\\n}\\r\\n.w-3\\\\/4{\\n  width: 75%;\\n}\\r\\n.w-4{\\n  width: 1rem;\\n}\\r\\n.w-5{\\n  width: 1.25rem;\\n}\\r\\n.w-6{\\n  width: 1.5rem;\\n}\\r\\n.w-64{\\n  width: 16rem;\\n}\\r\\n.w-7{\\n  width: 1.75rem;\\n}\\r\\n.w-72{\\n  width: 18rem;\\n}\\r\\n.w-8{\\n  width: 2rem;\\n}\\r\\n.w-9{\\n  width: 2.25rem;\\n}\\r\\n.w-96{\\n  width: 24rem;\\n}\\r\\n.w-\\\\[--sidebar-width\\\\]{\\n  width: var(--sidebar-width);\\n}\\r\\n.w-\\\\[100px\\\\]{\\n  width: 100px;\\n}\\r\\n.w-\\\\[1px\\\\]{\\n  width: 1px;\\n}\\r\\n.w-auto{\\n  width: auto;\\n}\\r\\n.w-full{\\n  width: 100%;\\n}\\r\\n.w-max{\\n  width: -moz-max-content;\\n  width: max-content;\\n}\\r\\n.w-px{\\n  width: 1px;\\n}\\r\\n.min-w-0{\\n  min-width: 0px;\\n}\\r\\n.min-w-5{\\n  min-width: 1.25rem;\\n}\\r\\n.min-w-\\\\[12rem\\\\]{\\n  min-width: 12rem;\\n}\\r\\n.min-w-\\\\[8rem\\\\]{\\n  min-width: 8rem;\\n}\\r\\n.min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\]{\\n  min-width: var(--radix-select-trigger-width);\\n}\\r\\n.max-w-2xl{\\n  max-width: 42rem;\\n}\\r\\n.max-w-3xl{\\n  max-width: 48rem;\\n}\\r\\n.max-w-4xl{\\n  max-width: 56rem;\\n}\\r\\n.max-w-6xl{\\n  max-width: 72rem;\\n}\\r\\n.max-w-7xl{\\n  max-width: 80rem;\\n}\\r\\n.max-w-\\\\[--skeleton-width\\\\]{\\n  max-width: var(--skeleton-width);\\n}\\r\\n.max-w-lg{\\n  max-width: 32rem;\\n}\\r\\n.max-w-max{\\n  max-width: -moz-max-content;\\n  max-width: max-content;\\n}\\r\\n.max-w-md{\\n  max-width: 28rem;\\n}\\r\\n.flex-1{\\n  flex: 1 1 0%;\\n}\\r\\n.flex-shrink-0{\\n  flex-shrink: 0;\\n}\\r\\n.shrink-0{\\n  flex-shrink: 0;\\n}\\r\\n.grow{\\n  flex-grow: 1;\\n}\\r\\n.grow-0{\\n  flex-grow: 0;\\n}\\r\\n.basis-full{\\n  flex-basis: 100%;\\n}\\r\\n.caption-bottom{\\n  caption-side: bottom;\\n}\\r\\n.border-collapse{\\n  border-collapse: collapse;\\n}\\r\\n.-translate-x-1\\\\/2{\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-x-12{\\n  --tw-translate-x: -3rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-x-px{\\n  --tw-translate-x: -1px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-y-1\\\\/2{\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-y-12{\\n  --tw-translate-y: -3rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-0{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-1\\\\/2{\\n  --tw-translate-x: 50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-12{\\n  --tw-translate-x: 3rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-\\\\[-50\\\\%\\\\]{\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-px{\\n  --tw-translate-x: 1px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-0{\\n  --tw-translate-y: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-1\\\\/2{\\n  --tw-translate-y: 50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-12{\\n  --tw-translate-y: 3rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-16{\\n  --tw-translate-y: 4rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-20{\\n  --tw-translate-y: 5rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-\\\\[-50\\\\%\\\\]{\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-0{\\n  --tw-rotate: 0deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-45{\\n  --tw-rotate: 45deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-6{\\n  --tw-rotate: 6deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-90{\\n  --tw-rotate: 90deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-100{\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-75{\\n  --tw-scale-x: .75;\\n  --tw-scale-y: .75;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-90{\\n  --tw-scale-x: .9;\\n  --tw-scale-y: .9;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-95{\\n  --tw-scale-x: .95;\\n  --tw-scale-y: .95;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-x-0{\\n  --tw-scale-x: 0;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.transform{\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.animate-\\\\[scale-x-100_1s_ease-out_0\\\\.5s_forwards\\\\]{\\n  animation: scale-x-100 1s ease-out 0.5s forwards;\\n}\\r\\n@keyframes pulse{\\r\\n\\r\\n  50%{\\n    opacity: .5;\\n  }\\n}\\r\\n.animate-pulse{\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\r\\n@keyframes spin{\\r\\n\\r\\n  to{\\n    transform: rotate(360deg);\\n  }\\n}\\r\\n.animate-spin{\\n  animation: spin 1s linear infinite;\\n}\\r\\n.cursor-default{\\n  cursor: default;\\n}\\r\\n.cursor-pointer{\\n  cursor: pointer;\\n}\\r\\n.touch-none{\\n  touch-action: none;\\n}\\r\\n.select-none{\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\r\\n.resize-none{\\n  resize: none;\\n}\\r\\n.list-none{\\n  list-style-type: none;\\n}\\r\\n.grid-cols-1{\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\r\\n.grid-cols-2{\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\r\\n.flex-row{\\n  flex-direction: row;\\n}\\r\\n.flex-row-reverse{\\n  flex-direction: row-reverse;\\n}\\r\\n.flex-col{\\n  flex-direction: column;\\n}\\r\\n.flex-col-reverse{\\n  flex-direction: column-reverse;\\n}\\r\\n.flex-wrap{\\n  flex-wrap: wrap;\\n}\\r\\n.items-start{\\n  align-items: flex-start;\\n}\\r\\n.items-end{\\n  align-items: flex-end;\\n}\\r\\n.items-center{\\n  align-items: center;\\n}\\r\\n.items-stretch{\\n  align-items: stretch;\\n}\\r\\n.justify-center{\\n  justify-content: center;\\n}\\r\\n.justify-between{\\n  justify-content: space-between;\\n}\\r\\n.gap-1{\\n  gap: 0.25rem;\\n}\\r\\n.gap-1\\\\.5{\\n  gap: 0.375rem;\\n}\\r\\n.gap-12{\\n  gap: 3rem;\\n}\\r\\n.gap-2{\\n  gap: 0.5rem;\\n}\\r\\n.gap-3{\\n  gap: 0.75rem;\\n}\\r\\n.gap-4{\\n  gap: 1rem;\\n}\\r\\n.gap-6{\\n  gap: 1.5rem;\\n}\\r\\n.gap-8{\\n  gap: 2rem;\\n}\\r\\n.space-x-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-6 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-1\\\\.5 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-6 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-8 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-x-reverse > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 1;\\n}\\r\\n.overflow-auto{\\n  overflow: auto;\\n}\\r\\n.overflow-hidden{\\n  overflow: hidden;\\n}\\r\\n.overflow-y-auto{\\n  overflow-y: auto;\\n}\\r\\n.overflow-x-hidden{\\n  overflow-x: hidden;\\n}\\r\\n.whitespace-nowrap{\\n  white-space: nowrap;\\n}\\r\\n.break-words{\\n  overflow-wrap: break-word;\\n}\\r\\n.rounded-\\\\[2px\\\\]{\\n  border-radius: 2px;\\n}\\r\\n.rounded-\\\\[inherit\\\\]{\\n  border-radius: inherit;\\n}\\r\\n.rounded-full{\\n  border-radius: 9999px;\\n}\\r\\n.rounded-lg{\\n  border-radius: var(--radius);\\n}\\r\\n.rounded-md{\\n  border-radius: calc(var(--radius) - 2px);\\n}\\r\\n.rounded-sm{\\n  border-radius: calc(var(--radius) - 4px);\\n}\\r\\n.rounded-t-\\\\[10px\\\\]{\\n  border-top-left-radius: 10px;\\n  border-top-right-radius: 10px;\\n}\\r\\n.rounded-tl-sm{\\n  border-top-left-radius: calc(var(--radius) - 4px);\\n}\\r\\n.border{\\n  border-width: 1px;\\n}\\r\\n.border-0{\\n  border-width: 0px;\\n}\\r\\n.border-2{\\n  border-width: 2px;\\n}\\r\\n.border-\\\\[1\\\\.5px\\\\]{\\n  border-width: 1.5px;\\n}\\r\\n.border-y{\\n  border-top-width: 1px;\\n  border-bottom-width: 1px;\\n}\\r\\n.border-b{\\n  border-bottom-width: 1px;\\n}\\r\\n.border-l{\\n  border-left-width: 1px;\\n}\\r\\n.border-r{\\n  border-right-width: 1px;\\n}\\r\\n.border-t{\\n  border-top-width: 1px;\\n}\\r\\n.border-dashed{\\n  border-style: dashed;\\n}\\r\\n.border-\\\\[--color-border\\\\]{\\n  border-color: var(--color-border);\\n}\\r\\n.border-border\\\\/50{\\n  border-color: hsl(var(--border) / 0.5);\\n}\\r\\n.border-destructive{\\n  border-color: hsl(var(--destructive));\\n}\\r\\n.border-destructive\\\\/50{\\n  border-color: hsl(var(--destructive) / 0.5);\\n}\\r\\n.border-gray-100{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-300{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-800{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-input{\\n  border-color: hsl(var(--input));\\n}\\r\\n.border-primary{\\n  border-color: hsl(var(--primary));\\n}\\r\\n.border-sidebar-border{\\n  border-color: hsl(var(--sidebar-border));\\n}\\r\\n.border-transparent{\\n  border-color: transparent;\\n}\\r\\n.border-white{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-l-transparent{\\n  border-left-color: transparent;\\n}\\r\\n.border-t-transparent{\\n  border-top-color: transparent;\\n}\\r\\n.bg-\\\\[--color-bg\\\\]{\\n  background-color: var(--color-bg);\\n}\\r\\n.bg-accent{\\n  background-color: hsl(var(--accent));\\n}\\r\\n.bg-background{\\n  background-color: hsl(var(--background));\\n}\\r\\n.bg-black\\\\/80{\\n  background-color: rgb(0 0 0 / 0.8);\\n}\\r\\n.bg-border{\\n  background-color: hsl(var(--border));\\n}\\r\\n.bg-card{\\n  background-color: hsl(var(--card));\\n}\\r\\n.bg-destructive{\\n  background-color: hsl(var(--destructive));\\n}\\r\\n.bg-foreground{\\n  background-color: hsl(var(--foreground));\\n}\\r\\n.bg-gray-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-200\\\\/50{\\n  background-color: rgb(229 231 235 / 0.5);\\n}\\r\\n.bg-gray-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-800{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-900{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-muted{\\n  background-color: hsl(var(--muted));\\n}\\r\\n.bg-muted\\\\/50{\\n  background-color: hsl(var(--muted) / 0.5);\\n}\\r\\n.bg-popover{\\n  background-color: hsl(var(--popover));\\n}\\r\\n.bg-primary{\\n  background-color: hsl(var(--primary));\\n}\\r\\n.bg-secondary{\\n  background-color: hsl(var(--secondary));\\n}\\r\\n.bg-sidebar{\\n  background-color: hsl(var(--sidebar-background));\\n}\\r\\n.bg-sidebar-border{\\n  background-color: hsl(var(--sidebar-border));\\n}\\r\\n.bg-transparent{\\n  background-color: transparent;\\n}\\r\\n.bg-white{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-white\\\\/80{\\n  background-color: rgb(255 255 255 / 0.8);\\n}\\r\\n.bg-white\\\\/90{\\n  background-color: rgb(255 255 255 / 0.9);\\n}\\r\\n.bg-white\\\\/95{\\n  background-color: rgb(255 255 255 / 0.95);\\n}\\r\\n.bg-gradient-to-br{\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\r\\n.bg-gradient-to-r{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\r\\n.from-blue-100{\\n  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-400\\\\/20{\\n  --tw-gradient-from: rgb(96 165 250 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-50\\\\/50{\\n  --tw-gradient-from: rgb(239 246 255 / 0.5) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-500{\\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-600{\\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-emerald-500{\\n  --tw-gradient-from: #10b981 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(16 185 129 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-50{\\n  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-900{\\n  --tw-gradient-from: #111827 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-green-500{\\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-indigo-500{\\n  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-purple-400\\\\/20{\\n  --tw-gradient-from: rgb(192 132 252 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-purple-500{\\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-slate-50{\\n  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-yellow-500{\\n  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.via-blue-800{\\n  --tw-gradient-to: rgb(30 64 175 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #1e40af var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.via-purple-500{\\n  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #a855f7 var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.via-transparent{\\n  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.via-white{\\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.to-blue-50{\\n  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);\\n}\\r\\n.to-blue-500{\\n  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);\\n}\\r\\n.to-green-500{\\n  --tw-gradient-to: #22c55e var(--tw-gradient-to-position);\\n}\\r\\n.to-orange-500{\\n  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);\\n}\\r\\n.to-pink-400\\\\/20{\\n  --tw-gradient-to: rgb(244 114 182 / 0.2) var(--tw-gradient-to-position);\\n}\\r\\n.to-pink-500{\\n  --tw-gradient-to: #ec4899 var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-100{\\n  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-400\\\\/20{\\n  --tw-gradient-to: rgb(192 132 252 / 0.2) var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-50\\\\/50{\\n  --tw-gradient-to: rgb(250 245 255 / 0.5) var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-500{\\n  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-600{\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-800{\\n  --tw-gradient-to: #6b21a8 var(--tw-gradient-to-position);\\n}\\r\\n.to-teal-500{\\n  --tw-gradient-to: #14b8a6 var(--tw-gradient-to-position);\\n}\\r\\n.bg-clip-text{\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n}\\r\\n.fill-current{\\n  fill: currentColor;\\n}\\r\\n.p-0{\\n  padding: 0px;\\n}\\r\\n.p-1{\\n  padding: 0.25rem;\\n}\\r\\n.p-2{\\n  padding: 0.5rem;\\n}\\r\\n.p-3{\\n  padding: 0.75rem;\\n}\\r\\n.p-4{\\n  padding: 1rem;\\n}\\r\\n.p-6{\\n  padding: 1.5rem;\\n}\\r\\n.p-\\\\[1px\\\\]{\\n  padding: 1px;\\n}\\r\\n.px-1{\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\r\\n.px-2{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n.px-2\\\\.5{\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n}\\r\\n.px-3{\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\r\\n.px-4{\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\r\\n.px-5{\\n  padding-left: 1.25rem;\\n  padding-right: 1.25rem;\\n}\\r\\n.px-6{\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\r\\n.px-8{\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\r\\n.py-0\\\\.5{\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n}\\r\\n.py-1{\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\r\\n.py-1\\\\.5{\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\r\\n.py-12{\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\r\\n.py-2{\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\r\\n.py-20{\\n  padding-top: 5rem;\\n  padding-bottom: 5rem;\\n}\\r\\n.py-3{\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n.py-4{\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\r\\n.py-6{\\n  padding-top: 1.5rem;\\n  padding-bottom: 1.5rem;\\n}\\r\\n.pb-3{\\n  padding-bottom: 0.75rem;\\n}\\r\\n.pb-4{\\n  padding-bottom: 1rem;\\n}\\r\\n.pl-2\\\\.5{\\n  padding-left: 0.625rem;\\n}\\r\\n.pl-4{\\n  padding-left: 1rem;\\n}\\r\\n.pl-8{\\n  padding-left: 2rem;\\n}\\r\\n.pr-2{\\n  padding-right: 0.5rem;\\n}\\r\\n.pr-2\\\\.5{\\n  padding-right: 0.625rem;\\n}\\r\\n.pr-8{\\n  padding-right: 2rem;\\n}\\r\\n.pt-0{\\n  padding-top: 0px;\\n}\\r\\n.pt-1{\\n  padding-top: 0.25rem;\\n}\\r\\n.pt-2{\\n  padding-top: 0.5rem;\\n}\\r\\n.pt-20{\\n  padding-top: 5rem;\\n}\\r\\n.pt-3{\\n  padding-top: 0.75rem;\\n}\\r\\n.pt-4{\\n  padding-top: 1rem;\\n}\\r\\n.pt-6{\\n  padding-top: 1.5rem;\\n}\\r\\n.text-left{\\n  text-align: left;\\n}\\r\\n.text-center{\\n  text-align: center;\\n}\\r\\n.text-right{\\n  text-align: right;\\n}\\r\\n.align-middle{\\n  vertical-align: middle;\\n}\\r\\n.font-cairo{\\n  font-family: Cairo, sans-serif;\\n}\\r\\n.font-inter{\\n  font-family: Inter, sans-serif;\\n}\\r\\n.font-mono{\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace;\\n}\\r\\n.text-2xl{\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\r\\n.text-3xl{\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\r\\n.text-4xl{\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\r\\n.text-9xl{\\n  font-size: 8rem;\\n  line-height: 1;\\n}\\r\\n.text-\\\\[0\\\\.8rem\\\\]{\\n  font-size: 0.8rem;\\n}\\r\\n.text-base{\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\r\\n.text-lg{\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-sm{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n.text-xl{\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-xs{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n.font-bold{\\n  font-weight: 700;\\n}\\r\\n.font-medium{\\n  font-weight: 500;\\n}\\r\\n.font-normal{\\n  font-weight: 400;\\n}\\r\\n.font-semibold{\\n  font-weight: 600;\\n}\\r\\n.tabular-nums{\\n  --tw-numeric-spacing: tabular-nums;\\n  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);\\n}\\r\\n.leading-none{\\n  line-height: 1;\\n}\\r\\n.leading-relaxed{\\n  line-height: 1.625;\\n}\\r\\n.tracking-tight{\\n  letter-spacing: -0.025em;\\n}\\r\\n.tracking-widest{\\n  letter-spacing: 0.1em;\\n}\\r\\n.text-accent-foreground{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n.text-blue-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-card-foreground{\\n  color: hsl(var(--card-foreground));\\n}\\r\\n.text-current{\\n  color: currentColor;\\n}\\r\\n.text-destructive{\\n  color: hsl(var(--destructive));\\n}\\r\\n.text-destructive-foreground{\\n  color: hsl(var(--destructive-foreground));\\n}\\r\\n.text-foreground{\\n  color: hsl(var(--foreground));\\n}\\r\\n.text-foreground\\\\/50{\\n  color: hsl(var(--foreground) / 0.5);\\n}\\r\\n.text-gray-200{\\n  --tw-text-opacity: 1;\\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-muted-foreground{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n.text-popover-foreground{\\n  color: hsl(var(--popover-foreground));\\n}\\r\\n.text-primary{\\n  color: hsl(var(--primary));\\n}\\r\\n.text-primary-foreground{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n.text-secondary-foreground{\\n  color: hsl(var(--secondary-foreground));\\n}\\r\\n.text-sidebar-foreground{\\n  color: hsl(var(--sidebar-foreground));\\n}\\r\\n.text-sidebar-foreground\\\\/70{\\n  color: hsl(var(--sidebar-foreground) / 0.7);\\n}\\r\\n.text-transparent{\\n  color: transparent;\\n}\\r\\n.text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n.underline-offset-4{\\n  text-underline-offset: 4px;\\n}\\r\\n.opacity-0{\\n  opacity: 0;\\n}\\r\\n.opacity-100{\\n  opacity: 1;\\n}\\r\\n.opacity-5{\\n  opacity: 0.05;\\n}\\r\\n.opacity-50{\\n  opacity: 0.5;\\n}\\r\\n.opacity-60{\\n  opacity: 0.6;\\n}\\r\\n.opacity-70{\\n  opacity: 0.7;\\n}\\r\\n.opacity-90{\\n  opacity: 0.9;\\n}\\r\\n.shadow-\\\\[0_0_0_1px_hsl\\\\(var\\\\(--sidebar-border\\\\)\\\\)\\\\]{\\n  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));\\n  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-md{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-none{\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-sm{\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-xl{\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.outline-none{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n.outline{\\n  outline-style: solid;\\n}\\r\\n.ring-0{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n.ring-2{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n.ring-gray-200{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity, 1));\\n}\\r\\n.ring-ring{\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n.ring-sidebar-ring{\\n  --tw-ring-color: hsl(var(--sidebar-ring));\\n}\\r\\n.ring-offset-background{\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\r\\n.blur-3xl{\\n  --tw-blur: blur(64px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.filter{\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.backdrop-blur-sm{\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.transition{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-\\\\[left\\\\2c right\\\\2c width\\\\]{\\n  transition-property: left,right,width;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-\\\\[margin\\\\2c opa\\\\]{\\n  transition-property: margin,opa;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-\\\\[width\\\\2c height\\\\2c padding\\\\]{\\n  transition-property: width,height,padding;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-\\\\[width\\\\]{\\n  transition-property: width;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-all{\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-colors{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-opacity{\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-shadow{\\n  transition-property: box-shadow;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-transform{\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.duration-1000{\\n  transition-duration: 1000ms;\\n}\\r\\n.duration-150{\\n  transition-duration: 150ms;\\n}\\r\\n.duration-200{\\n  transition-duration: 200ms;\\n}\\r\\n.duration-300{\\n  transition-duration: 300ms;\\n}\\r\\n.duration-500{\\n  transition-duration: 500ms;\\n}\\r\\n.ease-in-out{\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\r\\n.ease-linear{\\n  transition-timing-function: linear;\\n}\\r\\n.ease-out{\\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\r\\n@keyframes enter{\\r\\n\\r\\n  from{\\n    opacity: var(--tw-enter-opacity, 1);\\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\\n  }\\n}\\r\\n@keyframes exit{\\r\\n\\r\\n  to{\\n    opacity: var(--tw-exit-opacity, 1);\\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\\n  }\\n}\\r\\n.animate-in{\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n.fade-in-0{\\n  --tw-enter-opacity: 0;\\n}\\r\\n.fade-in-80{\\n  --tw-enter-opacity: 0.8;\\n}\\r\\n.zoom-in-95{\\n  --tw-enter-scale: .95;\\n}\\r\\n.duration-1000{\\n  animation-duration: 1000ms;\\n}\\r\\n.duration-150{\\n  animation-duration: 150ms;\\n}\\r\\n.duration-200{\\n  animation-duration: 200ms;\\n}\\r\\n.duration-300{\\n  animation-duration: 300ms;\\n}\\r\\n.duration-500{\\n  animation-duration: 500ms;\\n}\\r\\n.ease-in-out{\\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\r\\n.ease-linear{\\n  animation-timing-function: linear;\\n}\\r\\n.ease-out{\\n  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\r\\n\\r\\n/* Elegant Animations */\\r\\n@keyframes fadeInUp {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(30px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes fadeInLeft {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateX(-30px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateX(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes fadeInRight {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateX(30px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateX(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes scaleIn {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: scale(0.9);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: scale(1);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes float {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-10px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes gradient-shift {\\r\\n  0%, 100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes typing {\\r\\n  from {\\r\\n    width: 0;\\r\\n  }\\r\\n  to {\\r\\n    width: 100%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes blink {\\r\\n  0%, 50% {\\r\\n    border-color: transparent;\\r\\n  }\\r\\n  51%, 100% {\\r\\n    border-color: currentColor;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Animation Classes */\\r\\n.animate-fadeInUp {\\r\\n  animation: fadeInUp 0.8s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-fadeInLeft {\\r\\n  animation: fadeInLeft 0.8s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-fadeInRight {\\r\\n  animation: fadeInRight 0.8s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-scaleIn {\\r\\n  animation: scaleIn 0.6s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-float {\\r\\n  animation: float 3s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-gradient {\\r\\n  background-size: 200% 200%;\\r\\n  animation: gradient-shift 4s ease infinite;\\r\\n}\\r\\n\\r\\n.animate-typing {\\r\\n  overflow: hidden;\\r\\n  border-right: 2px solid;\\r\\n  white-space: nowrap;\\r\\n  animation: typing 3s steps(40, end), blink 0.75s step-end infinite;\\r\\n}\\r\\n\\r\\n/* Stagger animations */\\r\\n.animate-stagger-1 { animation-delay: 0.1s; }\\r\\n.animate-stagger-2 { animation-delay: 0.2s; }\\r\\n.animate-stagger-3 { animation-delay: 0.3s; }\\r\\n.animate-stagger-4 { animation-delay: 0.4s; }\\r\\n.animate-stagger-5 { animation-delay: 0.5s; }\\r\\n\\r\\n/* Enhanced smooth scrolling */\\r\\nhtml {\\r\\n  scroll-behavior: smooth;\\r\\n  scroll-padding-top: 80px;\\r\\n}\\r\\n\\r\\n/* Language transition effects */\\r\\n[dir=\\\"rtl\\\"] {\\r\\n  transition: all 0.3s ease-in-out;\\r\\n}\\r\\n\\r\\n[dir=\\\"ltr\\\"] {\\r\\n  transition: all 0.3s ease-in-out;\\r\\n}\\r\\n\\r\\n/* Text direction transitions */\\r\\n.text-transition {\\r\\n  transition: all 0.3s ease;\\r\\n}\\r\\n\\r\\n/* Performance optimizations */\\r\\n* {\\r\\n  -webkit-font-smoothing: antialiased;\\r\\n  -moz-osx-font-smoothing: grayscale;\\r\\n}\\r\\n\\r\\n/* Reduce motion for accessibility */\\r\\n@media (prefers-reduced-motion: reduce) {\\r\\n  *,\\r\\n  *::before,\\r\\n  *::after {\\r\\n    animation-duration: 0.01ms !important;\\r\\n    animation-iteration-count: 1 !important;\\r\\n    transition-duration: 0.01ms !important;\\r\\n    scroll-behavior: auto !important;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: #f1f1f1;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: linear-gradient(45deg, #3b82f6, #8b5cf6);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: linear-gradient(45deg, #2563eb, #7c3aed);\\r\\n}\\r\\n\\r\\n/* Text selection */\\r\\n::-moz-selection {\\r\\n  background: rgba(59, 130, 246, 0.3);\\r\\n  color: inherit;\\r\\n}\\r\\n::selection {\\r\\n  background: rgba(59, 130, 246, 0.3);\\r\\n  color: inherit;\\r\\n}\\r\\n\\r\\n/* Focus styles */\\r\\n.focus-visible:focus {\\r\\n  outline: 2px solid #3b82f6;\\r\\n  outline-offset: 2px;\\r\\n}\\r\\n\\r\\n/* Line clamp utility */\\r\\n.line-clamp-3 {\\r\\n  display: -webkit-box;\\r\\n  -webkit-line-clamp: 3;\\r\\n  line-clamp: 3;\\r\\n  -webkit-box-orient: vertical;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n/* Custom avatar glow effect */\\r\\n.avatar-glow {\\r\\n  box-shadow:\\r\\n    0 0 20px rgba(59, 130, 246, 0.3),\\r\\n    0 0 40px rgba(139, 92, 246, 0.2),\\r\\n    0 0 60px rgba(236, 72, 153, 0.1);\\r\\n}\\r\\n\\r\\n.avatar-glow:hover {\\r\\n  box-shadow:\\r\\n    0 0 30px rgba(59, 130, 246, 0.4),\\r\\n    0 0 60px rgba(139, 92, 246, 0.3),\\r\\n    0 0 90px rgba(236, 72, 153, 0.2);\\r\\n}\\r\\n\\r\\n/* Scroll Animation Classes - Enhanced for smoothness */\\r\\n.scroll-animate {\\r\\n  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, opacity;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-100 {\\r\\n  transition-delay: 150ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-200 {\\r\\n  transition-delay: 300ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-300 {\\r\\n  transition-delay: 450ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-400 {\\r\\n  transition-delay: 600ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-500 {\\r\\n  transition-delay: 750ms;\\r\\n}\\r\\n\\r\\n/* Stagger animation for children - Enhanced */\\r\\n.stagger-children > * {\\r\\n  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, opacity;\\r\\n}\\r\\n\\r\\n.stagger-children > *:nth-child(1) { transition-delay: 0ms; }\\r\\n.stagger-children > *:nth-child(2) { transition-delay: 150ms; }\\r\\n.stagger-children > *:nth-child(3) { transition-delay: 300ms; }\\r\\n.stagger-children > *:nth-child(4) { transition-delay: 450ms; }\\r\\n.stagger-children > *:nth-child(5) { transition-delay: 600ms; }\\r\\n.stagger-children > *:nth-child(6) { transition-delay: 750ms; }\\r\\n.stagger-children > *:nth-child(7) { transition-delay: 900ms; }\\r\\n.stagger-children > *:nth-child(8) { transition-delay: 1050ms; }\\r\\n\\r\\n/* Parallax effect */\\r\\n.parallax-slow {\\r\\n  transform: translateY(var(--scroll-y, 0) * 0.5);\\r\\n}\\r\\n\\r\\n.parallax-fast {\\r\\n  transform: translateY(var(--scroll-y, 0) * -0.3);\\r\\n}\\r\\n\\r\\n/* Reveal animations */\\r\\n.reveal-up {\\r\\n  opacity: 0;\\r\\n  transform: translateY(50px);\\r\\n}\\r\\n\\r\\n.reveal-up.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.reveal-left {\\r\\n  opacity: 0;\\r\\n  transform: translateX(-50px);\\r\\n}\\r\\n\\r\\n.reveal-left.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateX(0);\\r\\n}\\r\\n\\r\\n.reveal-right {\\r\\n  opacity: 0;\\r\\n  transform: translateX(50px);\\r\\n}\\r\\n\\r\\n.reveal-right.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateX(0);\\r\\n}\\r\\n\\r\\n.reveal-scale {\\r\\n  opacity: 0;\\r\\n  transform: scale(0.8);\\r\\n}\\r\\n\\r\\n.reveal-scale.revealed {\\r\\n  opacity: 1;\\r\\n  transform: scale(1);\\r\\n}\\r\\n\\r\\n/* Hover effects for cards - Enhanced smoothness */\\r\\n.card-hover {\\r\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, box-shadow;\\r\\n}\\r\\n\\r\\n.card-hover:hover {\\r\\n  transform: translateY(-12px) scale(1.03) rotateX(2deg);\\r\\n  box-shadow:\\r\\n    0 32px 64px -12px rgba(0, 0, 0, 0.25),\\r\\n    0 0 0 1px rgba(255, 255, 255, 0.1);\\r\\n}\\r\\n\\r\\n/* Enhanced gradient text animation */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #ec4899, #10b981, #f59e0b);\\r\\n  background-size: 400% 400%;\\r\\n  animation: gradient-shift 8s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  will-change: background-position;\\r\\n}\\r\\n\\r\\n@keyframes gradient-shift {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Typing animation */\\r\\n.typing-animation {\\r\\n  overflow: hidden;\\r\\n  border-right: 2px solid #3b82f6;\\r\\n  white-space: nowrap;\\r\\n  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;\\r\\n}\\r\\n\\r\\n@keyframes typing {\\r\\n  from {\\r\\n    width: 0;\\r\\n  }\\r\\n  to {\\r\\n    width: 100%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes blink-caret {\\r\\n  from, to {\\r\\n    border-color: transparent;\\r\\n  }\\r\\n  50% {\\r\\n    border-color: #3b82f6;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Enhanced magnetic effect */\\r\\n.magnetic {\\r\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform;\\r\\n}\\r\\n\\r\\n.magnetic:hover {\\r\\n  transform: scale(1.08) translateY(-2px);\\r\\n  filter: brightness(1.1);\\r\\n}\\r\\n\\r\\n/* Smooth button transitions */\\r\\n.btn-smooth {\\r\\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, box-shadow, background-color;\\r\\n}\\r\\n\\r\\n.btn-smooth:hover {\\r\\n  transform: translateY(-2px) scale(1.02);\\r\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\\r\\n}\\r\\n\\r\\n.btn-smooth:active {\\r\\n  transform: translateY(0) scale(0.98);\\r\\n  transition-duration: 0.1s;\\r\\n}\\r\\n\\r\\n/* Glitch effect */\\r\\n.glitch {\\r\\n  position: relative;\\r\\n  animation: glitch 2s infinite;\\r\\n}\\r\\n\\r\\n.glitch::before,\\r\\n.glitch::after {\\r\\n  content: attr(data-text);\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n}\\r\\n\\r\\n.glitch::before {\\r\\n  animation: glitch-1 0.5s infinite;\\r\\n  color: #ff0000;\\r\\n  z-index: -1;\\r\\n}\\r\\n\\r\\n.glitch::after {\\r\\n  animation: glitch-2 0.5s infinite;\\r\\n  color: #00ff00;\\r\\n  z-index: -2;\\r\\n}\\r\\n\\r\\n@keyframes glitch {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(-2px, 2px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(-2px, -2px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(2px, 2px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(2px, -2px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes glitch-1 {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(-1px, 1px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(-1px, -1px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(1px, 1px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(1px, -1px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes glitch-2 {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(1px, -1px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(1px, 1px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(-1px, -1px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(-1px, 1px);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* RTL Support */\\r\\n[dir=\\\"rtl\\\"] .flex-row {\\r\\n  flex-direction: row-reverse;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .flex-row-reverse {\\r\\n  flex-direction: row;\\r\\n}\\r\\n\\r\\n/* RTL Space between */\\r\\n[dir=\\\"rtl\\\"] .space-x-8 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n/* RTL Gap utilities */\\r\\n[dir=\\\"rtl\\\"] .gap-2 {\\r\\n  gap: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .gap-3 {\\r\\n  gap: 0.75rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .gap-4 {\\r\\n  gap: 1rem;\\r\\n}\\r\\n\\r\\n/* RTL Icon spacing */\\r\\n[dir=\\\"rtl\\\"] .mr-2 {\\r\\n  margin-right: 0;\\r\\n  margin-left: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .ml-2 {\\r\\n  margin-left: 0;\\r\\n  margin-right: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .mr-3 {\\r\\n  margin-right: 0;\\r\\n  margin-left: 0.75rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .ml-3 {\\r\\n  margin-left: 0;\\r\\n  margin-right: 0.75rem;\\r\\n}\\r\\n\\r\\n/* RTL Text alignment */\\r\\n[dir=\\\"rtl\\\"] .text-left {\\r\\n  text-align: right;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .text-right {\\r\\n  text-align: left;\\r\\n}\\r\\n\\r\\n/* RTL Float */\\r\\n[dir=\\\"rtl\\\"] .float-left {\\r\\n  float: right;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .float-right {\\r\\n  float: left;\\r\\n}\\r\\n\\r\\n/* RTL Positioning */\\r\\n[dir=\\\"rtl\\\"] .left-0 {\\r\\n  left: auto;\\r\\n  right: 0;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .right-0 {\\r\\n  right: auto;\\r\\n  left: 0;\\r\\n}\\r\\n\\r\\n/* RTL Border radius */\\r\\n[dir=\\\"rtl\\\"] .rounded-l {\\r\\n  border-top-left-radius: 0;\\r\\n  border-bottom-left-radius: 0;\\r\\n  border-top-right-radius: 0.25rem;\\r\\n  border-bottom-right-radius: 0.25rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .rounded-r {\\r\\n  border-top-right-radius: 0;\\r\\n  border-bottom-right-radius: 0;\\r\\n  border-top-left-radius: 0.25rem;\\r\\n  border-bottom-left-radius: 0.25rem;\\r\\n}\\r\\n\\r\\n/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */\\r\\n\\r\\n/* Enhanced animations and effects */\\r\\n@keyframes fade-in {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(30px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes float {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-20px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes bounce-slow {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-25px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes pulse-slow {\\r\\n  0%, 100% {\\r\\n    opacity: 0.4;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 0.8;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes gradient-x {\\r\\n  0%, 100% {\\r\\n    background-size: 200% 200%;\\r\\n    background-position: left center;\\r\\n  }\\r\\n  50% {\\r\\n    background-size: 200% 200%;\\r\\n    background-position: right center;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes scale-x-100 {\\r\\n  from {\\r\\n    transform: scaleX(0);\\r\\n  }\\r\\n  to {\\r\\n    transform: scaleX(1);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-fade-in {\\r\\n  animation: fade-in 0.8s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-float {\\r\\n  animation: float 6s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-bounce-slow {\\r\\n  animation: bounce-slow 3s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-pulse-slow {\\r\\n  animation: pulse-slow 4s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-gradient-x {\\r\\n  animation: gradient-x 3s ease infinite;\\r\\n}\\r\\n\\r\\n/* Smooth scroll behavior */\\r\\nhtml {\\r\\n  scroll-behavior: smooth;\\r\\n}\\r\\n\\r\\n/* Grid pattern background */\\r\\n.bg-grid-pattern {\\r\\n  background-image: \\r\\n    linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),\\r\\n    linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n}\\r\\n\\r\\n/* Enhanced gradients */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Glassmorphism effect */\\r\\n.glass-effect {\\r\\n  background: rgba(255, 255, 255, 0.25);\\r\\n  -webkit-backdrop-filter: blur(10px);\\r\\n          backdrop-filter: blur(10px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.18);\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: #f1f1f1;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: linear-gradient(45deg, #667eea, #764ba2);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: linear-gradient(45deg, #5a6fd8, #6a419a);\\r\\n}\\r\\n\\r\\n.file\\\\:border-0::file-selector-button{\\n  border-width: 0px;\\n}\\r\\n\\r\\n.file\\\\:bg-transparent::file-selector-button{\\n  background-color: transparent;\\n}\\r\\n\\r\\n.file\\\\:text-sm::file-selector-button{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n\\r\\n.file\\\\:font-medium::file-selector-button{\\n  font-weight: 500;\\n}\\r\\n\\r\\n.file\\\\:text-foreground::file-selector-button{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::-moz-placeholder{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::placeholder{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.after\\\\:absolute::after{\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\r\\n\\r\\n.after\\\\:-inset-2::after{\\n  content: var(--tw-content);\\n  inset: -0.5rem;\\n}\\r\\n\\r\\n.after\\\\:inset-y-0::after{\\n  content: var(--tw-content);\\n  top: 0px;\\n  bottom: 0px;\\n}\\r\\n\\r\\n.after\\\\:left-1\\\\/2::after{\\n  content: var(--tw-content);\\n  left: 50%;\\n}\\r\\n\\r\\n.after\\\\:w-1::after{\\n  content: var(--tw-content);\\n  width: 0.25rem;\\n}\\r\\n\\r\\n.after\\\\:w-\\\\[2px\\\\]::after{\\n  content: var(--tw-content);\\n  width: 2px;\\n}\\r\\n\\r\\n.after\\\\:-translate-x-1\\\\/2::after{\\n  content: var(--tw-content);\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.first\\\\:rounded-l-md:first-child{\\n  border-top-left-radius: calc(var(--radius) - 2px);\\n  border-bottom-left-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.first\\\\:border-l:first-child{\\n  border-left-width: 1px;\\n}\\r\\n\\r\\n.last\\\\:rounded-r-md:last-child{\\n  border-top-right-radius: calc(var(--radius) - 2px);\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.focus-within\\\\:relative:focus-within{\\n  position: relative;\\n}\\r\\n\\r\\n.focus-within\\\\:z-20:focus-within{\\n  z-index: 20;\\n}\\r\\n\\r\\n.hover\\\\:scale-105:hover{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.hover\\\\:scale-110:hover{\\n  --tw-scale-x: 1.1;\\n  --tw-scale-y: 1.1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.hover\\\\:scale-\\\\[1\\\\.02\\\\]:hover{\\n  --tw-scale-x: 1.02;\\n  --tw-scale-y: 1.02;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.hover\\\\:bg-accent:hover{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/80:hover{\\n  background-color: hsl(var(--destructive) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/90:hover{\\n  background-color: hsl(var(--destructive) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-100:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-200:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-800:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-muted:hover{\\n  background-color: hsl(var(--muted));\\n}\\r\\n\\r\\n.hover\\\\:bg-muted\\\\/50:hover{\\n  background-color: hsl(var(--muted) / 0.5);\\n}\\r\\n\\r\\n.hover\\\\:bg-primary:hover{\\n  background-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/80:hover{\\n  background-color: hsl(var(--primary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/90:hover{\\n  background-color: hsl(var(--primary) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-secondary:hover{\\n  background-color: hsl(var(--secondary));\\n}\\r\\n\\r\\n.hover\\\\:bg-secondary\\\\/80:hover{\\n  background-color: hsl(var(--secondary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:bg-sidebar-accent:hover{\\n  background-color: hsl(var(--sidebar-accent));\\n}\\r\\n\\r\\n.hover\\\\:bg-white\\\\/90:hover{\\n  background-color: rgb(255 255 255 / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:from-blue-600:hover{\\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n\\r\\n.hover\\\\:from-blue-700:hover{\\n  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n\\r\\n.hover\\\\:to-purple-700:hover{\\n  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);\\n}\\r\\n\\r\\n.hover\\\\:text-accent-foreground:hover{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-blue-600:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:text-foreground:hover{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-gray-900:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:text-muted-foreground:hover{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-primary-foreground:hover{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-sidebar-accent-foreground:hover{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.hover\\\\:underline:hover{\\n  text-decoration-line: underline;\\n}\\r\\n\\r\\n.hover\\\\:opacity-100:hover{\\n  opacity: 1;\\n}\\r\\n\\r\\n.hover\\\\:shadow-2xl:hover{\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.hover\\\\:shadow-\\\\[0_0_0_1px_hsl\\\\(var\\\\(--sidebar-accent\\\\)\\\\)\\\\]:hover{\\n  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));\\n  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.hover\\\\:shadow-md:hover{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.hover\\\\:shadow-xl:hover{\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.hover\\\\:after\\\\:bg-sidebar-border:hover::after{\\n  content: var(--tw-content);\\n  background-color: hsl(var(--sidebar-border));\\n}\\r\\n\\r\\n.focus\\\\:border-blue-500:focus{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n}\\r\\n\\r\\n.focus\\\\:bg-accent:focus{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.focus\\\\:bg-primary:focus{\\n  background-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.focus\\\\:text-accent-foreground:focus{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.focus\\\\:text-primary-foreground:focus{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n\\r\\n.focus\\\\:opacity-100:focus{\\n  opacity: 1;\\n}\\r\\n\\r\\n.focus\\\\:outline-none:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus\\\\:ring-2:focus{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus\\\\:ring-blue-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\r\\n\\r\\n.focus\\\\:ring-ring:focus{\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus\\\\:ring-offset-2:focus{\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:outline-none:focus-visible{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-1:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-2:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-ring:focus-visible{\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-sidebar-ring:focus-visible{\\n  --tw-ring-color: hsl(var(--sidebar-ring));\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-offset-1:focus-visible{\\n  --tw-ring-offset-width: 1px;\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-offset-2:focus-visible{\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-offset-background:focus-visible{\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\r\\n\\r\\n.active\\\\:bg-sidebar-accent:active{\\n  background-color: hsl(var(--sidebar-accent));\\n}\\r\\n\\r\\n.active\\\\:text-sidebar-accent-foreground:active{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.disabled\\\\:pointer-events-none:disabled{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.disabled\\\\:cursor-not-allowed:disabled{\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.disabled\\\\:opacity-50:disabled{\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.group\\\\/menu-item:focus-within .group-focus-within\\\\/menu-item\\\\:opacity-100{\\n  opacity: 1;\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:-translate-y-1{\\n  --tw-translate-y: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group\\\\/item:hover .group-hover\\\\/item\\\\:scale-125{\\n  --tw-scale-x: 1.25;\\n  --tw-scale-y: 1.25;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:scale-110{\\n  --tw-scale-x: 1.1;\\n  --tw-scale-y: 1.1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group\\\\/item:hover .group-hover\\\\/item\\\\:text-gray-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:text-blue-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:text-blue-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group\\\\/menu-item:hover .group-hover\\\\/menu-item\\\\:opacity-100{\\n  opacity: 1;\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:opacity-100{\\n  opacity: 1;\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:border-muted\\\\/40{\\n  border-color: hsl(var(--muted) / 0.4);\\n}\\r\\n\\r\\n.group.toaster .group-\\\\[\\\\.toaster\\\\]\\\\:border-border{\\n  border-color: hsl(var(--border));\\n}\\r\\n\\r\\n.group.toast .group-\\\\[\\\\.toast\\\\]\\\\:bg-muted{\\n  background-color: hsl(var(--muted));\\n}\\r\\n\\r\\n.group.toast .group-\\\\[\\\\.toast\\\\]\\\\:bg-primary{\\n  background-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.group.toaster .group-\\\\[\\\\.toaster\\\\]\\\\:bg-background{\\n  background-color: hsl(var(--background));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:text-red-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group.toast .group-\\\\[\\\\.toast\\\\]\\\\:text-muted-foreground{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.group.toast .group-\\\\[\\\\.toast\\\\]\\\\:text-primary-foreground{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n\\r\\n.group.toaster .group-\\\\[\\\\.toaster\\\\]\\\\:text-foreground{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.group.toaster .group-\\\\[\\\\.toaster\\\\]\\\\:shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:border-destructive\\\\/30:hover{\\n  border-color: hsl(var(--destructive) / 0.3);\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:bg-destructive:hover{\\n  background-color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:text-destructive-foreground:hover{\\n  color: hsl(var(--destructive-foreground));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:text-red-50:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(254 242 242 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-destructive:focus{\\n  --tw-ring-color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-red-400:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-offset-red-600:focus{\\n  --tw-ring-offset-color: #dc2626;\\n}\\r\\n\\r\\n.peer\\\\/menu-button:hover ~ .peer-hover\\\\/menu-button\\\\:text-sidebar-accent-foreground{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:cursor-not-allowed{\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:opacity-70{\\n  opacity: 0.7;\\n}\\r\\n\\r\\n.has-\\\\[\\\\[data-variant\\\\=inset\\\\]\\\\]\\\\:bg-sidebar:has([data-variant=inset]){\\n  background-color: hsl(var(--sidebar-background));\\n}\\r\\n\\r\\n.has-\\\\[\\\\:disabled\\\\]\\\\:opacity-50:has(:disabled){\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.group\\\\/menu-item:has([data-sidebar=menu-action]) .group-has-\\\\[\\\\[data-sidebar\\\\=menu-action\\\\]\\\\]\\\\/menu-item\\\\:pr-8{\\n  padding-right: 2rem;\\n}\\r\\n\\r\\n.aria-disabled\\\\:pointer-events-none[aria-disabled=\\\"true\\\"]{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.aria-disabled\\\\:opacity-50[aria-disabled=\\\"true\\\"]{\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.aria-selected\\\\:bg-accent[aria-selected=\\\"true\\\"]{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.aria-selected\\\\:bg-accent\\\\/50[aria-selected=\\\"true\\\"]{\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\r\\n\\r\\n.aria-selected\\\\:text-accent-foreground[aria-selected=\\\"true\\\"]{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.aria-selected\\\\:text-muted-foreground[aria-selected=\\\"true\\\"]{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.aria-selected\\\\:opacity-100[aria-selected=\\\"true\\\"]{\\n  opacity: 1;\\n}\\r\\n\\r\\n.aria-selected\\\\:opacity-30[aria-selected=\\\"true\\\"]{\\n  opacity: 0.3;\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\=true\\\\]\\\\:pointer-events-none[data-disabled=\\\"true\\\"]{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:pointer-events-none[data-disabled]{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:h-px[data-panel-group-direction=\\\"vertical\\\"]{\\n  height: 1px;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:w-full[data-panel-group-direction=\\\"vertical\\\"]{\\n  width: 100%;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=bottom\\\\]\\\\:translate-y-1[data-side=\\\"bottom\\\"]{\\n  --tw-translate-y: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=left\\\\]\\\\:-translate-x-1[data-side=\\\"left\\\"]{\\n  --tw-translate-x: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=right\\\\]\\\\:translate-x-1[data-side=\\\"right\\\"]{\\n  --tw-translate-x: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=top\\\\]\\\\:-translate-y-1[data-side=\\\"top\\\"]{\\n  --tw-translate-y: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=checked\\\\]\\\\:translate-x-5[data-state=\\\"checked\\\"]{\\n  --tw-translate-x: 1.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=unchecked\\\\]\\\\:translate-x-0[data-state=\\\"unchecked\\\"]{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=cancel\\\\]\\\\:translate-x-0[data-swipe=\\\"cancel\\\"]{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=end\\\\]\\\\:translate-x-\\\\[var\\\\(--radix-toast-swipe-end-x\\\\)\\\\][data-swipe=\\\"end\\\"]{\\n  --tw-translate-x: var(--radix-toast-swipe-end-x);\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=move\\\\]\\\\:translate-x-\\\\[var\\\\(--radix-toast-swipe-move-x\\\\)\\\\][data-swipe=\\\"move\\\"]{\\n  --tw-translate-x: var(--radix-toast-swipe-move-x);\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n@keyframes accordion-up{\\r\\n\\r\\n  from{\\n    height: var(--radix-accordion-content-height);\\n  }\\r\\n\\r\\n  to{\\n    height: 0;\\n  }\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:animate-accordion-up[data-state=\\\"closed\\\"]{\\n  animation: accordion-up 0.2s ease-out;\\n}\\r\\n\\r\\n@keyframes accordion-down{\\r\\n\\r\\n  from{\\n    height: 0;\\n  }\\r\\n\\r\\n  to{\\n    height: var(--radix-accordion-content-height);\\n  }\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:animate-accordion-down[data-state=\\\"open\\\"]{\\n  animation: accordion-down 0.2s ease-out;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:flex-col[data-panel-group-direction=\\\"vertical\\\"]{\\n  flex-direction: column;\\n}\\r\\n\\r\\n.data-\\\\[active\\\\=true\\\\]\\\\:bg-sidebar-accent[data-active=\\\"true\\\"]{\\n  background-color: hsl(var(--sidebar-accent));\\n}\\r\\n\\r\\n.data-\\\\[active\\\\]\\\\:bg-accent\\\\/50[data-active]{\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\r\\n\\r\\n.data-\\\\[selected\\\\=\\\\'true\\\\'\\\\]\\\\:bg-accent[data-selected='true']{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:bg-background[data-state=\\\"active\\\"]{\\n  background-color: hsl(var(--background));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=checked\\\\]\\\\:bg-primary[data-state=\\\"checked\\\"]{\\n  background-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=on\\\\]\\\\:bg-accent[data-state=\\\"on\\\"]{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent[data-state=\\\"open\\\"]{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent\\\\/50[data-state=\\\"open\\\"]{\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-secondary[data-state=\\\"open\\\"]{\\n  background-color: hsl(var(--secondary));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=selected\\\\]\\\\:bg-muted[data-state=\\\"selected\\\"]{\\n  background-color: hsl(var(--muted));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=unchecked\\\\]\\\\:bg-input[data-state=\\\"unchecked\\\"]{\\n  background-color: hsl(var(--input));\\n}\\r\\n\\r\\n.data-\\\\[active\\\\=true\\\\]\\\\:font-medium[data-active=\\\"true\\\"]{\\n  font-weight: 500;\\n}\\r\\n\\r\\n.data-\\\\[active\\\\=true\\\\]\\\\:text-sidebar-accent-foreground[data-active=\\\"true\\\"]{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.data-\\\\[selected\\\\=true\\\\]\\\\:text-accent-foreground[data-selected=\\\"true\\\"]{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:text-foreground[data-state=\\\"active\\\"]{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=checked\\\\]\\\\:text-primary-foreground[data-state=\\\"checked\\\"]{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=on\\\\]\\\\:text-accent-foreground[data-state=\\\"on\\\"]{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:text-accent-foreground[data-state=\\\"open\\\"]{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:text-muted-foreground[data-state=\\\"open\\\"]{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\=true\\\\]\\\\:opacity-50[data-disabled=\\\"true\\\"]{\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:opacity-50[data-disabled]{\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:opacity-100[data-state=\\\"open\\\"]{\\n  opacity: 1;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:shadow-sm[data-state=\\\"active\\\"]{\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=move\\\\]\\\\:transition-none[data-swipe=\\\"move\\\"]{\\n  transition-property: none;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:duration-300[data-state=\\\"closed\\\"]{\\n  transition-duration: 300ms;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:duration-500[data-state=\\\"open\\\"]{\\n  transition-duration: 500ms;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\^\\\\=from-\\\\]\\\\:animate-in[data-motion^=\\\"from-\\\"]{\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:animate-in[data-state=\\\"open\\\"]{\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=visible\\\\]\\\\:animate-in[data-state=\\\"visible\\\"]{\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\^\\\\=to-\\\\]\\\\:animate-out[data-motion^=\\\"to-\\\"]{\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:animate-out[data-state=\\\"closed\\\"]{\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=hidden\\\\]\\\\:animate-out[data-state=\\\"hidden\\\"]{\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=end\\\\]\\\\:animate-out[data-swipe=\\\"end\\\"]{\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\^\\\\=from-\\\\]\\\\:fade-in[data-motion^=\\\"from-\\\"]{\\n  --tw-enter-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\^\\\\=to-\\\\]\\\\:fade-out[data-motion^=\\\"to-\\\"]{\\n  --tw-exit-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-0[data-state=\\\"closed\\\"]{\\n  --tw-exit-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-80[data-state=\\\"closed\\\"]{\\n  --tw-exit-opacity: 0.8;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=hidden\\\\]\\\\:fade-out[data-state=\\\"hidden\\\"]{\\n  --tw-exit-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:fade-in-0[data-state=\\\"open\\\"]{\\n  --tw-enter-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=visible\\\\]\\\\:fade-in[data-state=\\\"visible\\\"]{\\n  --tw-enter-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:zoom-out-95[data-state=\\\"closed\\\"]{\\n  --tw-exit-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-90[data-state=\\\"open\\\"]{\\n  --tw-enter-scale: .9;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-95[data-state=\\\"open\\\"]{\\n  --tw-enter-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\=from-end\\\\]\\\\:slide-in-from-right-52[data-motion=\\\"from-end\\\"]{\\n  --tw-enter-translate-x: 13rem;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\=from-start\\\\]\\\\:slide-in-from-left-52[data-motion=\\\"from-start\\\"]{\\n  --tw-enter-translate-x: -13rem;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\=to-end\\\\]\\\\:slide-out-to-right-52[data-motion=\\\"to-end\\\"]{\\n  --tw-exit-translate-x: 13rem;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\=to-start\\\\]\\\\:slide-out-to-left-52[data-motion=\\\"to-start\\\"]{\\n  --tw-exit-translate-x: -13rem;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=bottom\\\\]\\\\:slide-in-from-top-2[data-side=\\\"bottom\\\"]{\\n  --tw-enter-translate-y: -0.5rem;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=left\\\\]\\\\:slide-in-from-right-2[data-side=\\\"left\\\"]{\\n  --tw-enter-translate-x: 0.5rem;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=right\\\\]\\\\:slide-in-from-left-2[data-side=\\\"right\\\"]{\\n  --tw-enter-translate-x: -0.5rem;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=top\\\\]\\\\:slide-in-from-bottom-2[data-side=\\\"top\\\"]{\\n  --tw-enter-translate-y: 0.5rem;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-bottom[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-y: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-x: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left-1\\\\/2[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-right[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-x: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-right-full[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-x: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-y: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top-\\\\[48\\\\%\\\\][data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-y: -48%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-bottom[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-y: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-x: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left-1\\\\/2[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-right[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-x: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-y: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-\\\\[48\\\\%\\\\][data-state=\\\"open\\\"]{\\n  --tw-enter-translate-y: -48%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-full[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-y: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:duration-300[data-state=\\\"closed\\\"]{\\n  animation-duration: 300ms;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:duration-500[data-state=\\\"open\\\"]{\\n  animation-duration: 500ms;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:left-0[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  left: 0px;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:h-1[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  height: 0.25rem;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:w-full[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  width: 100%;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:-translate-y-1\\\\/2[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:translate-x-0[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:hover\\\\:bg-sidebar-accent:hover[data-state=\\\"open\\\"]{\\n  background-color: hsl(var(--sidebar-accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:hover\\\\:text-sidebar-accent-foreground:hover[data-state=\\\"open\\\"]{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:left-\\\\[calc\\\\(var\\\\(--sidebar-width\\\\)\\\\*-1\\\\)\\\\]{\\n  left: calc(var(--sidebar-width) * -1);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:right-\\\\[calc\\\\(var\\\\(--sidebar-width\\\\)\\\\*-1\\\\)\\\\]{\\n  right: calc(var(--sidebar-width) * -1);\\n}\\r\\n\\r\\n.group[data-side=\\\"left\\\"] .group-data-\\\\[side\\\\=left\\\\]\\\\:-right-4{\\n  right: -1rem;\\n}\\r\\n\\r\\n.group[data-side=\\\"right\\\"] .group-data-\\\\[side\\\\=right\\\\]\\\\:left-0{\\n  left: 0px;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:-mt-8{\\n  margin-top: -2rem;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:hidden{\\n  display: none;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:\\\\!size-8{\\n  width: 2rem !important;\\n  height: 2rem !important;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[--sidebar-width-icon\\\\]{\\n  width: var(--sidebar-width-icon);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[calc\\\\(var\\\\(--sidebar-width-icon\\\\)_\\\\+_theme\\\\(spacing\\\\.4\\\\)\\\\)\\\\]{\\n  width: calc(var(--sidebar-width-icon) + 1rem);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[calc\\\\(var\\\\(--sidebar-width-icon\\\\)_\\\\+_theme\\\\(spacing\\\\.4\\\\)_\\\\+2px\\\\)\\\\]{\\n  width: calc(var(--sidebar-width-icon) + 1rem + 2px);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:w-0{\\n  width: 0px;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:translate-x-0{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group[data-side=\\\"right\\\"] .group-data-\\\\[side\\\\=right\\\\]\\\\:rotate-180{\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group[data-state=\\\"open\\\"] .group-data-\\\\[state\\\\=open\\\\]\\\\:rotate-180{\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:overflow-hidden{\\n  overflow: hidden;\\n}\\r\\n\\r\\n.group[data-variant=\\\"floating\\\"] .group-data-\\\\[variant\\\\=floating\\\\]\\\\:rounded-lg{\\n  border-radius: var(--radius);\\n}\\r\\n\\r\\n.group[data-variant=\\\"floating\\\"] .group-data-\\\\[variant\\\\=floating\\\\]\\\\:border{\\n  border-width: 1px;\\n}\\r\\n\\r\\n.group[data-side=\\\"left\\\"] .group-data-\\\\[side\\\\=left\\\\]\\\\:border-r{\\n  border-right-width: 1px;\\n}\\r\\n\\r\\n.group[data-side=\\\"right\\\"] .group-data-\\\\[side\\\\=right\\\\]\\\\:border-l{\\n  border-left-width: 1px;\\n}\\r\\n\\r\\n.group[data-variant=\\\"floating\\\"] .group-data-\\\\[variant\\\\=floating\\\\]\\\\:border-sidebar-border{\\n  border-color: hsl(var(--sidebar-border));\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:\\\\!p-0{\\n  padding: 0px !important;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:\\\\!p-2{\\n  padding: 0.5rem !important;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:opacity-0{\\n  opacity: 0;\\n}\\r\\n\\r\\n.group[data-variant=\\\"floating\\\"] .group-data-\\\\[variant\\\\=floating\\\\]\\\\:shadow{\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:after\\\\:left-full::after{\\n  content: var(--tw-content);\\n  left: 100%;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:hover\\\\:bg-sidebar:hover{\\n  background-color: hsl(var(--sidebar-background));\\n}\\r\\n\\r\\n.peer\\\\/menu-button[data-size=\\\"default\\\"] ~ .peer-data-\\\\[size\\\\=default\\\\]\\\\/menu-button\\\\:top-1\\\\.5{\\n  top: 0.375rem;\\n}\\r\\n\\r\\n.peer\\\\/menu-button[data-size=\\\"lg\\\"] ~ .peer-data-\\\\[size\\\\=lg\\\\]\\\\/menu-button\\\\:top-2\\\\.5{\\n  top: 0.625rem;\\n}\\r\\n\\r\\n.peer\\\\/menu-button[data-size=\\\"sm\\\"] ~ .peer-data-\\\\[size\\\\=sm\\\\]\\\\/menu-button\\\\:top-1{\\n  top: 0.25rem;\\n}\\r\\n\\r\\n.peer[data-variant=\\\"inset\\\"] ~ .peer-data-\\\\[variant\\\\=inset\\\\]\\\\:min-h-\\\\[calc\\\\(100svh-theme\\\\(spacing\\\\.4\\\\)\\\\)\\\\]{\\n  min-height: calc(100svh - 1rem);\\n}\\r\\n\\r\\n.peer\\\\/menu-button[data-active=\\\"true\\\"] ~ .peer-data-\\\\[active\\\\=true\\\\]\\\\/menu-button\\\\:text-sidebar-accent-foreground{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.dark\\\\:border-destructive:is(.dark *){\\n  border-color: hsl(var(--destructive));\\n}\\r\\n\\r\\n@media (min-width: 640px){\\r\\n\\r\\n  .sm\\\\:bottom-0{\\n    bottom: 0px;\\n  }\\r\\n\\r\\n  .sm\\\\:right-0{\\n    right: 0px;\\n  }\\r\\n\\r\\n  .sm\\\\:top-auto{\\n    top: auto;\\n  }\\r\\n\\r\\n  .sm\\\\:mt-0{\\n    margin-top: 0px;\\n  }\\r\\n\\r\\n  .sm\\\\:flex{\\n    display: flex;\\n  }\\r\\n\\r\\n  .sm\\\\:max-w-sm{\\n    max-width: 24rem;\\n  }\\r\\n\\r\\n  .sm\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .sm\\\\:flex-row{\\n    flex-direction: row;\\n  }\\r\\n\\r\\n  .sm\\\\:flex-row-reverse{\\n    flex-direction: row-reverse;\\n  }\\r\\n\\r\\n  .sm\\\\:flex-col{\\n    flex-direction: column;\\n  }\\r\\n\\r\\n  .sm\\\\:justify-end{\\n    justify-content: flex-end;\\n  }\\r\\n\\r\\n  .sm\\\\:gap-2\\\\.5{\\n    gap: 0.625rem;\\n  }\\r\\n\\r\\n  .sm\\\\:space-x-2 > :not([hidden]) ~ :not([hidden]){\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\r\\n\\r\\n  .sm\\\\:space-x-4 > :not([hidden]) ~ :not([hidden]){\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\r\\n\\r\\n  .sm\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]){\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n  }\\r\\n\\r\\n  .sm\\\\:rounded-lg{\\n    border-radius: var(--radius);\\n  }\\r\\n\\r\\n  .sm\\\\:px-3{\\n    padding-left: 0.75rem;\\n    padding-right: 0.75rem;\\n  }\\r\\n\\r\\n  .sm\\\\:px-6{\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\r\\n\\r\\n  .sm\\\\:text-left{\\n    text-align: left;\\n  }\\r\\n\\r\\n  .data-\\\\[state\\\\=open\\\\]\\\\:sm\\\\:slide-in-from-bottom-full[data-state=\\\"open\\\"]{\\n    --tw-enter-translate-y: 100%;\\n  }\\n}\\r\\n\\r\\n@media (min-width: 768px){\\r\\n\\r\\n  .md\\\\:absolute{\\n    position: absolute;\\n  }\\r\\n\\r\\n  .md\\\\:mt-0{\\n    margin-top: 0px;\\n  }\\r\\n\\r\\n  .md\\\\:block{\\n    display: block;\\n  }\\r\\n\\r\\n  .md\\\\:flex{\\n    display: flex;\\n  }\\r\\n\\r\\n  .md\\\\:hidden{\\n    display: none;\\n  }\\r\\n\\r\\n  .md\\\\:w-\\\\[var\\\\(--radix-navigation-menu-viewport-width\\\\)\\\\]{\\n    width: var(--radix-navigation-menu-viewport-width);\\n  }\\r\\n\\r\\n  .md\\\\:w-auto{\\n    width: auto;\\n  }\\r\\n\\r\\n  .md\\\\:max-w-\\\\[420px\\\\]{\\n    max-width: 420px;\\n  }\\r\\n\\r\\n  .md\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .md\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .md\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .md\\\\:flex-row{\\n    flex-direction: row;\\n  }\\r\\n\\r\\n  .md\\\\:items-center{\\n    align-items: center;\\n  }\\r\\n\\r\\n  .md\\\\:justify-between{\\n    justify-content: space-between;\\n  }\\r\\n\\r\\n  .md\\\\:text-4xl{\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\r\\n\\r\\n  .md\\\\:text-5xl{\\n    font-size: 3rem;\\n    line-height: 1;\\n  }\\r\\n\\r\\n  .md\\\\:text-6xl{\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\r\\n\\r\\n  .md\\\\:text-sm{\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\r\\n\\r\\n  .md\\\\:opacity-0{\\n    opacity: 0;\\n  }\\r\\n\\r\\n  .after\\\\:md\\\\:hidden::after{\\n    content: var(--tw-content);\\n    display: none;\\n  }\\r\\n\\r\\n  .peer[data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:m-2{\\n    margin: 0.5rem;\\n  }\\r\\n\\r\\n  .peer[data-state=\\\"collapsed\\\"][data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[state\\\\=collapsed\\\\]\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:ml-2{\\n    margin-left: 0.5rem;\\n  }\\r\\n\\r\\n  .peer[data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:ml-0{\\n    margin-left: 0px;\\n  }\\r\\n\\r\\n  .peer[data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:rounded-xl{\\n    border-radius: 0.75rem;\\n  }\\r\\n\\r\\n  .peer[data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:shadow{\\n    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  }\\n}\\r\\n\\r\\n@media (min-width: 1024px){\\r\\n\\r\\n  .lg\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .lg\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .lg\\\\:px-8{\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\r\\n\\r\\n  .lg\\\\:text-2xl{\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:bg-accent:has([aria-selected]){\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.first\\\\:\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:rounded-l-md:has([aria-selected]):first-child{\\n  border-top-left-radius: calc(var(--radius) - 2px);\\n  border-bottom-left-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.last\\\\:\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:rounded-r-md:has([aria-selected]):last-child{\\n  border-top-right-radius: calc(var(--radius) - 2px);\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\.day-outside\\\\)\\\\]\\\\:bg-accent\\\\/50:has([aria-selected].day-outside){\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\.day-range-end\\\\)\\\\]\\\\:rounded-r-md:has([aria-selected].day-range-end){\\n  border-top-right-radius: calc(var(--radius) - 2px);\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\:has\\\\(\\\\[role\\\\=checkbox\\\\]\\\\)\\\\]\\\\:pr-0:has([role=checkbox]){\\n  padding-right: 0px;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>button\\\\]\\\\:hidden>button{\\n  display: none;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>span\\\\:last-child\\\\]\\\\:truncate>span:last-child{\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>span\\\\]\\\\:line-clamp-1>span{\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 1;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\+div\\\\]\\\\:translate-y-\\\\[-3px\\\\]>svg+div{\\n  --tw-translate-y: -3px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:absolute>svg{\\n  position: absolute;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:left-4>svg{\\n  left: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:top-4>svg{\\n  top: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:size-3\\\\.5>svg{\\n  width: 0.875rem;\\n  height: 0.875rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:size-4>svg{\\n  width: 1rem;\\n  height: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:h-2\\\\.5>svg{\\n  height: 0.625rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:h-3>svg{\\n  height: 0.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:w-2\\\\.5>svg{\\n  width: 0.625rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:w-3>svg{\\n  width: 0.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:shrink-0>svg{\\n  flex-shrink: 0;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-destructive>svg{\\n  color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-foreground>svg{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-muted-foreground>svg{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-sidebar-accent-foreground>svg{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\~\\\\*\\\\]\\\\:pl-7>svg~*{\\n  padding-left: 1.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>tr\\\\]\\\\:last\\\\:border-b-0:last-child>tr{\\n  border-bottom-width: 0px;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\[data-panel-group-direction\\\\=vertical\\\\]\\\\>div\\\\]\\\\:rotate-90[data-panel-group-direction=vertical]>div{\\n  --tw-rotate: 90deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\[data-state\\\\=open\\\\]\\\\>svg\\\\]\\\\:rotate-180[data-state=open]>svg{\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-cartesian-axis-tick_text\\\\]\\\\:fill-muted-foreground .recharts-cartesian-axis-tick text{\\n  fill: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-cartesian-grid_line\\\\[stroke\\\\=\\\\'\\\\#ccc\\\\'\\\\]\\\\]\\\\:stroke-border\\\\/50 .recharts-cartesian-grid line[stroke='#ccc']{\\n  stroke: hsl(var(--border) / 0.5);\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-curve\\\\.recharts-tooltip-cursor\\\\]\\\\:stroke-border .recharts-curve.recharts-tooltip-cursor{\\n  stroke: hsl(var(--border));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-dot\\\\[stroke\\\\=\\\\'\\\\#fff\\\\'\\\\]\\\\]\\\\:stroke-transparent .recharts-dot[stroke='#fff']{\\n  stroke: transparent;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-layer\\\\]\\\\:outline-none .recharts-layer{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-polar-grid_\\\\[stroke\\\\=\\\\'\\\\#ccc\\\\'\\\\]\\\\]\\\\:stroke-border .recharts-polar-grid [stroke='#ccc']{\\n  stroke: hsl(var(--border));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-radial-bar-background-sector\\\\]\\\\:fill-muted .recharts-radial-bar-background-sector{\\n  fill: hsl(var(--muted));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-rectangle\\\\.recharts-tooltip-cursor\\\\]\\\\:fill-muted .recharts-rectangle.recharts-tooltip-cursor{\\n  fill: hsl(var(--muted));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-reference-line_\\\\[stroke\\\\=\\\\'\\\\#ccc\\\\'\\\\]\\\\]\\\\:stroke-border .recharts-reference-line [stroke='#ccc']{\\n  stroke: hsl(var(--border));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-sector\\\\[stroke\\\\=\\\\'\\\\#fff\\\\'\\\\]\\\\]\\\\:stroke-transparent .recharts-sector[stroke='#fff']{\\n  stroke: transparent;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-sector\\\\]\\\\:outline-none .recharts-sector{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-surface\\\\]\\\\:outline-none .recharts-surface{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:px-2 [cmdk-group-heading]{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:py-1\\\\.5 [cmdk-group-heading]{\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:text-xs [cmdk-group-heading]{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:font-medium [cmdk-group-heading]{\\n  font-weight: 500;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:text-muted-foreground [cmdk-group-heading]{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group\\\\]\\\\:not\\\\(\\\\[hidden\\\\]\\\\)_\\\\~\\\\[cmdk-group\\\\]\\\\]\\\\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group]{\\n  padding-top: 0px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group\\\\]\\\\]\\\\:px-2 [cmdk-group]{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-input-wrapper\\\\]_svg\\\\]\\\\:h-5 [cmdk-input-wrapper] svg{\\n  height: 1.25rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-input-wrapper\\\\]_svg\\\\]\\\\:w-5 [cmdk-input-wrapper] svg{\\n  width: 1.25rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-input\\\\]\\\\]\\\\:h-12 [cmdk-input]{\\n  height: 3rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-item\\\\]\\\\]\\\\:px-2 [cmdk-item]{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-item\\\\]\\\\]\\\\:py-3 [cmdk-item]{\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-item\\\\]_svg\\\\]\\\\:h-5 [cmdk-item] svg{\\n  height: 1.25rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-item\\\\]_svg\\\\]\\\\:w-5 [cmdk-item] svg{\\n  width: 1.25rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_p\\\\]\\\\:leading-relaxed p{\\n  line-height: 1.625;\\n}\\r\\n\\r\\n.\\\\[\\\\&_svg\\\\]\\\\:pointer-events-none svg{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.\\\\[\\\\&_svg\\\\]\\\\:size-4 svg{\\n  width: 1rem;\\n  height: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_svg\\\\]\\\\:shrink-0 svg{\\n  flex-shrink: 0;\\n}\\r\\n\\r\\n.\\\\[\\\\&_tr\\\\:last-child\\\\]\\\\:border-0 tr:last-child{\\n  border-width: 0px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_tr\\\\]\\\\:border-b tr{\\n  border-bottom-width: 1px;\\n}\\r\\n\\r\\n[data-side=left][data-collapsible=offcanvas] .\\\\[\\\\[data-side\\\\=left\\\\]\\\\[data-collapsible\\\\=offcanvas\\\\]_\\\\&\\\\]\\\\:-right-2{\\n  right: -0.5rem;\\n}\\r\\n\\r\\n[data-side=left][data-state=collapsed] .\\\\[\\\\[data-side\\\\=left\\\\]\\\\[data-state\\\\=collapsed\\\\]_\\\\&\\\\]\\\\:cursor-e-resize{\\n  cursor: e-resize;\\n}\\r\\n\\r\\n[data-side=left] .\\\\[\\\\[data-side\\\\=left\\\\]_\\\\&\\\\]\\\\:cursor-w-resize{\\n  cursor: w-resize;\\n}\\r\\n\\r\\n[data-side=right][data-collapsible=offcanvas] .\\\\[\\\\[data-side\\\\=right\\\\]\\\\[data-collapsible\\\\=offcanvas\\\\]_\\\\&\\\\]\\\\:-left-2{\\n  left: -0.5rem;\\n}\\r\\n\\r\\n[data-side=right][data-state=collapsed] .\\\\[\\\\[data-side\\\\=right\\\\]\\\\[data-state\\\\=collapsed\\\\]_\\\\&\\\\]\\\\:cursor-w-resize{\\n  cursor: w-resize;\\n}\\r\\n\\r\\n[data-side=right] .\\\\[\\\\[data-side\\\\=right\\\\]_\\\\&\\\\]\\\\:cursor-e-resize{\\n  cursor: e-resize;\\n}\\r\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/index.css\"],\"names\":[],\"mappings\":\";AACA,uGAAuG;AACvG,uGAAuG;;AAEvG;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;;CAAc;;AAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;IAAA,uBAAc;IAAd,4BAAc;;IAAd,iBAAc;IAAd,iCAAc;;IAAd,oBAAc;IAAd,oCAAc;;IAAd,4BAAc;IAAd,iCAAc;;IAAd,0BAAc;IAAd,yCAAc;;IAAd,sBAAc;IAAd,qCAAc;;IAAd,uBAAc;IAAd,sCAAc;;IAAd,4BAAc;IAAd,qCAAc;;IAAd,2BAAc;IAAd,0BAAc;IAAd,sBAAc;;IAAd,gBAAc;;IAAd,8BAAc;IAAd,oCAAc;IAAd,+BAAc;IAAd,sCAAc;IAAd,gCAAc;IAAd,yCAAc;IAAd,6BAAc;IAAd,iCAAc;EAAA;;AAAd;IAAA,4BAAc;IAAd,yBAAc;;IAAd,sBAAc;IAAd,8BAAc;;IAAd,yBAAc;IAAd,iCAAc;;IAAd,sBAAc;IAAd,uCAAc;;IAAd,8BAAc;IAAd,mCAAc;;IAAd,0BAAc;IAAd,mCAAc;;IAAd,2BAAc;IAAd,gCAAc;;IAAd,4BAAc;IAAd,qCAAc;;IAAd,2BAAc;IAAd,0BAAc;IAAd,yBAAc;IAAd,kCAAc;IAAd,oCAAc;IAAd,kCAAc;IAAd,uCAAc;IAAd,gCAAc;IAAd,2CAAc;IAAd,gCAAc;IAAd,iCAAc;EAAA;;AAAd;EAAA;AAAc;;AAAd;EAAA,wCAAc;EAAd;AAAc;AAEd;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,SAAmB;EAAnB;AAAmB;AAAnB;EAAA,QAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,WAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yBAAmB;KAAnB,sBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,sEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kCAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iDAAmB;EAAnB,qDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,qCAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,yCAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,0BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA,mCAAmB;IAAnB;EAAmB;AAAA;AAAnB;;EAAA;IAAA,kCAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA,qBAAmB;EAAnB,yBAAmB;EAAnB,2BAAmB;EAAnB,yBAAmB;EAAnB,0BAAmB;EAAnB,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAEnB,uBAAuB;AACvB;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,4BAA4B;EAC9B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,qBAAqB;EACvB;EACA;IACE,UAAU;IACV,mBAAmB;EACrB;AACF;;AAEA;EACE;IACE,0BAA0B;EAC5B;EACA;IACE,4BAA4B;EAC9B;AACF;;AAEA;EACE;IACE,2BAA2B;EAC7B;EACA;IACE,6BAA6B;EAC/B;AACF;;AAEA;EACE;IACE,QAAQ;EACV;EACA;IACE,WAAW;EACb;AACF;;AAEA;EACE;IACE,yBAAyB;EAC3B;EACA;IACE,0BAA0B;EAC5B;AACF;;AAEA,sBAAsB;AACtB;EACE,0CAA0C;AAC5C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,yCAAyC;AAC3C;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,0BAA0B;EAC1B,0CAA0C;AAC5C;;AAEA;EACE,gBAAgB;EAChB,uBAAuB;EACvB,mBAAmB;EACnB,kEAAkE;AACpE;;AAEA,uBAAuB;AACvB,qBAAqB,qBAAqB,EAAE;AAC5C,qBAAqB,qBAAqB,EAAE;AAC5C,qBAAqB,qBAAqB,EAAE;AAC5C,qBAAqB,qBAAqB,EAAE;AAC5C,qBAAqB,qBAAqB,EAAE;;AAE5C,8BAA8B;AAC9B;EACE,uBAAuB;EACvB,wBAAwB;AAC1B;;AAEA,gCAAgC;AAChC;EACE,gCAAgC;AAClC;;AAEA;EACE,gCAAgC;AAClC;;AAEA,+BAA+B;AAC/B;EACE,yBAAyB;AAC3B;;AAEA,8BAA8B;AAC9B;EACE,mCAAmC;EACnC,kCAAkC;AACpC;;AAEA,oCAAoC;AACpC;EACE;;;IAGE,qCAAqC;IACrC,uCAAuC;IACvC,sCAAsC;IACtC,gCAAgC;EAClC;AACF;;AAEA,qBAAqB;AACrB;EACE,UAAU;AACZ;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,oDAAoD;EACpD,kBAAkB;AACpB;;AAEA;EACE,oDAAoD;AACtD;;AAEA,mBAAmB;AACnB;EACE,mCAAmC;EACnC,cAAc;AAChB;AAHA;EACE,mCAAmC;EACnC,cAAc;AAChB;;AAEA,iBAAiB;AACjB;EACE,0BAA0B;EAC1B,mBAAmB;AACrB;;AAEA,uBAAuB;AACvB;EACE,oBAAoB;EACpB,qBAAqB;EACrB,aAAa;EACb,4BAA4B;EAC5B,gBAAgB;AAClB;;AAEA,8BAA8B;AAC9B;EACE;;;oCAGkC;AACpC;;AAEA;EACE;;;oCAGkC;AACpC;;AAEA,uDAAuD;AACvD;EACE,yDAAyD;EACzD,+BAA+B;AACjC;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,uBAAuB;AACzB;;AAEA,8CAA8C;AAC9C;EACE,yDAAyD;EACzD,+BAA+B;AACjC;;AAEA,qCAAqC,qBAAqB,EAAE;AAC5D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,wBAAwB,EAAE;;AAE/D,oBAAoB;AACpB;EACE,+CAA+C;AACjD;;AAEA;EACE,gDAAgD;AAClD;;AAEA,sBAAsB;AACtB;EACE,UAAU;EACV,2BAA2B;AAC7B;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA;EACE,UAAU;EACV,4BAA4B;AAC9B;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA;EACE,UAAU;EACV,2BAA2B;AAC7B;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA;EACE,UAAU;EACV,qBAAqB;AACvB;;AAEA;EACE,UAAU;EACV,mBAAmB;AACrB;;AAEA,kDAAkD;AAClD;EACE,yDAAyD;EACzD,kCAAkC;AACpC;;AAEA;EACE,sDAAsD;EACtD;;sCAEoC;AACtC;;AAEA,qCAAqC;AACrC;EACE,gFAAgF;EAChF,0BAA0B;EAC1B,0EAA0E;EAC1E,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;EACrB,gCAAgC;AAClC;;AAEA;EACE;IACE,2BAA2B;EAC7B;EACA;IACE,6BAA6B;EAC/B;EACA;IACE,2BAA2B;EAC7B;AACF;;AAEA,qBAAqB;AACrB;EACE,gBAAgB;EAChB,+BAA+B;EAC/B,mBAAmB;EACnB,0EAA0E;AAC5E;;AAEA;EACE;IACE,QAAQ;EACV;EACA;IACE,WAAW;EACb;AACF;;AAEA;EACE;IACE,yBAAyB;EAC3B;EACA;IACE,qBAAqB;EACvB;AACF;;AAEA,6BAA6B;AAC7B;EACE,yDAAyD;EACzD,sBAAsB;AACxB;;AAEA;EACE,uCAAuC;EACvC,uBAAuB;AACzB;;AAEA,8BAA8B;AAC9B;EACE,yDAAyD;EACzD,oDAAoD;AACtD;;AAEA;EACE,uCAAuC;EACvC,2CAA2C;AAC7C;;AAEA;EACE,oCAAoC;EACpC,yBAAyB;AAC3B;;AAEA,kBAAkB;AAClB;EACE,kBAAkB;EAClB,6BAA6B;AAC/B;;AAEA;;EAEE,wBAAwB;EACxB,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;AACd;;AAEA;EACE,iCAAiC;EACjC,cAAc;EACd,WAAW;AACb;;AAEA;EACE,iCAAiC;EACjC,cAAc;EACd,WAAW;AACb;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,+BAA+B;EACjC;EACA;IACE,gCAAgC;EAClC;EACA;IACE,8BAA8B;EAChC;EACA;IACE,+BAA+B;EACjC;AACF;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,+BAA+B;EACjC;EACA;IACE,gCAAgC;EAClC;EACA;IACE,8BAA8B;EAChC;EACA;IACE,+BAA+B;EACjC;AACF;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,+BAA+B;EACjC;EACA;IACE,8BAA8B;EAChC;EACA;IACE,gCAAgC;EAClC;EACA;IACE,+BAA+B;EACjC;AACF;;AAEA,gBAAgB;AAChB;EACE,2BAA2B;AAC7B;;AAEA;EACE,mBAAmB;AACrB;;AAEA,sBAAsB;AACtB;EACE,uBAAuB;EACvB,oDAAoD;EACpD,6DAA6D;AAC/D;;AAEA;EACE,uBAAuB;EACvB,oDAAoD;EACpD,6DAA6D;AAC/D;;AAEA;EACE,uBAAuB;EACvB,sDAAsD;EACtD,+DAA+D;AACjE;;AAEA,sBAAsB;AACtB;EACE,WAAW;AACb;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,SAAS;AACX;;AAEA,qBAAqB;AACrB;EACE,eAAe;EACf,mBAAmB;AACrB;;AAEA;EACE,cAAc;EACd,oBAAoB;AACtB;;AAEA;EACE,eAAe;EACf,oBAAoB;AACtB;;AAEA;EACE,cAAc;EACd,qBAAqB;AACvB;;AAEA,uBAAuB;AACvB;EACE,iBAAiB;AACnB;;AAEA;EACE,gBAAgB;AAClB;;AAEA,cAAc;AACd;EACE,YAAY;AACd;;AAEA;EACE,WAAW;AACb;;AAEA,oBAAoB;AACpB;EACE,UAAU;EACV,QAAQ;AACV;;AAEA;EACE,WAAW;EACX,OAAO;AACT;;AAEA,sBAAsB;AACtB;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,gCAAgC;EAChC,mCAAmC;AACrC;;AAEA;EACE,0BAA0B;EAC1B,6BAA6B;EAC7B,+BAA+B;EAC/B,kCAAkC;AACpC;;AAEA,+FAA+F;;AA6F/F,oCAAoC;AACpC;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,0BAA0B;EAC5B;EACA;IACE,4BAA4B;EAC9B;AACF;;AAEA;EACE;IACE,wBAAwB;EAC1B;EACA;IACE,4BAA4B;EAC9B;AACF;;AAEA;EACE;IACE,YAAY;EACd;EACA;IACE,YAAY;EACd;AACF;;AAEA;EACE;IACE,0BAA0B;IAC1B,gCAAgC;EAClC;EACA;IACE,0BAA0B;IAC1B,iCAAiC;EACnC;AACF;;AAEA;EACE;IACE,oBAAoB;EACtB;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE,yCAAyC;AAC3C;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,sCAAsC;AACxC;;AAEA,2BAA2B;AAC3B;EACE,uBAAuB;AACzB;;AAEA,4BAA4B;AAC5B;EACE;;gEAE8D;EAC9D,0BAA0B;AAC5B;;AAEA,uBAAuB;AACvB;EACE,6DAA6D;EAC7D,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;AACvB;;AAEA,yBAAyB;AACzB;EACE,qCAAqC;EACrC,mCAA2B;UAA3B,2BAA2B;EAC3B,2CAA2C;AAC7C;;AAEA,qBAAqB;AACrB;EACE,UAAU;AACZ;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,oDAAoD;EACpD,kBAAkB;AACpB;;AAEA;EACE,oDAAoD;AACtD;;AAryBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,mBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA,QAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA,sBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,iDAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,kDAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,kBAsyBA;EAtyBA,kBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,iBAsyBA;EAtyBA,iBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,kBAsyBA;EAtyBA,kBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,kBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,kBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,kBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,kBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,4DAsyBA;EAtyBA,mEAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,4DAsyBA;EAtyBA,mEAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,gDAsyBA;EAtyBA,6DAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,iDAsyBA;EAtyBA,qDAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,6EAsyBA;EAtyBA,iGAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,gFAsyBA;EAtyBA,oGAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,sBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,8BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,2GAsyBA;EAtyBA,yGAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,8BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,2GAsyBA;EAtyBA,yGAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,2GAsyBA;EAtyBA,yGAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,kBAsyBA;EAtyBA,kBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,iBAsyBA;EAtyBA,iBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,+EAsyBA;EAtyBA,mGAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,yBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,yBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,yBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,qBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,qBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,gDAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,iDAsyBA;EAtyBA;AAsyBA;;AAtyBA;;EAAA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;AAAA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;;EAAA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;AAAA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,0CAsyBA;EAtyBA,uDAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,qBAsyBA;EAtyBA,yBAsyBA;EAtyBA,2BAsyBA;EAtyBA,yBAsyBA;EAtyBA,0BAsyBA;EAtyBA,+BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,qBAsyBA;EAtyBA,yBAsyBA;EAtyBA,2BAsyBA;EAtyBA,yBAsyBA;EAtyBA,0BAsyBA;EAtyBA,+BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,qBAsyBA;EAtyBA,yBAsyBA;EAtyBA,2BAsyBA;EAtyBA,yBAsyBA;EAtyBA,0BAsyBA;EAtyBA,+BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA,yBAsyBA;EAtyBA,0BAsyBA;EAtyBA,wBAsyBA;EAtyBA,yBAsyBA;EAtyBA,8BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA,yBAsyBA;EAtyBA,0BAsyBA;EAtyBA,wBAsyBA;EAtyBA,yBAsyBA;EAtyBA,8BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA,yBAsyBA;EAtyBA,0BAsyBA;EAtyBA,wBAsyBA;EAtyBA,yBAsyBA;EAtyBA,8BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA,yBAsyBA;EAtyBA,0BAsyBA;EAtyBA,wBAsyBA;EAtyBA,yBAsyBA;EAtyBA,8BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA,sBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA,qBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,sBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,qBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,mBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,mBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,0EAsyBA;EAtyBA,8FAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,0BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;;EAAA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA,uBAsyBA;IAtyBA,sDAsyBA;IAtyBA;EAsyBA;;EAtyBA;IAAA,uBAsyBA;IAtyBA,oDAsyBA;IAtyBA;EAsyBA;;EAtyBA;IAAA,uBAsyBA;IAtyBA,2DAsyBA;IAtyBA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA,qBAsyBA;IAtyBA;EAsyBA;;EAtyBA;IAAA,oBAsyBA;IAtyBA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;AAAA;;AAtyBA;;EAAA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA,kBAsyBA;IAtyBA;EAsyBA;;EAtyBA;IAAA,eAsyBA;IAtyBA;EAsyBA;;EAtyBA;IAAA,kBAsyBA;IAtyBA;EAsyBA;;EAtyBA;IAAA,mBAsyBA;IAtyBA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA,0BAsyBA;IAtyBA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA,0EAsyBA;IAtyBA,8FAsyBA;IAtyBA;EAsyBA;AAAA;;AAtyBA;;EAAA;IAAA;EAsyBA;;EAtyBA;IAAA;EAsyBA;;EAtyBA;IAAA,kBAsyBA;IAtyBA;EAsyBA;;EAtyBA;IAAA,iBAsyBA;IAtyBA;EAsyBA;AAAA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,iDAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,kDAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,kDAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,gBAsyBA;EAtyBA,uBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,gBAsyBA;EAtyBA,oBAsyBA;EAtyBA,4BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,sBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,eAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,WAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,kBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,mBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,8BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,8BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,8BAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,qBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,kBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA,oBAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA,WAsyBA;EAtyBA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA;;AAtyBA;EAAA;AAsyBA\",\"sourcesContent\":[\"\\r\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');\\r\\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');\\r\\n\\r\\n@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n\\r\\n/* Elegant Animations */\\r\\n@keyframes fadeInUp {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(30px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes fadeInLeft {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateX(-30px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateX(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes fadeInRight {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateX(30px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateX(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes scaleIn {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: scale(0.9);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: scale(1);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes float {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-10px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes gradient-shift {\\r\\n  0%, 100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes typing {\\r\\n  from {\\r\\n    width: 0;\\r\\n  }\\r\\n  to {\\r\\n    width: 100%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes blink {\\r\\n  0%, 50% {\\r\\n    border-color: transparent;\\r\\n  }\\r\\n  51%, 100% {\\r\\n    border-color: currentColor;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Animation Classes */\\r\\n.animate-fadeInUp {\\r\\n  animation: fadeInUp 0.8s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-fadeInLeft {\\r\\n  animation: fadeInLeft 0.8s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-fadeInRight {\\r\\n  animation: fadeInRight 0.8s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-scaleIn {\\r\\n  animation: scaleIn 0.6s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-float {\\r\\n  animation: float 3s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-gradient {\\r\\n  background-size: 200% 200%;\\r\\n  animation: gradient-shift 4s ease infinite;\\r\\n}\\r\\n\\r\\n.animate-typing {\\r\\n  overflow: hidden;\\r\\n  border-right: 2px solid;\\r\\n  white-space: nowrap;\\r\\n  animation: typing 3s steps(40, end), blink 0.75s step-end infinite;\\r\\n}\\r\\n\\r\\n/* Stagger animations */\\r\\n.animate-stagger-1 { animation-delay: 0.1s; }\\r\\n.animate-stagger-2 { animation-delay: 0.2s; }\\r\\n.animate-stagger-3 { animation-delay: 0.3s; }\\r\\n.animate-stagger-4 { animation-delay: 0.4s; }\\r\\n.animate-stagger-5 { animation-delay: 0.5s; }\\r\\n\\r\\n/* Enhanced smooth scrolling */\\r\\nhtml {\\r\\n  scroll-behavior: smooth;\\r\\n  scroll-padding-top: 80px;\\r\\n}\\r\\n\\r\\n/* Language transition effects */\\r\\n[dir=\\\"rtl\\\"] {\\r\\n  transition: all 0.3s ease-in-out;\\r\\n}\\r\\n\\r\\n[dir=\\\"ltr\\\"] {\\r\\n  transition: all 0.3s ease-in-out;\\r\\n}\\r\\n\\r\\n/* Text direction transitions */\\r\\n.text-transition {\\r\\n  transition: all 0.3s ease;\\r\\n}\\r\\n\\r\\n/* Performance optimizations */\\r\\n* {\\r\\n  -webkit-font-smoothing: antialiased;\\r\\n  -moz-osx-font-smoothing: grayscale;\\r\\n}\\r\\n\\r\\n/* Reduce motion for accessibility */\\r\\n@media (prefers-reduced-motion: reduce) {\\r\\n  *,\\r\\n  *::before,\\r\\n  *::after {\\r\\n    animation-duration: 0.01ms !important;\\r\\n    animation-iteration-count: 1 !important;\\r\\n    transition-duration: 0.01ms !important;\\r\\n    scroll-behavior: auto !important;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: #f1f1f1;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: linear-gradient(45deg, #3b82f6, #8b5cf6);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: linear-gradient(45deg, #2563eb, #7c3aed);\\r\\n}\\r\\n\\r\\n/* Text selection */\\r\\n::selection {\\r\\n  background: rgba(59, 130, 246, 0.3);\\r\\n  color: inherit;\\r\\n}\\r\\n\\r\\n/* Focus styles */\\r\\n.focus-visible:focus {\\r\\n  outline: 2px solid #3b82f6;\\r\\n  outline-offset: 2px;\\r\\n}\\r\\n\\r\\n/* Line clamp utility */\\r\\n.line-clamp-3 {\\r\\n  display: -webkit-box;\\r\\n  -webkit-line-clamp: 3;\\r\\n  line-clamp: 3;\\r\\n  -webkit-box-orient: vertical;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n/* Custom avatar glow effect */\\r\\n.avatar-glow {\\r\\n  box-shadow:\\r\\n    0 0 20px rgba(59, 130, 246, 0.3),\\r\\n    0 0 40px rgba(139, 92, 246, 0.2),\\r\\n    0 0 60px rgba(236, 72, 153, 0.1);\\r\\n}\\r\\n\\r\\n.avatar-glow:hover {\\r\\n  box-shadow:\\r\\n    0 0 30px rgba(59, 130, 246, 0.4),\\r\\n    0 0 60px rgba(139, 92, 246, 0.3),\\r\\n    0 0 90px rgba(236, 72, 153, 0.2);\\r\\n}\\r\\n\\r\\n/* Scroll Animation Classes - Enhanced for smoothness */\\r\\n.scroll-animate {\\r\\n  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, opacity;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-100 {\\r\\n  transition-delay: 150ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-200 {\\r\\n  transition-delay: 300ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-300 {\\r\\n  transition-delay: 450ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-400 {\\r\\n  transition-delay: 600ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-500 {\\r\\n  transition-delay: 750ms;\\r\\n}\\r\\n\\r\\n/* Stagger animation for children - Enhanced */\\r\\n.stagger-children > * {\\r\\n  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, opacity;\\r\\n}\\r\\n\\r\\n.stagger-children > *:nth-child(1) { transition-delay: 0ms; }\\r\\n.stagger-children > *:nth-child(2) { transition-delay: 150ms; }\\r\\n.stagger-children > *:nth-child(3) { transition-delay: 300ms; }\\r\\n.stagger-children > *:nth-child(4) { transition-delay: 450ms; }\\r\\n.stagger-children > *:nth-child(5) { transition-delay: 600ms; }\\r\\n.stagger-children > *:nth-child(6) { transition-delay: 750ms; }\\r\\n.stagger-children > *:nth-child(7) { transition-delay: 900ms; }\\r\\n.stagger-children > *:nth-child(8) { transition-delay: 1050ms; }\\r\\n\\r\\n/* Parallax effect */\\r\\n.parallax-slow {\\r\\n  transform: translateY(var(--scroll-y, 0) * 0.5);\\r\\n}\\r\\n\\r\\n.parallax-fast {\\r\\n  transform: translateY(var(--scroll-y, 0) * -0.3);\\r\\n}\\r\\n\\r\\n/* Reveal animations */\\r\\n.reveal-up {\\r\\n  opacity: 0;\\r\\n  transform: translateY(50px);\\r\\n}\\r\\n\\r\\n.reveal-up.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.reveal-left {\\r\\n  opacity: 0;\\r\\n  transform: translateX(-50px);\\r\\n}\\r\\n\\r\\n.reveal-left.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateX(0);\\r\\n}\\r\\n\\r\\n.reveal-right {\\r\\n  opacity: 0;\\r\\n  transform: translateX(50px);\\r\\n}\\r\\n\\r\\n.reveal-right.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateX(0);\\r\\n}\\r\\n\\r\\n.reveal-scale {\\r\\n  opacity: 0;\\r\\n  transform: scale(0.8);\\r\\n}\\r\\n\\r\\n.reveal-scale.revealed {\\r\\n  opacity: 1;\\r\\n  transform: scale(1);\\r\\n}\\r\\n\\r\\n/* Hover effects for cards - Enhanced smoothness */\\r\\n.card-hover {\\r\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, box-shadow;\\r\\n}\\r\\n\\r\\n.card-hover:hover {\\r\\n  transform: translateY(-12px) scale(1.03) rotateX(2deg);\\r\\n  box-shadow:\\r\\n    0 32px 64px -12px rgba(0, 0, 0, 0.25),\\r\\n    0 0 0 1px rgba(255, 255, 255, 0.1);\\r\\n}\\r\\n\\r\\n/* Enhanced gradient text animation */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #ec4899, #10b981, #f59e0b);\\r\\n  background-size: 400% 400%;\\r\\n  animation: gradient-shift 8s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  will-change: background-position;\\r\\n}\\r\\n\\r\\n@keyframes gradient-shift {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Typing animation */\\r\\n.typing-animation {\\r\\n  overflow: hidden;\\r\\n  border-right: 2px solid #3b82f6;\\r\\n  white-space: nowrap;\\r\\n  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;\\r\\n}\\r\\n\\r\\n@keyframes typing {\\r\\n  from {\\r\\n    width: 0;\\r\\n  }\\r\\n  to {\\r\\n    width: 100%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes blink-caret {\\r\\n  from, to {\\r\\n    border-color: transparent;\\r\\n  }\\r\\n  50% {\\r\\n    border-color: #3b82f6;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Enhanced magnetic effect */\\r\\n.magnetic {\\r\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform;\\r\\n}\\r\\n\\r\\n.magnetic:hover {\\r\\n  transform: scale(1.08) translateY(-2px);\\r\\n  filter: brightness(1.1);\\r\\n}\\r\\n\\r\\n/* Smooth button transitions */\\r\\n.btn-smooth {\\r\\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, box-shadow, background-color;\\r\\n}\\r\\n\\r\\n.btn-smooth:hover {\\r\\n  transform: translateY(-2px) scale(1.02);\\r\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\\r\\n}\\r\\n\\r\\n.btn-smooth:active {\\r\\n  transform: translateY(0) scale(0.98);\\r\\n  transition-duration: 0.1s;\\r\\n}\\r\\n\\r\\n/* Glitch effect */\\r\\n.glitch {\\r\\n  position: relative;\\r\\n  animation: glitch 2s infinite;\\r\\n}\\r\\n\\r\\n.glitch::before,\\r\\n.glitch::after {\\r\\n  content: attr(data-text);\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n}\\r\\n\\r\\n.glitch::before {\\r\\n  animation: glitch-1 0.5s infinite;\\r\\n  color: #ff0000;\\r\\n  z-index: -1;\\r\\n}\\r\\n\\r\\n.glitch::after {\\r\\n  animation: glitch-2 0.5s infinite;\\r\\n  color: #00ff00;\\r\\n  z-index: -2;\\r\\n}\\r\\n\\r\\n@keyframes glitch {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(-2px, 2px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(-2px, -2px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(2px, 2px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(2px, -2px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes glitch-1 {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(-1px, 1px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(-1px, -1px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(1px, 1px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(1px, -1px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes glitch-2 {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(1px, -1px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(1px, 1px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(-1px, -1px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(-1px, 1px);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* RTL Support */\\r\\n[dir=\\\"rtl\\\"] .flex-row {\\r\\n  flex-direction: row-reverse;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .flex-row-reverse {\\r\\n  flex-direction: row;\\r\\n}\\r\\n\\r\\n/* RTL Space between */\\r\\n[dir=\\\"rtl\\\"] .space-x-8 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n/* RTL Gap utilities */\\r\\n[dir=\\\"rtl\\\"] .gap-2 {\\r\\n  gap: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .gap-3 {\\r\\n  gap: 0.75rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .gap-4 {\\r\\n  gap: 1rem;\\r\\n}\\r\\n\\r\\n/* RTL Icon spacing */\\r\\n[dir=\\\"rtl\\\"] .mr-2 {\\r\\n  margin-right: 0;\\r\\n  margin-left: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .ml-2 {\\r\\n  margin-left: 0;\\r\\n  margin-right: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .mr-3 {\\r\\n  margin-right: 0;\\r\\n  margin-left: 0.75rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .ml-3 {\\r\\n  margin-left: 0;\\r\\n  margin-right: 0.75rem;\\r\\n}\\r\\n\\r\\n/* RTL Text alignment */\\r\\n[dir=\\\"rtl\\\"] .text-left {\\r\\n  text-align: right;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .text-right {\\r\\n  text-align: left;\\r\\n}\\r\\n\\r\\n/* RTL Float */\\r\\n[dir=\\\"rtl\\\"] .float-left {\\r\\n  float: right;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .float-right {\\r\\n  float: left;\\r\\n}\\r\\n\\r\\n/* RTL Positioning */\\r\\n[dir=\\\"rtl\\\"] .left-0 {\\r\\n  left: auto;\\r\\n  right: 0;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .right-0 {\\r\\n  right: auto;\\r\\n  left: 0;\\r\\n}\\r\\n\\r\\n/* RTL Border radius */\\r\\n[dir=\\\"rtl\\\"] .rounded-l {\\r\\n  border-top-left-radius: 0;\\r\\n  border-bottom-left-radius: 0;\\r\\n  border-top-right-radius: 0.25rem;\\r\\n  border-bottom-right-radius: 0.25rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .rounded-r {\\r\\n  border-top-right-radius: 0;\\r\\n  border-bottom-right-radius: 0;\\r\\n  border-top-left-radius: 0.25rem;\\r\\n  border-bottom-left-radius: 0.25rem;\\r\\n}\\r\\n\\r\\n/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */\\r\\n\\r\\n@layer base {\\r\\n  :root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --primary: 222.2 47.4% 11.2%;\\r\\n    --primary-foreground: 210 40% 98%;\\r\\n\\r\\n    --secondary: 210 40% 96.1%;\\r\\n    --secondary-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --muted: 210 40% 96.1%;\\r\\n    --muted-foreground: 215.4 16.3% 46.9%;\\r\\n\\r\\n    --accent: 210 40% 96.1%;\\r\\n    --accent-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 210 40% 98%;\\r\\n\\r\\n    --border: 214.3 31.8% 91.4%;\\r\\n    --input: 214.3 31.8% 91.4%;\\r\\n    --ring: 222.2 84% 4.9%;\\r\\n\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    --sidebar-background: 0 0% 98%;\\r\\n    --sidebar-foreground: 240 5.3% 26.1%;\\r\\n    --sidebar-primary: 240 5.9% 10%;\\r\\n    --sidebar-primary-foreground: 0 0% 98%;\\r\\n    --sidebar-accent: 240 4.8% 95.9%;\\r\\n    --sidebar-accent-foreground: 240 5.9% 10%;\\r\\n    --sidebar-border: 220 13% 91%;\\r\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\r\\n  }\\r\\n\\r\\n  .dark {\\r\\n    --background: 222.2 84% 4.9%;\\r\\n    --foreground: 210 40% 98%;\\r\\n\\r\\n    --card: 222.2 84% 4.9%;\\r\\n    --card-foreground: 210 40% 98%;\\r\\n\\r\\n    --popover: 222.2 84% 4.9%;\\r\\n    --popover-foreground: 210 40% 98%;\\r\\n\\r\\n    --primary: 210 40% 98%;\\r\\n    --primary-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --secondary: 217.2 32.6% 17.5%;\\r\\n    --secondary-foreground: 210 40% 98%;\\r\\n\\r\\n    --muted: 217.2 32.6% 17.5%;\\r\\n    --muted-foreground: 215 20.2% 65.1%;\\r\\n\\r\\n    --accent: 217.2 32.6% 17.5%;\\r\\n    --accent-foreground: 210 40% 98%;\\r\\n\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 210 40% 98%;\\r\\n\\r\\n    --border: 217.2 32.6% 17.5%;\\r\\n    --input: 217.2 32.6% 17.5%;\\r\\n    --ring: 212.7 26.8% 83.9%;\\r\\n    --sidebar-background: 240 5.9% 10%;\\r\\n    --sidebar-foreground: 240 4.8% 95.9%;\\r\\n    --sidebar-primary: 224.3 76.3% 48%;\\r\\n    --sidebar-primary-foreground: 0 0% 100%;\\r\\n    --sidebar-accent: 240 3.7% 15.9%;\\r\\n    --sidebar-accent-foreground: 240 4.8% 95.9%;\\r\\n    --sidebar-border: 240 3.7% 15.9%;\\r\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@layer base {\\r\\n  * {\\r\\n    @apply border-border;\\r\\n  }\\r\\n\\r\\n  body {\\r\\n    @apply bg-background text-foreground;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Enhanced animations and effects */\\r\\n@keyframes fade-in {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(30px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes float {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-20px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes bounce-slow {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-25px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes pulse-slow {\\r\\n  0%, 100% {\\r\\n    opacity: 0.4;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 0.8;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes gradient-x {\\r\\n  0%, 100% {\\r\\n    background-size: 200% 200%;\\r\\n    background-position: left center;\\r\\n  }\\r\\n  50% {\\r\\n    background-size: 200% 200%;\\r\\n    background-position: right center;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes scale-x-100 {\\r\\n  from {\\r\\n    transform: scaleX(0);\\r\\n  }\\r\\n  to {\\r\\n    transform: scaleX(1);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-fade-in {\\r\\n  animation: fade-in 0.8s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-float {\\r\\n  animation: float 6s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-bounce-slow {\\r\\n  animation: bounce-slow 3s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-pulse-slow {\\r\\n  animation: pulse-slow 4s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-gradient-x {\\r\\n  animation: gradient-x 3s ease infinite;\\r\\n}\\r\\n\\r\\n/* Smooth scroll behavior */\\r\\nhtml {\\r\\n  scroll-behavior: smooth;\\r\\n}\\r\\n\\r\\n/* Grid pattern background */\\r\\n.bg-grid-pattern {\\r\\n  background-image: \\r\\n    linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),\\r\\n    linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n}\\r\\n\\r\\n/* Enhanced gradients */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Glassmorphism effect */\\r\\n.glass-effect {\\r\\n  background: rgba(255, 255, 255, 0.25);\\r\\n  backdrop-filter: blur(10px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.18);\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: #f1f1f1;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: linear-gradient(45deg, #667eea, #764ba2);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: linear-gradient(45deg, #5a6fd8, #6a419a);\\r\\n}\\r\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/index.css\n"));

/***/ })

});