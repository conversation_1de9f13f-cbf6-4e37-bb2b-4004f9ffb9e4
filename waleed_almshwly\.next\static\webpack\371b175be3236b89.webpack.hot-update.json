{"c": ["pages/index", "webpack"], "r": [], "m": ["./node_modules/@radix-ui/react-label/dist/index.mjs", "./node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js", "./node_modules/@supabase/auth-js/dist/module/AuthClient.js", "./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js", "./node_modules/@supabase/auth-js/dist/module/GoTrueClient.js", "./node_modules/@supabase/auth-js/dist/module/index.js", "./node_modules/@supabase/auth-js/dist/module/lib/base64url.js", "./node_modules/@supabase/auth-js/dist/module/lib/constants.js", "./node_modules/@supabase/auth-js/dist/module/lib/errors.js", "./node_modules/@supabase/auth-js/dist/module/lib/fetch.js", "./node_modules/@supabase/auth-js/dist/module/lib/helpers.js", "./node_modules/@supabase/auth-js/dist/module/lib/local-storage.js", "./node_modules/@supabase/auth-js/dist/module/lib/locks.js", "./node_modules/@supabase/auth-js/dist/module/lib/polyfills.js", "./node_modules/@supabase/auth-js/dist/module/lib/types.js", "./node_modules/@supabase/auth-js/dist/module/lib/version.js", "./node_modules/@supabase/functions-js/dist/module/FunctionsClient.js", "./node_modules/@supabase/functions-js/dist/module/helper.js", "./node_modules/@supabase/functions-js/dist/module/index.js", "./node_modules/@supabase/functions-js/dist/module/types.js", "./node_modules/@supabase/node-fetch/browser.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js", "./node_modules/@supabase/postgrest-js/dist/cjs/constants.js", "./node_modules/@supabase/postgrest-js/dist/cjs/index.js", "./node_modules/@supabase/postgrest-js/dist/cjs/version.js", "./node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs", "./node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js", "./node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js", "./node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js", "./node_modules/@supabase/realtime-js/dist/module/WebSocket.js", "./node_modules/@supabase/realtime-js/dist/module/index.js", "./node_modules/@supabase/realtime-js/dist/module/lib/constants.js", "./node_modules/@supabase/realtime-js/dist/module/lib/push.js", "./node_modules/@supabase/realtime-js/dist/module/lib/serializer.js", "./node_modules/@supabase/realtime-js/dist/module/lib/timer.js", "./node_modules/@supabase/realtime-js/dist/module/lib/transformers.js", "./node_modules/@supabase/realtime-js/dist/module/lib/version.js", "./node_modules/@supabase/storage-js/dist/module/StorageClient.js", "./node_modules/@supabase/storage-js/dist/module/index.js", "./node_modules/@supabase/storage-js/dist/module/lib/constants.js", "./node_modules/@supabase/storage-js/dist/module/lib/errors.js", "./node_modules/@supabase/storage-js/dist/module/lib/fetch.js", "./node_modules/@supabase/storage-js/dist/module/lib/helpers.js", "./node_modules/@supabase/storage-js/dist/module/lib/types.js", "./node_modules/@supabase/storage-js/dist/module/lib/version.js", "./node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js", "./node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js", "./node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js", "./node_modules/@supabase/supabase-js/dist/module/index.js", "./node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js", "./node_modules/@supabase/supabase-js/dist/module/lib/constants.js", "./node_modules/@supabase/supabase-js/dist/module/lib/fetch.js", "./node_modules/@supabase/supabase-js/dist/module/lib/helpers.js", "./node_modules/@supabase/supabase-js/dist/module/lib/version.js", "./node_modules/lucide-react/dist/esm/icons/clock.js", "./node_modules/lucide-react/dist/esm/icons/eye.js", "./node_modules/lucide-react/dist/esm/icons/file-text.js", "./node_modules/lucide-react/dist/esm/icons/git-branch.js", "./node_modules/lucide-react/dist/esm/icons/message-square.js", "./node_modules/lucide-react/dist/esm/icons/send.js", "./node_modules/lucide-react/dist/esm/icons/star.js", "./node_modules/lucide-react/dist/esm/icons/user.js", "./node_modules/next/dist/build/polyfills/process.js", "./node_modules/next/dist/compiled/buffer/index.js", "./node_modules/next/dist/compiled/process/browser.js", "./node_modules/ws/browser.js", "./src/components/ContactForm.tsx", "./src/components/icons/SocialIcons.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/card.tsx", "./src/components/ui/label.tsx", "./src/hooks/useContactForm.tsx", "./src/hooks/useScrollAnimation.tsx", "./src/integrations/supabase/client.ts", "__barrel_optimize__?names=Clock,Mail,MapPin,Phone!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=ExternalLink,Eye,GitBranch,Star!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=FileText,Mail,MessageSquare,Send,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js"]}