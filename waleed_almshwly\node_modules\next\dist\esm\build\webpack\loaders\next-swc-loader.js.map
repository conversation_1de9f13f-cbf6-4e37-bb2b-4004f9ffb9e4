{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-swc-loader.ts"], "names": ["isWasm", "transform", "getLoaderSWCOptions", "path", "isAbsolute", "babelIncludeRegexes", "isResourceInPackages", "maybeExclude", "excludePath", "transpilePackages", "some", "r", "test", "shouldBeBundled", "includes", "FORCE_TRANSPILE_CONDITIONS", "loaderTransform", "parentTrace", "source", "inputSourceMap", "nextConfig", "filename", "resourcePath", "loaderOptions", "getOptions", "shouldMaybeExclude", "Error", "isServer", "rootDir", "pagesDir", "appDir", "hasReactRefresh", "jsConfig", "supportedBrowsers", "swcCacheDir", "serverComponents", "bundleLayer", "esm", "isPageFile", "startsWith", "relativeFilePathFromRoot", "relative", "swcOptions", "development", "mode", "modularizeImports", "optimizePackageImports", "experimental", "swcPlugins", "compilerOptions", "compiler", "optimizeServerReact", "programmaticOptions", "JSON", "stringify", "undefined", "sourceMaps", "sourceMap", "inlineSourcesContent", "sourceFileName", "jsc", "react", "Object", "prototype", "hasOwnProperty", "call", "swcSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "then", "output", "eliminatedPackages", "pkg", "parse", "add", "code", "map", "EXCLUDED_PATHS", "pitch", "callback", "async", "process", "versions", "pnp", "loaders", "length", "loaderIndex", "loaderSpan", "currentTraceSpan", "addDependency", "sw<PERSON><PERSON><PERSON><PERSON>", "inputSource", "transformedSource", "outputSourceMap", "err", "raw"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,GAIA,SAASA,MAAM,EAAEC,SAAS,QAAQ,YAAW;AAC7C,SAASC,mBAAmB,QAAQ,oBAAmB;AACvD,OAAOC,QAAQC,UAAU,QAAQ,OAAM;AACvC,SAASC,mBAAmB,QAAQ,uBAAsB;AAC1D,SAASC,oBAAoB,QAAQ,yBAAwB;AAE7D,MAAMC,eAAe,CACnBC,aACAC;IAEA,IAAIJ,oBAAoBK,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,CAACJ,eAAe;QACxD,OAAO;IACT;IAEA,MAAMK,kBAAkBP,qBAAqBE,aAAaC;IAC1D,IAAII,iBAAiB,OAAO;IAE5B,OAAOL,YAAYM,QAAQ,CAAC;AAC9B;AAmBA,0CAA0C;AAC1C,2CAA2C;AAC3C,MAAMC,6BACJ;AAEF,eAAeC,gBAEbC,WAAgB,EAChBC,MAAe,EACfC,cAAoB;QA+CMC,0BACZA,2BAESA;IAhDvB,wBAAwB;IACxB,MAAMC,WAAW,IAAI,CAACC,YAAY;IAElC,IAAIC,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAC5D,MAAMC,qBAAqBlB,aACzBc,UACAE,cAAcd,iBAAiB,IAAI,EAAE;IAGvC,IAAIgB,oBAAoB;QACtB,IAAI,CAACP,QAAQ;YACX,MAAM,IAAIQ,MAAM,CAAC,8CAA8C,CAAC;QAClE;QAEA,IAAI,CAACX,2BAA2BH,IAAI,CAACM,SAAS;YAC5C,OAAO;gBAACA;gBAAQC;aAAe;QACjC;IACF;IAEA,MAAM,EACJQ,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfX,UAAU,EACVY,QAAQ,EACRC,iBAAiB,EACjBC,WAAW,EACXC,gBAAgB,EAChBC,WAAW,EACXC,GAAG,EACJ,GAAGd;IACJ,MAAMe,aAAajB,SAASkB,UAAU,CAACV;IACvC,MAAMW,2BAA2BrC,KAAKsC,QAAQ,CAACb,SAASP;IAExD,MAAMqB,aAAaxC,oBAAoB;QACrC2B;QACAC;QACAT;QACAM;QACAW;QACAK,aAAa,IAAI,CAACC,IAAI,KAAK;QAC3Bb;QACAc,iBAAiB,EAAEzB,8BAAAA,WAAYyB,iBAAiB;QAChDC,sBAAsB,EAAE1B,+BAAAA,2BAAAA,WAAY2B,YAAY,qBAAxB3B,yBAA0B0B,sBAAsB;QACxEE,UAAU,EAAE5B,+BAAAA,4BAAAA,WAAY2B,YAAY,qBAAxB3B,0BAA0B4B,UAAU;QAChDC,eAAe,EAAE7B,8BAAAA,WAAY8B,QAAQ;QACrCC,mBAAmB,EAAE/B,+BAAAA,4BAAAA,WAAY2B,YAAY,qBAAxB3B,0BAA0B+B,mBAAmB;QAClEnB;QACAC;QACAC;QACAM;QACAL;QACAC;QACAC;IACF;IAEA,MAAMe,sBAAsB;QAC1B,GAAGV,UAAU;QACbrB;QACAF,gBAAgBA,iBAAiBkC,KAAKC,SAAS,CAACnC,kBAAkBoC;QAElE,sEAAsE;QACtEC,YAAY,IAAI,CAACC,SAAS;QAC1BC,sBAAsB,IAAI,CAACD,SAAS;QAEpC,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgBtC;IAClB;IAEA,IAAI,CAAC+B,oBAAoBjC,cAAc,EAAE;QACvC,OAAOiC,oBAAoBjC,cAAc;IAC3C;IAEA,+BAA+B;IAC/B,IACE,IAAI,CAACyB,IAAI,IACTQ,oBAAoBQ,GAAG,IACvBR,oBAAoBQ,GAAG,CAAC3D,SAAS,IACjCmD,oBAAoBQ,GAAG,CAAC3D,SAAS,CAAC4D,KAAK,IACvC,CAACC,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CACnCb,oBAAoBQ,GAAG,CAAC3D,SAAS,CAAC4D,KAAK,EACvC,gBAEF;QACAT,oBAAoBQ,GAAG,CAAC3D,SAAS,CAAC4D,KAAK,CAAClB,WAAW,GACjD,IAAI,CAACC,IAAI,KAAK;IAClB;IAEA,MAAMsB,UAAUjD,YAAYkD,UAAU,CAAC;IACvC,OAAOD,QAAQE,YAAY,CAAC,IAC1BnE,UAAUiB,QAAekC,qBAAqBiB,IAAI,CAAC,CAACC;YAClD,IAAIA,OAAOC,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,EAAE;gBACxD,KAAK,MAAMC,OAAOnB,KAAKoB,KAAK,CAACH,OAAOC,kBAAkB,EAAG;oBACvD,IAAI,CAACA,kBAAkB,CAACG,GAAG,CAACF;gBAC9B;YACF;YACA,OAAO;gBAACF,OAAOK,IAAI;gBAAEL,OAAOM,GAAG,GAAGvB,KAAKoB,KAAK,CAACH,OAAOM,GAAG,IAAIrB;aAAU;QACvE;AAEJ;AAEA,MAAMsB,iBACJ;AAEF,OAAO,SAASC;IACd,MAAMC,WAAW,IAAI,CAACC,KAAK;IAC3B,IAAIzD,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAE5D,MAAMC,qBAAqBlB,aACzB,IAAI,CAACe,YAAY,EACjBC,cAAcd,iBAAiB,IAAI,EAAE;IAGrC,CAAA;QACA,IACE,0DAA0D;QAC1D,CAACgB,sBACD,kDAAkD;QAClD,CAACwD,QAAQC,QAAQ,CAACC,GAAG,IACrB,CAACN,eAAejE,IAAI,CAAC,IAAI,CAACU,YAAY,KACtC,IAAI,CAAC8D,OAAO,CAACC,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,IAC5ClF,WAAW,IAAI,CAACkB,YAAY,KAC5B,CAAE,MAAMtB,UACR;YACA,MAAMuF,aAAa,IAAI,CAACC,gBAAgB,CAACrB,UAAU,CAAC;YACpD,IAAI,CAACsB,aAAa,CAAC,IAAI,CAACnE,YAAY;YACpC,OAAOiE,WAAWnB,YAAY,CAAC,IAC7BpD,gBAAgBiD,IAAI,CAAC,IAAI,EAAEsB;QAE/B;IACF,CAAA,IAAKlB,IAAI,CAAC,CAAC1D;QACT,IAAIA,GAAG,OAAOoE,SAAS,SAASpE;QAChCoE;IACF,GAAGA;AACL;AAEA,eAAe,SAASW,UAEtBC,WAAmB,EACnBxE,cAAmB;IAEnB,MAAMoE,aAAa,IAAI,CAACC,gBAAgB,CAACrB,UAAU,CAAC;IACpD,MAAMY,WAAW,IAAI,CAACC,KAAK;IAC3BO,WACGnB,YAAY,CAAC,IACZpD,gBAAgBiD,IAAI,CAAC,IAAI,EAAEsB,YAAYI,aAAaxE,iBAErDkD,IAAI,CACH,CAAC,CAACuB,mBAAmBC,gBAAqB;QACxCd,SAAS,MAAMa,mBAAmBC,mBAAmB1E;IACvD,GACA,CAAC2E;QACCf,SAASe;IACX;AAEN;AAEA,oCAAoC;AACpC,OAAO,MAAMC,MAAM,KAAI"}