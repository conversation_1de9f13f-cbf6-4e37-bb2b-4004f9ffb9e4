"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: function() { return /* binding */ Navigation; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"__barrel_optimize__?names=Menu,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/LanguageToggle */ \"./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTranslation */ \"./src/hooks/useTranslation.tsx\");\n/* harmony import */ var _hooks_useRTL__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useRTL */ \"./src/hooks/useRTL.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Navigation = ()=>{\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const { isRTL } = (0,_hooks_useRTL__WEBPACK_IMPORTED_MODULE_5__.useRTL)();\n    const navItems = [\n        {\n            href: \"#\",\n            label: t(\"about\")\n        },\n        {\n            href: \"#\",\n            label: t(\"skills\")\n        },\n        {\n            href: \"#\",\n            label: t(\"projects\")\n        },\n        {\n            href: \"#\",\n            label: t(\"contact\")\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full bg-white/95 backdrop-blur-sm z-50 border-b border-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: \"Waleed Almshwly\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center \".concat(isRTL ? \"space-x-reverse space-x-6\" : \"space-x-6\"),\n                                    children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: item.href,\n                                            className: \"text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors\",\n                                            children: item.label\n                                        }, item.href, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__.LanguageToggle, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__.LanguageToggle, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: ()=>setIsOpen(!isOpen),\n                                    children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__.X, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 25\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Menu, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 53\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3\",\n                        children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: item.href,\n                                className: \"text-gray-700 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium\",\n                                onClick: ()=>setIsOpen(false),\n                                children: item.label\n                            }, item.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navigation, \"Jad9rf3hp9p9le205gj7UKApF3o=\", false, function() {\n    return [\n        _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        _hooks_useRTL__WEBPACK_IMPORTED_MODULE_5__.useRTL\n    ];\n});\n_c = Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Navigation.tsx\n"));

/***/ })

});