{"version": 3, "sources": ["../../src/server/image-optimizer.ts"], "names": ["createHash", "promises", "cpus", "mediaType", "contentDisposition", "getOrientation", "Orientation", "imageSizeOf", "isAnimated", "join", "nodeUrl", "getImageBlurSvg", "hasLocalMatch", "hasRemoteMatch", "createRequestResponseMocks", "sendEtagResponse", "getContentType", "getExtension", "Log", "parseUrl", "AVIF", "WEBP", "PNG", "JPEG", "GIF", "SVG", "ICO", "CACHE_VERSION", "ANIMATABLE_TYPES", "VECTOR_TYPES", "BLUR_IMG_SIZE", "BLUR_QUALITY", "sharp", "require", "process", "env", "NEXT_SHARP_PATH", "concurrency", "divisor", "NODE_ENV", "Math", "floor", "max", "length", "e", "showSharpMissingWarning", "getSupportedMimeType", "options", "accept", "mimeType", "includes", "getHash", "items", "hash", "item", "update", "String", "digest", "replace", "writeToCacheDir", "dir", "extension", "maxAge", "expireAt", "buffer", "etag", "filename", "rm", "recursive", "force", "catch", "mkdir", "writeFile", "detectContentType", "every", "b", "i", "ImageOptimizerCache", "validateParams", "req", "query", "nextConfig", "isDev", "imageData", "images", "deviceSizes", "imageSizes", "domains", "minimumCacheTTL", "formats", "remotePatterns", "localPatterns", "qualities", "url", "w", "q", "href", "warnOnce", "errorMessage", "Array", "isArray", "startsWith", "isAbsolute", "test", "decodeURIComponent", "pathname", "hrefParsed", "URL", "toString", "_error", "protocol", "width", "parseInt", "isNaN", "sizes", "push", "isValidSize", "quality", "headers", "isStatic", "basePath", "get<PERSON><PERSON><PERSON><PERSON>", "constructor", "distDir", "cacheDir", "get", "cache<PERSON>ey", "files", "readdir", "now", "Date", "file", "maxAgeSt", "expireAtSt", "split", "readFile", "Number", "value", "kind", "revalidateAfter", "curRevalidate", "isStale", "_", "set", "revalidate", "Error", "err", "error", "ImageError", "statusCode", "message", "parseCacheControl", "str", "map", "Map", "directive", "key", "trim", "toLowerCase", "getMaxAge", "age", "endsWith", "slice", "n", "optimizeImage", "contentType", "height", "nextConfigOutput", "optimizedBuffer", "transformer", "sequentialRead", "rotate", "resize", "undefined", "withoutEnlargement", "avif", "avifQuality", "chromaSubsampling", "webp", "png", "jpeg", "progressive", "<PERSON><PERSON><PERSON><PERSON>", "orientation", "operations", "RIGHT_TOP", "type", "numRotations", "BOTTOM_RIGHT", "LEFT_BOTTOM", "processBuffer", "fetchExternalImage", "res", "fetch", "ok", "status", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "cacheControl", "fetchInternalImage", "_req", "_res", "handleRequest", "mocked", "method", "socket", "parse", "hasStreamed", "concat", "buffers", "<PERSON><PERSON><PERSON><PERSON>", "imageOptimizer", "imageUpstream", "paramsResult", "upstreamBuffer", "upstreamType", "dangerouslyAllowSVG", "output", "getMetadata", "meta", "opts", "blur<PERSON>idth", "blurHeight", "blurDataURL", "unescape", "getFileNameWithExtension", "urlWithoutQueryParams", "fileNameWithExtension", "pop", "fileName", "setResponseHeaders", "xCache", "imagesConfig", "<PERSON><PERSON><PERSON><PERSON>", "finished", "contentDispositionType", "contentSecurityPolicy", "sendResponse", "result", "byteLength", "end", "getImageSize", "metadata", "decodeBuffer"], "mappings": "AAAA,SAASA,UAAU,QAAQ,SAAQ;AACnC,SAASC,QAAQ,QAAQ,KAAI;AAC7B,SAASC,IAAI,QAAQ,KAAI;AAEzB,SAASC,SAAS,QAAQ,kCAAiC;AAC3D,OAAOC,wBAAwB,yCAAwC;AACvE,SAASC,cAAc,EAAEC,WAAW,QAAQ,qCAAoC;AAChF,OAAOC,iBAAiB,gCAA+B;AACvD,OAAOC,gBAAgB,iCAAgC;AACvD,SAASC,IAAI,QAAQ,OAAM;AAC3B,OAAOC,aAA0C,MAAK;AAEtD,SAASC,eAAe,QAAQ,+BAA8B;AAE9D,SAASC,aAAa,QAAQ,oCAAmC;AACjE,SAASC,cAAc,QAAQ,qCAAoC;AAEnE,SAASC,0BAA0B,QAAQ,qBAAoB;AAW/D,SAASC,gBAAgB,QAAQ,iBAAgB;AACjD,SAASC,cAAc,EAAEC,YAAY,QAAQ,iBAAgB;AAC7D,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,QAAQ,QAAQ,aAAY;AAIrC,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,gBAAgB;AACtB,MAAMC,mBAAmB;IAACP;IAAMC;IAAKE;CAAI;AACzC,MAAMK,eAAe;IAACJ;CAAI;AAC1B,MAAMK,gBAAgB,EAAE,mCAAmC;;AAC3D,MAAMC,eAAe,GAAG,mCAAmC;;AAE3D,IAAIC;AAEJ,IAAI;IACFA,QAAQC,QAAQC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAC/C,IAAIJ,SAASA,MAAMK,WAAW,KAAK,GAAG;QACpC,2DAA2D;QAC3D,8DAA8D;QAC9D,0DAA0D;QAC1D,MAAMC,UAAUJ,QAAQC,GAAG,CAACI,QAAQ,KAAK,gBAAgB,IAAI;QAC7DP,MAAMK,WAAW,CAACG,KAAKC,KAAK,CAACD,KAAKE,GAAG,CAACxC,OAAOyC,MAAM,GAAGL,SAAS;IACjE;AACF,EAAE,OAAOM,GAAG;AACV,iEAAiE;AACnE;AAEA,IAAIC,0BAA0BX,QAAQC,GAAG,CAACI,QAAQ,KAAK;AAmBvD,SAASO,qBAAqBC,OAAiB,EAAEC,SAAS,EAAE;IAC1D,MAAMC,WAAW9C,UAAU6C,QAAQD;IACnC,OAAOC,OAAOE,QAAQ,CAACD,YAAYA,WAAW;AAChD;AAEA,OAAO,SAASE,QAAQC,KAAmC;IACzD,MAAMC,OAAOrD,WAAW;IACxB,KAAK,IAAIsD,QAAQF,MAAO;QACtB,IAAI,OAAOE,SAAS,UAAUD,KAAKE,MAAM,CAACC,OAAOF;aAC5C;YACHD,KAAKE,MAAM,CAACD;QACd;IACF;IACA,qDAAqD;IACrD,OAAOD,KAAKI,MAAM,CAAC,UAAUC,OAAO,CAAC,OAAO;AAC9C;AAEA,eAAeC,gBACbC,GAAW,EACXC,SAAiB,EACjBC,MAAc,EACdC,QAAgB,EAChBC,MAAc,EACdC,IAAY;IAEZ,MAAMC,WAAWzD,KAAKmD,KAAK,CAAC,EAAEE,OAAO,CAAC,EAAEC,SAAS,CAAC,EAAEE,KAAK,CAAC,EAAEJ,UAAU,CAAC;IAEvE,MAAM5D,SAASkE,EAAE,CAACP,KAAK;QAAEQ,WAAW;QAAMC,OAAO;IAAK,GAAGC,KAAK,CAAC,KAAO;IAEtE,MAAMrE,SAASsE,KAAK,CAACX,KAAK;QAAEQ,WAAW;IAAK;IAC5C,MAAMnE,SAASuE,SAAS,CAACN,UAAUF;AACrC;AAEA;;;;CAIC,GACD,OAAO,SAASS,kBAAkBT,MAAc;IAC9C,IAAI;QAAC;QAAM;QAAM;KAAK,CAACU,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QACvD,OAAOpD;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACmD,KAAK,CACpD,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAE1B;QACA,OAAOrD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACoD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QAC7D,OAAOnD;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;KAAK,CAACkD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKX,MAAM,CAACY,EAAE,KAAKD,IAEhC;QACA,OAAOtD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAACqD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QACnE,OAAOlD;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACiD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKX,MAAM,CAACY,EAAE,KAAKD,IAEhC;QACA,OAAOvD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACsD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QAC7D,OAAOjD;IACT;IACA,OAAO;AACT;AAEA,OAAO,MAAMmD;IAIX,OAAOC,eACLC,GAAoB,EACpBC,KAAkC,EAClCC,UAA8B,EAC9BC,KAAc,EACgC;YASvBD,oBACDA,qBACJA;QAVlB,MAAME,YAAYF,WAAWG,MAAM;QACnC,MAAM,EACJC,cAAc,EAAE,EAChBC,aAAa,EAAE,EACfC,UAAU,EAAE,EACZC,kBAAkB,EAAE,EACpBC,UAAU;YAAC;SAAa,EACzB,GAAGN;QACJ,MAAMO,iBAAiBT,EAAAA,qBAAAA,WAAWG,MAAM,qBAAjBH,mBAAmBS,cAAc,KAAI,EAAE;QAC9D,MAAMC,iBAAgBV,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBU,aAAa;QACtD,MAAMC,aAAYX,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBW,SAAS;QAC9C,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGf;QACtB,IAAIgB;QAEJ,IAAIT,QAAQ5C,MAAM,GAAG,GAAG;YACtBzB,IAAI+E,QAAQ,CACV;QAEJ;QAEA,IAAI,CAACJ,KAAK;YACR,OAAO;gBAAEK,cAAc;YAA8B;QACvD,OAAO,IAAIC,MAAMC,OAAO,CAACP,MAAM;YAC7B,OAAO;gBAAEK,cAAc;YAAqC;QAC9D;QAEA,IAAIL,IAAIlD,MAAM,GAAG,MAAM;YACrB,OAAO;gBAAEuD,cAAc;YAA8B;QACvD;QAEA,IAAIL,IAAIQ,UAAU,CAAC,OAAO;YACxB,OAAO;gBACLH,cAAc;YAChB;QACF;QAEA,IAAII;QAEJ,IAAIT,IAAIQ,UAAU,CAAC,MAAM;gBAKAlF;YAJvB6E,OAAOH;YACPS,aAAa;YACb,IACE,uBAAuBC,IAAI,CACzBC,mBAAmBrF,EAAAA,YAAAA,SAAS0E,yBAAT1E,UAAesF,QAAQ,KAAI,MAEhD;gBACA,OAAO;oBACLP,cAAc;gBAChB;YACF;YACA,IAAI,CAACtF,cAAc+E,eAAeE,MAAM;gBACtC,OAAO;oBAAEK,cAAc;gBAAiC;YAC1D;QACF,OAAO;YACL,IAAIQ;YAEJ,IAAI;gBACFA,aAAa,IAAIC,IAAId;gBACrBG,OAAOU,WAAWE,QAAQ;gBAC1BN,aAAa;YACf,EAAE,OAAOO,QAAQ;gBACf,OAAO;oBAAEX,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAC;gBAAC;gBAAS;aAAS,CAAChD,QAAQ,CAACwD,WAAWI,QAAQ,GAAG;gBACtD,OAAO;oBAAEZ,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAACrF,eAAe0E,SAASG,gBAAgBgB,aAAa;gBACxD,OAAO;oBAAER,cAAc;gBAAiC;YAC1D;QACF;QAEA,IAAI,CAACJ,GAAG;YACN,OAAO;gBAAEI,cAAc;YAAoC;QAC7D,OAAO,IAAIC,MAAMC,OAAO,CAACN,IAAI;YAC3B,OAAO;gBAAEI,cAAc;YAA2C;QACpE;QAEA,IAAI,CAACH,GAAG;YACN,OAAO;gBAAEG,cAAc;YAAsC;QAC/D,OAAO,IAAIC,MAAMC,OAAO,CAACL,IAAI;YAC3B,OAAO;gBAAEG,cAAc;YAA6C;QACtE;QAEA,MAAMa,QAAQC,SAASlB,GAAG;QAE1B,IAAIiB,SAAS,KAAKE,MAAMF,QAAQ;YAC9B,OAAO;gBACLb,cAAc;YAChB;QACF;QAEA,MAAMgB,QAAQ;eAAK7B,eAAe,EAAE;eAAOC,cAAc,EAAE;SAAE;QAE7D,IAAIJ,OAAO;YACTgC,MAAMC,IAAI,CAACrF;QACb;QAEA,MAAMsF,cACJF,MAAMhE,QAAQ,CAAC6D,UAAW7B,SAAS6B,SAASjF;QAE9C,IAAI,CAACsF,aAAa;YAChB,OAAO;gBACLlB,cAAc,CAAC,yBAAyB,EAAEa,MAAM,eAAe,CAAC;YAClE;QACF;QAEA,MAAMM,UAAUL,SAASjB;QAEzB,IAAIkB,MAAMI,YAAYA,UAAU,KAAKA,UAAU,KAAK;YAClD,OAAO;gBACLnB,cACE;YACJ;QACF;QAEA,IAAIN,WAAW;YACb,IAAIV,OAAO;gBACTU,UAAUuB,IAAI,CAACpF;YACjB;YAEA,IAAI,CAAC6D,UAAU1C,QAAQ,CAACmE,UAAU;gBAChC,OAAO;oBACLnB,cAAc,CAAC,2BAA2B,EAAEH,EAAE,eAAe,CAAC;gBAChE;YACF;QACF;QAEA,MAAM9C,WAAWH,qBAAqB2C,WAAW,EAAE,EAAEV,IAAIuC,OAAO,CAAC,SAAS;QAE1E,MAAMC,WAAW1B,IAAIQ,UAAU,CAC7B,CAAC,EAAEpB,WAAWuC,QAAQ,IAAI,GAAG,mBAAmB,CAAC;QAGnD,OAAO;YACLxB;YACAkB;YACAZ;YACAiB;YACAR;YACAM;YACApE;YACAuC;QACF;IACF;IAEA,OAAOiC,YAAY,EACjBzB,IAAI,EACJe,KAAK,EACLM,OAAO,EACPpE,QAAQ,EAMT,EAAU;QACT,OAAOE,QAAQ;YAACxB;YAAeqE;YAAMe;YAAOM;YAASpE;SAAS;IAChE;IAEAyE,YAAY,EACVC,OAAO,EACP1C,UAAU,EAIX,CAAE;QACD,IAAI,CAAC2C,QAAQ,GAAGnH,KAAKkH,SAAS,SAAS;QACvC,IAAI,CAAC1C,UAAU,GAAGA;IACpB;IAEA,MAAM4C,IAAIC,QAAgB,EAAyC;QACjE,IAAI;YACF,MAAMF,WAAWnH,KAAK,IAAI,CAACmH,QAAQ,EAAEE;YACrC,MAAMC,QAAQ,MAAM9H,SAAS+H,OAAO,CAACJ;YACrC,MAAMK,MAAMC,KAAKD,GAAG;YAEpB,KAAK,MAAME,QAAQJ,MAAO;gBACxB,MAAM,CAACK,UAAUC,YAAYpE,MAAMJ,UAAU,GAAGsE,KAAKG,KAAK,CAAC,KAAK;gBAChE,MAAMtE,SAAS,MAAM/D,SAASsI,QAAQ,CAAC9H,KAAKmH,UAAUO;gBACtD,MAAMpE,WAAWyE,OAAOH;gBACxB,MAAMvE,SAAS0E,OAAOJ;gBAEtB,OAAO;oBACLK,OAAO;wBACLC,MAAM;wBACNzE;wBACAD;wBACAH;oBACF;oBACA8E,iBACEnG,KAAKE,GAAG,CAACoB,QAAQ,IAAI,CAACmB,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC3D0C,KAAKD,GAAG;oBACVW,eAAe9E;oBACf+E,SAASZ,MAAMlE;gBACjB;YACF;QACF,EAAE,OAAO+E,GAAG;QACV,qDAAqD;QACvD;QACA,OAAO;IACT;IACA,MAAMC,IACJjB,QAAgB,EAChBW,KAAmC,EACnC,EACEO,UAAU,EAGX,EACD;QACA,IAAIP,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,SAAS;YAC3B,MAAM,IAAIO,MAAM;QAClB;QAEA,IAAI,OAAOD,eAAe,UAAU;YAClC,MAAM,IAAIC,MAAM;QAClB;QACA,MAAMlF,WACJvB,KAAKE,GAAG,CAACsG,YAAY,IAAI,CAAC/D,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC/D0C,KAAKD,GAAG;QAEV,IAAI;YACF,MAAMtE,gBACJlD,KAAK,IAAI,CAACmH,QAAQ,EAAEE,WACpBW,MAAM5E,SAAS,EACfmF,YACAjF,UACA0E,MAAMzE,MAAM,EACZyE,MAAMxE,IAAI;QAEd,EAAE,OAAOiF,KAAK;YACZhI,IAAIiI,KAAK,CAAC,CAAC,+BAA+B,EAAErB,SAAS,CAAC,EAAEoB;QAC1D;IACF;AACF;AACA,OAAO,MAAME,mBAAmBH;IAG9BvB,YAAY2B,UAAkB,EAAEC,OAAe,CAAE;QAC/C,KAAK,CAACA;QAEN,uCAAuC;QACvC,IAAID,cAAc,KAAK;YACrB,IAAI,CAACA,UAAU,GAAGA;QACpB,OAAO;YACL,IAAI,CAACA,UAAU,GAAG;QACpB;IACF;AACF;AAEA,SAASE,kBACPC,GAA8B;IAE9B,MAAMC,MAAM,IAAIC;IAChB,IAAI,CAACF,KAAK;QACR,OAAOC;IACT;IACA,KAAK,IAAIE,aAAaH,IAAIlB,KAAK,CAAC,KAAM;QACpC,IAAI,CAACsB,KAAKnB,MAAM,GAAGkB,UAAUE,IAAI,GAAGvB,KAAK,CAAC,KAAK;QAC/CsB,MAAMA,IAAIE,WAAW;QACrB,IAAIrB,OAAO;YACTA,QAAQA,MAAMqB,WAAW;QAC3B;QACAL,IAAIV,GAAG,CAACa,KAAKnB;IACf;IACA,OAAOgB;AACT;AAEA,OAAO,SAASM,UAAUP,GAA8B;IACtD,MAAMC,MAAMF,kBAAkBC;IAC9B,IAAIC,KAAK;QACP,IAAIO,MAAMP,IAAI5B,GAAG,CAAC,eAAe4B,IAAI5B,GAAG,CAAC,cAAc;QACvD,IAAImC,IAAI3D,UAAU,CAAC,QAAQ2D,IAAIC,QAAQ,CAAC,MAAM;YAC5CD,MAAMA,IAAIE,KAAK,CAAC,GAAG,CAAC;QACtB;QACA,MAAMC,IAAInD,SAASgD,KAAK;QACxB,IAAI,CAAC/C,MAAMkD,IAAI;YACb,OAAOA;QACT;IACF;IACA,OAAO;AACT;AAEA,OAAO,eAAeC,cAAc,EAClCpG,MAAM,EACNqG,WAAW,EACXhD,OAAO,EACPN,KAAK,EACLuD,MAAM,EACNC,gBAAgB,EAQjB;IACC,IAAIC,kBAAkBxG;IACtB,IAAIhC,OAAO;QACT,mCAAmC;QACnC,MAAMyI,cAAczI,MAAMgC,QAAQ;YAChC0G,gBAAgB;QAClB;QAEAD,YAAYE,MAAM;QAElB,IAAIL,QAAQ;YACVG,YAAYG,MAAM,CAAC7D,OAAOuD;QAC5B,OAAO;YACLG,YAAYG,MAAM,CAAC7D,OAAO8D,WAAW;gBACnCC,oBAAoB;YACtB;QACF;QAEA,IAAIT,gBAAgBjJ,MAAM;YACxB,IAAIqJ,YAAYM,IAAI,EAAE;gBACpB,MAAMC,cAAc3D,UAAU;gBAC9BoD,YAAYM,IAAI,CAAC;oBACf1D,SAAS7E,KAAKE,GAAG,CAACsI,aAAa;oBAC/BC,mBAAmB;gBACrB;YACF,OAAO;gBACL/J,IAAI+E,QAAQ,CACV,CAAC,wIAAwI,CAAC,GACxI;gBAEJwE,YAAYS,IAAI,CAAC;oBAAE7D;gBAAQ;YAC7B;QACF,OAAO,IAAIgD,gBAAgBhJ,MAAM;YAC/BoJ,YAAYS,IAAI,CAAC;gBAAE7D;YAAQ;QAC7B,OAAO,IAAIgD,gBAAgB/I,KAAK;YAC9BmJ,YAAYU,GAAG,CAAC;gBAAE9D;YAAQ;QAC5B,OAAO,IAAIgD,gBAAgB9I,MAAM;YAC/BkJ,YAAYW,IAAI,CAAC;gBAAE/D;gBAASgE,aAAa;YAAK;QAChD;QAEAb,kBAAkB,MAAMC,YAAYa,QAAQ;IAC5C,iCAAiC;IACnC,OAAO;QACL,IAAIzI,2BAA2B0H,qBAAqB,cAAc;YAChErJ,IAAIiI,KAAK,CACP,CAAC,0LAA0L,CAAC;YAE9L,MAAM,IAAIC,WAAW,KAAK;QAC5B;QACA,wCAAwC;QACxC,IAAIvG,yBAAyB;YAC3B3B,IAAI+E,QAAQ,CACV,CAAC,wLAAwL,CAAC,GACxL;YAEJpD,0BAA0B;QAC5B;QAEA,qCAAqC;QACrC,MAAM0I,cAAc,MAAMlL,eAAe2D;QAEzC,MAAMwH,aAA0B,EAAE;QAElC,IAAID,gBAAgBjL,YAAYmL,SAAS,EAAE;YACzCD,WAAWrE,IAAI,CAAC;gBAAEuE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIJ,gBAAgBjL,YAAYsL,YAAY,EAAE;YACnDJ,WAAWrE,IAAI,CAAC;gBAAEuE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIJ,gBAAgBjL,YAAYuL,WAAW,EAAE;YAClDL,WAAWrE,IAAI,CAAC;gBAAEuE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO;QACL,kCAAkC;QAClC,6DAA6D;QAC7D,+BAA+B;QACjC;QAEA,IAAIrB,QAAQ;YACVkB,WAAWrE,IAAI,CAAC;gBAAEuE,MAAM;gBAAU3E;gBAAOuD;YAAO;QAClD,OAAO;YACLkB,WAAWrE,IAAI,CAAC;gBAAEuE,MAAM;gBAAU3E;YAAM;QAC1C;QAEA,MAAM,EAAE+E,aAAa,EAAE,GACrB7J,QAAQ;QAEV,IAAIoI,gBAAgBjJ,MAAM;YACxBoJ,kBAAkB,MAAMsB,cAAc9H,QAAQwH,YAAY,QAAQnE;QACpE,OAAO,IAAIgD,gBAAgBhJ,MAAM;YAC/BmJ,kBAAkB,MAAMsB,cAAc9H,QAAQwH,YAAY,QAAQnE;QACpE,OAAO,IAAIgD,gBAAgB/I,KAAK;YAC9BkJ,kBAAkB,MAAMsB,cAAc9H,QAAQwH,YAAY,OAAOnE;QACnE,OAAO,IAAIgD,gBAAgB9I,MAAM;YAC/BiJ,kBAAkB,MAAMsB,cAAc9H,QAAQwH,YAAY,QAAQnE;QACpE;IACF;IAEA,OAAOmD;AACT;AAEA,OAAO,eAAeuB,mBAAmB/F,IAAY;IACnD,MAAMgG,MAAM,MAAMC,MAAMjG;IAExB,IAAI,CAACgG,IAAIE,EAAE,EAAE;QACXhL,IAAIiI,KAAK,CAAC,sCAAsCnD,MAAMgG,IAAIG,MAAM;QAChE,MAAM,IAAI/C,WACR4C,IAAIG,MAAM,EACV;IAEJ;IAEA,MAAMnI,SAASoI,OAAOC,IAAI,CAAC,MAAML,IAAIM,WAAW;IAChD,MAAMjC,cAAc2B,IAAI1E,OAAO,CAACO,GAAG,CAAC;IACpC,MAAM0E,eAAeP,IAAI1E,OAAO,CAACO,GAAG,CAAC;IAErC,OAAO;QAAE7D;QAAQqG;QAAakC;IAAa;AAC7C;AAEA,OAAO,eAAeC,mBACpBxG,IAAY,EACZyG,IAAqB,EACrBC,IAAoB,EACpBC,aAIkB;IAElB,IAAI;QACF,MAAMC,SAAS9L,2BAA2B;YACxC+E,KAAKG;YACL6G,QAAQJ,KAAKI,MAAM,IAAI;YACvBvF,SAASmF,KAAKnF,OAAO;YACrBwF,QAAQL,KAAKK,MAAM;QACrB;QAEA,MAAMH,cAAcC,OAAO7H,GAAG,EAAE6H,OAAOZ,GAAG,EAAEtL,QAAQqM,KAAK,CAAC/G,MAAM;QAChE,MAAM4G,OAAOZ,GAAG,CAACgB,WAAW;QAE5B,IAAI,CAACJ,OAAOZ,GAAG,CAAC3C,UAAU,EAAE;YAC1BnI,IAAIiI,KAAK,CAAC,6BAA6BnD,MAAM4G,OAAOZ,GAAG,CAAC3C,UAAU;YAClE,MAAM,IAAID,WACRwD,OAAOZ,GAAG,CAAC3C,UAAU,EACrB;QAEJ;QAEA,MAAMrF,SAASoI,OAAOa,MAAM,CAACL,OAAOZ,GAAG,CAACkB,OAAO;QAC/C,MAAM7C,cAAcuC,OAAOZ,GAAG,CAACmB,SAAS,CAAC;QACzC,MAAMZ,eAAeK,OAAOZ,GAAG,CAACmB,SAAS,CAAC;QAC1C,OAAO;YAAEnJ;YAAQqG;YAAakC;QAAa;IAC7C,EAAE,OAAOrD,KAAK;QACZhI,IAAIiI,KAAK,CAAC,sCAAsCnD,MAAMkD;QACtD,MAAM,IAAIE,WACR,KACA;IAEJ;AACF;AAEA,OAAO,eAAegE,eACpBC,aAA4B,EAC5BC,YAGC,EACDrI,UAMC,EACDC,KAA0B;QAOxBmI;IALF,MAAM,EAAErH,IAAI,EAAEqB,OAAO,EAAEN,KAAK,EAAE9D,QAAQ,EAAE,GAAGqK;IAC3C,MAAMC,iBAAiBF,cAAcrJ,MAAM;IAC3C,MAAMF,SAASiG,UAAUsD,cAAcd,YAAY;IACnD,MAAMiB,eACJ/I,kBAAkB8I,qBAClBF,6BAAAA,cAAchD,WAAW,qBAAzBgD,2BAA2BvD,WAAW,GAAGD,IAAI;IAE/C,IAAI2D,cAAc;QAChB,IACEA,aAAanH,UAAU,CAAC,gBACxB,CAACpB,WAAWG,MAAM,CAACqI,mBAAmB,EACtC;YACAvM,IAAIiI,KAAK,CACP,CAAC,wBAAwB,EAAEnD,KAAK,YAAY,EAAEwH,aAAa,qCAAqC,CAAC;YAEnG,MAAM,IAAIpE,WACR,KACA;QAEJ;QAEA,IAAIxH,iBAAiBsB,QAAQ,CAACsK,iBAAiBhN,WAAW+M,iBAAiB;YACzErM,IAAI+E,QAAQ,CACV,CAAC,wBAAwB,EAAED,KAAK,8GAA8G,CAAC;YAEjJ,OAAO;gBAAEhC,QAAQuJ;gBAAgBlD,aAAamD;gBAAc1J;YAAO;QACrE;QACA,IAAIjC,aAAaqB,QAAQ,CAACsK,eAAe;YACvC,wEAAwE;YACxE,6DAA6D;YAC7D,4EAA4E;YAC5E,OAAO;gBAAExJ,QAAQuJ;gBAAgBlD,aAAamD;gBAAc1J;YAAO;QACrE;QACA,IAAI,CAAC0J,aAAanH,UAAU,CAAC,aAAamH,aAAatK,QAAQ,CAAC,MAAM;YACpEhC,IAAIiI,KAAK,CACP,kDACAnD,MACA,YACAwH;YAEF,MAAM,IAAIpE,WAAW,KAAK;QAC5B;IACF;IAEA,IAAIiB;IAEJ,IAAIpH,UAAU;QACZoH,cAAcpH;IAChB,OAAO,IACLuK,CAAAA,gCAAAA,aAAcnH,UAAU,CAAC,cACzBpF,aAAauM,iBACbA,iBAAiBnM,QACjBmM,iBAAiBpM,MACjB;QACAiJ,cAAcmD;IAChB,OAAO;QACLnD,cAAc9I;IAChB;IACA,IAAI;QACF,IAAIiJ,kBAAkB,MAAMJ,cAAc;YACxCpG,QAAQuJ;YACRlD;YACAhD;YACAN;YACAwD,kBAAkBtF,WAAWyI,MAAM;QACrC;QACA,IAAIlD,iBAAiB;YACnB,IAAItF,SAAS6B,SAASjF,iBAAiBuF,YAAYtF,cAAc;gBAC/D,MAAM,EAAE4L,WAAW,EAAE,GACnB1L,QAAQ;gBACV,8EAA8E;gBAC9E,gFAAgF;gBAChF,qFAAqF;gBACrF,MAAM2L,OAAO,MAAMD,YAAYnD;gBAC/B,MAAMqD,OAAO;oBACXC,WAAWF,KAAK7G,KAAK;oBACrBgH,YAAYH,KAAKtD,MAAM;oBACvB0D,aAAa,CAAC,KAAK,EAAE3D,YAAY,QAAQ,EAAEG,gBAAgB5D,QAAQ,CACjE,UACA,CAAC;gBACL;gBACA4D,kBAAkB4B,OAAOC,IAAI,CAAC4B,SAAStN,gBAAgBkN;gBACvDxD,cAAc;YAChB;YACA,OAAO;gBACLrG,QAAQwG;gBACRH;gBACAvG,QAAQtB,KAAKE,GAAG,CAACoB,QAAQmB,WAAWG,MAAM,CAACI,eAAe;YAC5D;QACF,OAAO;YACL,MAAM,IAAI4D,WAAW,KAAK;QAC5B;IACF,EAAE,OAAOD,OAAO;QACd,IAAIoE,kBAAkBC,cAAc;YAClC,yDAAyD;YACzD,OAAO;gBACLxJ,QAAQuJ;gBACRlD,aAAamD;gBACb1J,QAAQmB,WAAWG,MAAM,CAACI,eAAe;YAC3C;QACF,OAAO;YACL,MAAM,IAAI4D,WACR,KACA;QAEJ;IACF;AACF;AAEA,SAAS8E,yBACPrI,GAAW,EACXwE,WAA0B;IAE1B,MAAM,CAAC8D,sBAAsB,GAAGtI,IAAIyC,KAAK,CAAC,KAAK;IAC/C,MAAM8F,wBAAwBD,sBAAsB7F,KAAK,CAAC,KAAK+F,GAAG;IAClE,IAAI,CAAChE,eAAe,CAAC+D,uBAAuB;QAC1C,OAAO;IACT;IAEA,MAAM,CAACE,SAAS,GAAGF,sBAAsB9F,KAAK,CAAC,KAAK;IACpD,MAAMzE,YAAY5C,aAAaoJ;IAC/B,OAAO,CAAC,EAAEiE,SAAS,CAAC,EAAEzK,UAAU,CAAC;AACnC;AAEA,SAAS0K,mBACPxJ,GAAoB,EACpBiH,GAAmB,EACnBnG,GAAW,EACX5B,IAAY,EACZoG,WAA0B,EAC1B9C,QAAiB,EACjBiH,MAAoB,EACpBC,YAAiC,EACjC3K,MAAc,EACdoB,KAAc;IAEd8G,IAAI0C,SAAS,CAAC,QAAQ;IACtB1C,IAAI0C,SAAS,CACX,iBACAnH,WACI,yCACA,CAAC,gBAAgB,EAAErC,QAAQ,IAAIpB,OAAO,iBAAiB,CAAC;IAE9D,IAAI/C,iBAAiBgE,KAAKiH,KAAK/H,OAAO;QACpC,6CAA6C;QAC7C,OAAO;YAAE0K,UAAU;QAAK;IAC1B;IACA,IAAItE,aAAa;QACf2B,IAAI0C,SAAS,CAAC,gBAAgBrE;IAChC;IAEA,MAAMiE,WAAWJ,yBAAyBrI,KAAKwE;IAC/C2B,IAAI0C,SAAS,CACX,uBACAtO,mBAAmBkO,UAAU;QAAE5C,MAAM+C,aAAaG,sBAAsB;IAAC;IAG3E5C,IAAI0C,SAAS,CAAC,2BAA2BD,aAAaI,qBAAqB;IAC3E7C,IAAI0C,SAAS,CAAC,kBAAkBF;IAEhC,OAAO;QAAEG,UAAU;IAAM;AAC3B;AAEA,OAAO,SAASG,aACd/J,GAAoB,EACpBiH,GAAmB,EACnBnG,GAAW,EACXhC,SAAiB,EACjBG,MAAc,EACduD,QAAiB,EACjBiH,MAAoB,EACpBC,YAAiC,EACjC3K,MAAc,EACdoB,KAAc;IAEd,MAAMmF,cAAcrJ,eAAe6C;IACnC,MAAMI,OAAOd,QAAQ;QAACa;KAAO;IAC7B,MAAM+K,SAASR,mBACbxJ,KACAiH,KACAnG,KACA5B,MACAoG,aACA9C,UACAiH,QACAC,cACA3K,QACAoB;IAEF,IAAI,CAAC6J,OAAOJ,QAAQ,EAAE;QACpB3C,IAAI0C,SAAS,CAAC,kBAAkBtC,OAAO4C,UAAU,CAAChL;QAClDgI,IAAIiD,GAAG,CAACjL;IACV;AACF;AAEA,OAAO,eAAekL,aACpBlL,MAAc,EACd,8BAA8B;AAC9BH,SAA2C;IAK3C,qDAAqD;IACrD,0DAA0D;IAC1D,IAAIA,cAAc,QAAQ;QACxB,IAAI7B,OAAO;YACT,MAAMyI,cAAczI,MAAMgC;YAC1B,MAAM,EAAE+C,KAAK,EAAEuD,MAAM,EAAE,GAAG,MAAMG,YAAY0E,QAAQ;YACpD,OAAO;gBAAEpI;gBAAOuD;YAAO;QACzB,OAAO;YACL,MAAM,EAAE8E,YAAY,EAAE,GACpBnN,QAAQ;YACV,MAAM,EAAE8E,KAAK,EAAEuD,MAAM,EAAE,GAAG,MAAM8E,aAAapL;YAC7C,OAAO;gBAAE+C;gBAAOuD;YAAO;QACzB;IACF;IAEA,MAAM,EAAEvD,KAAK,EAAEuD,MAAM,EAAE,GAAG/J,YAAYyD;IACtC,OAAO;QAAE+C;QAAOuD;IAAO;AACzB"}