{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/next-fixture.ts"], "names": ["applyNextFixture", "NextFixtureImpl", "constructor", "testInfo", "options", "worker", "page", "fetchHandlers", "testId", "testHeaders", "String", "proxyPort", "handleFetch", "bind", "onFetch", "context", "route", "handleRoute", "teardown", "cleanupTest", "handler", "push", "request", "reportFetch", "req", "slice", "reverse", "result", "clone", "fetch<PERSON><PERSON><PERSON>", "fetch", "undefined", "use", "nextOptions", "nextWorker", "fixture"], "mappings": ";;;;+BA2DsBA;;;eAAAA;;;2BAvDM;wBACA;AAM5B,MAAMC;IAIJC,YACE,AAAQC,QAAkB,EAC1B,AAAQC,OAAoB,EAC5B,AAAQC,MAAyB,EACjC,AAAQC,IAAU,CAClB;aAJQH,WAAAA;aACAC,UAAAA;aACAC,SAAAA;aACAC,OAAAA;aANFC,gBAAgC,EAAE;QAQxC,IAAI,CAACC,MAAM,GAAGL,SAASK,MAAM;QAC7B,MAAMC,cAAc;YAClB,wBAAwBC,OAAOL,OAAOM,SAAS;YAC/C,kBAAkB,IAAI,CAACH,MAAM;QAC/B;QACA,MAAMI,cAAc,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,IAAI;QAC9CR,OAAOS,OAAO,CAAC,IAAI,CAACN,MAAM,EAAEI;QAC5B,IAAI,CAACN,IAAI,CACNS,OAAO,GACPC,KAAK,CAAC,MAAM,CAACA,QACZC,IAAAA,sBAAW,EAACD,OAAOV,MAAMG,aAAaG;IAE5C;IAEAM,WAAiB;QACf,IAAI,CAACb,MAAM,CAACc,WAAW,CAAC,IAAI,CAACX,MAAM;IACrC;IAEAM,QAAQM,OAAqB,EAAQ;QACnC,IAAI,CAACb,aAAa,CAACc,IAAI,CAACD;IAC1B;IAEA,MAAcR,YAAYU,OAAgB,EAA+B;QACvE,OAAOC,IAAAA,mBAAW,EAAC,IAAI,CAACpB,QAAQ,EAAEmB,SAAS,OAAOE;YAChD,KAAK,MAAMJ,WAAW,IAAI,CAACb,aAAa,CAACkB,KAAK,GAAGC,OAAO,GAAI;gBAC1D,MAAMC,SAAS,MAAMP,QAAQI,IAAII,KAAK;gBACtC,IAAID,QAAQ;oBACV,OAAOA;gBACT;YACF;YACA,IAAI,IAAI,CAACvB,OAAO,CAACyB,aAAa,EAAE;gBAC9B,OAAOC,MAAMN,IAAII,KAAK;YACxB;YACA,OAAOG;QACT;IACF;AACF;AAEO,eAAe/B,iBACpBgC,GAA4C,EAC5C,EACE7B,QAAQ,EACR8B,WAAW,EACXC,UAAU,EACV5B,IAAI,EAML;IAED,MAAM6B,UAAU,IAAIlC,gBAAgBE,UAAU8B,aAAaC,YAAY5B;IAEvE,MAAM0B,IAAIG;IAEVA,QAAQjB,QAAQ;AAClB"}