{"version": 3, "sources": ["../../../src/build/templates/middleware.ts"], "names": ["adapter", "_mod", "mod", "handler", "middleware", "default", "page", "Error", "nH<PERSON><PERSON>", "opts"], "mappings": "AAEA,OAAO,2BAA0B;AAEjC,SAASA,OAAO,QAAQ,2BAA0B;AAElD,4BAA4B;AAC5B,YAAYC,UAAU,eAAc;AAEpC,MAAMC,MAAM;IAAE,GAAGD,IAAI;AAAC;AACtB,MAAME,UAAUD,IAAIE,UAAU,IAAIF,IAAIG,OAAO;AAE7C,MAAMC,OAAO;AAEb,IAAI,OAAOH,YAAY,YAAY;IACjC,MAAM,IAAII,MACR,CAAC,gBAAgB,EAAED,KAAK,wDAAwD,CAAC;AAErF;AAEA,eAAe,SAASE,SACtBC,IAAmE;IAEnE,OAAOT,QAAQ;QACb,GAAGS,IAAI;QACPH;QACAH;IACF;AACF"}