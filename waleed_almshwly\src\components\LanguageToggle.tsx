
import { Button } from "@/components/ui/button";
import { useTranslation } from "@/hooks/useTranslation";
import { Globe } from "lucide-react";

export const LanguageToggle = () => {
  const { language, setLanguage } = useTranslation();

  return (
    <div className="flex items-center gap-1 bg-white/80 backdrop-blur-sm rounded-full p-1 shadow-lg">
      <Globe className="w-4 h-4 text-gray-500 mx-2" />
      <Button
        variant={language === 'en' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => setLanguage('en')}
        className={`text-sm rounded-full px-3 py-1 transition-all duration-300 ${
          language === 'en'
            ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-md'
            : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
        }`}
      >
        EN
      </Button>
      <Button
        variant={language === 'ar' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => setLanguage('ar')}
        className={`text-sm rounded-full px-3 py-1 transition-all duration-300 ${
          language === 'ar'
            ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-md'
            : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
        }`}
      >
        عربي
      </Button>
    </div>
  );
};
