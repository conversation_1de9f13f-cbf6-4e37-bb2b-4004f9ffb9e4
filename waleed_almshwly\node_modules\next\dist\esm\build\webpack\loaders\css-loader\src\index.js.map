{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/css-loader/src/index.ts"], "names": ["CssSyntaxError", "Warning", "stringifyRequest", "moduleRegExp", "getModulesOptions", "rawOptions", "loaderContext", "resourcePath", "modules", "isModules", "test", "modulesOptions", "compileType", "icss", "auto", "mode", "exportGlobals", "localIdentName", "localIdentContext", "rootContext", "localIdentHashPrefix", "localIdentRegExp", "undefined", "namedExport", "exportLocalsConvention", "exportOnlyLocals", "RegExp", "isModule", "esModule", "Error", "normalizeOptions", "emitWarning", "url", "import", "sourceMap", "importLoaders", "parseInt", "fontLoader", "loader", "content", "map", "meta", "getOptions", "plugins", "callback", "async", "loaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "options", "error", "postcss", "shouldUseModulesPlugins", "shouldUseImportPlugin", "shouldUseURLPlugin", "shouldUseIcssPlugin", "getPreRequester", "getExportCode", "getFilter", "getImportCode", "getModuleCode", "getModulesPlugins", "normalizeSourceMap", "sort", "require", "icss<PERSON><PERSON><PERSON>", "importParser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "replacements", "exports", "push", "importPluginImports", "importPluginApi", "resolver", "getResolve", "conditionNames", "extensions", "mainFields", "mainFiles", "restrictions", "imports", "api", "context", "filter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "urlPluginImports", "urlResolver", "icssPluginImports", "icssPluginApi", "icssResolver", "ast", "type", "root", "setAttribute", "result", "process", "from", "to", "prev", "inline", "annotation", "file", "addDependency", "name", "warning", "warnings", "unshift", "importName", "resolve", "importCode", "moduleCode", "exportCode", "then", "code", "err"], "mappings": "AAAA;;;AAGA,GACA,OAAOA,oBAAoB,mBAAkB;AAC7C,OAAOC,aAAa,mCAAkC;AACtD,SAASC,gBAAgB,QAAQ,6BAA4B;AAE7D,MAAMC,eAAe;AAErB,SAASC,kBAAkBC,UAAe,EAAEC,aAAkB;IAC5D,MAAM,EAAEC,YAAY,EAAE,GAAGD;IAEzB,IAAI,OAAOD,WAAWG,OAAO,KAAK,aAAa;QAC7C,MAAMC,YAAYN,aAAaO,IAAI,CAACH;QAEpC,IAAI,CAACE,WAAW;YACd,OAAO;QACT;IACF,OAAO,IACL,OAAOJ,WAAWG,OAAO,KAAK,aAC9BH,WAAWG,OAAO,KAAK,OACvB;QACA,OAAO;IACT;IAEA,IAAIG,iBAAsB;QACxBC,aAAaP,WAAWQ,IAAI,GAAG,SAAS;QACxCC,MAAM;QACNC,MAAM;QACNC,eAAe;QACfC,gBAAgB;QAChBC,mBAAmBZ,cAAca,WAAW;QAC5CC,sBAAsB;QACtB,wCAAwC;QACxCC,kBAAkBC;QAClBC,aAAa;QACbC,wBAAwB;QACxBC,kBAAkB;IACpB;IAEA,IACE,OAAOpB,WAAWG,OAAO,KAAK,aAC9B,OAAOH,WAAWG,OAAO,KAAK,UAC9B;QACAG,eAAeI,IAAI,GACjB,OAAOV,WAAWG,OAAO,KAAK,WAAWH,WAAWG,OAAO,GAAG;IAClE,OAAO;QACL,IAAIH,WAAWG,OAAO,EAAE;YACtB,IAAI,OAAOH,WAAWG,OAAO,CAACM,IAAI,KAAK,WAAW;gBAChD,MAAML,YACJJ,WAAWG,OAAO,CAACM,IAAI,IAAIX,aAAaO,IAAI,CAACH;gBAE/C,IAAI,CAACE,WAAW;oBACd,OAAO;gBACT;YACF,OAAO,IAAIJ,WAAWG,OAAO,CAACM,IAAI,YAAYY,QAAQ;gBACpD,MAAMjB,YAAYJ,WAAWG,OAAO,CAACM,IAAI,CAACJ,IAAI,CAACH;gBAE/C,IAAI,CAACE,WAAW;oBACd,OAAO;gBACT;YACF,OAAO,IAAI,OAAOJ,WAAWG,OAAO,CAACM,IAAI,KAAK,YAAY;gBACxD,MAAMa,WAAWtB,WAAWG,OAAO,CAACM,IAAI,CAACP;gBAEzC,IAAI,CAACoB,UAAU;oBACb,OAAO;gBACT;YACF;YAEA,IACEtB,WAAWG,OAAO,CAACe,WAAW,KAAK,QACnC,OAAOlB,WAAWG,OAAO,CAACgB,sBAAsB,KAAK,aACrD;gBACAb,eAAea,sBAAsB,GAAG;YAC1C;QACF;QAEAb,iBAAiB;YAAE,GAAGA,cAAc;YAAE,GAAIN,WAAWG,OAAO,IAAI,CAAC,CAAC;QAAE;IACtE;IAEA,IAAI,OAAOG,eAAeI,IAAI,KAAK,YAAY;QAC7CJ,eAAeI,IAAI,GAAGJ,eAAeI,IAAI,CAACT,cAAcC,YAAY;IACtE;IAEA,IAAII,eAAeY,WAAW,KAAK,MAAM;QACvC,IAAIlB,WAAWuB,QAAQ,KAAK,OAAO;YACjC,MAAM,IAAIC,MACR;QAEJ;QAEA,IAAIlB,eAAea,sBAAsB,KAAK,iBAAiB;YAC7D,MAAM,IAAIK,MACR;QAEJ;IACF;IAEA,OAAOlB;AACT;AAEA,SAASmB,iBAAiBzB,UAAe,EAAEC,aAAkB;IAC3D,IAAID,WAAWQ,IAAI,EAAE;QACnBP,cAAcyB,WAAW,CACvB,IAAIF,MACF;IAGN;IAEA,MAAMlB,iBAAiBP,kBAAkBC,YAAYC;IAErD,OAAO;QACL0B,KAAK,OAAO3B,WAAW2B,GAAG,KAAK,cAAc,OAAO3B,WAAW2B,GAAG;QAClEC,QAAQ,OAAO5B,WAAW4B,MAAM,KAAK,cAAc,OAAO5B,WAAW4B,MAAM;QAC3EzB,SAASG;QACT,wCAAwC;QACxCE,MAAM,OAAOR,WAAWQ,IAAI,KAAK,cAAc,QAAQR,WAAWQ,IAAI;QACtEqB,WACE,OAAO7B,WAAW6B,SAAS,KAAK,YAC5B7B,WAAW6B,SAAS,GACpB5B,cAAc4B,SAAS;QAC7BC,eACE,OAAO9B,WAAW8B,aAAa,KAAK,WAChCC,SAAS/B,WAAW8B,aAAa,EAAE,MACnC9B,WAAW8B,aAAa;QAC9BP,UACE,OAAOvB,WAAWuB,QAAQ,KAAK,cAAc,OAAOvB,WAAWuB,QAAQ;QACzES,YAAYhC,WAAWgC,UAAU;IACnC;AACF;AAEA,eAAe,eAAeC,OAE5BC,OAAe,EACfC,GAAQ,EACRC,IAAS;IAET,MAAMpC,aAAa,IAAI,CAACqC,UAAU;IAElC,MAAMC,UAAiB,EAAE;IACzB,MAAMC,WAAW,IAAI,CAACC,KAAK;IAE3B,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IAEpDF,WACGG,YAAY,CAAC;QACZ,IAAIC;QAEJ,IAAI;YACFA,UAAUpB,iBAAiBzB,YAAY,IAAI;QAC7C,EAAE,OAAO8C,OAAO;YACd,MAAMA;QACR;QAEA,MAAM,EAAEC,OAAO,EAAE,GAAG,MAAM/C,WAAW+C,OAAO;QAE5C,MAAM,EACJC,uBAAuB,EACvBC,qBAAqB,EACrBC,kBAAkB,EAClBC,mBAAmB,EACnBC,eAAe,EACfC,aAAa,EACbC,SAAS,EACTC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,kBAAkB,EAClBC,IAAI,EACL,GAAGC,QAAQ;QAEZ,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAE,GAAGH,QAAQ;QAExD,MAAMI,eAAsB,EAAE;QAC9B,yFAAyF;QACzF,MAAMC,UAAUpB,QAAQb,UAAU,GAAGI,KAAK6B,OAAO,GAAG,EAAE;QAEtD,IAAIjB,wBAAwBH,UAAU;YACpCP,QAAQ4B,IAAI,IAAIT,kBAAkBZ,SAAS,IAAI,EAAET;QACnD;QAEA,MAAM+B,sBAA6B,EAAE;QACrC,MAAMC,kBAAyB,EAAE;QAEjC,IAAInB,sBAAsBJ,UAAU;YAClC,MAAMwB,WAAW,IAAI,CAACC,UAAU,CAAC;gBAC/BC,gBAAgB;oBAAC;iBAAQ;gBACzBC,YAAY;oBAAC;iBAAO;gBACpBC,YAAY;oBAAC;oBAAO;oBAAS;oBAAQ;iBAAM;gBAC3CC,WAAW;oBAAC;oBAAS;iBAAM;gBAC3BC,cAAc;oBAAC;iBAAU;YAC3B;YAEArC,QAAQ4B,IAAI,CACVJ,aAAa;gBACXc,SAAST;gBACTU,KAAKT;gBACLU,SAAS,IAAI,CAACA,OAAO;gBACrBhE,aAAa,IAAI,CAACA,WAAW;gBAC7BiE,QAAQzB,UAAUT,QAAQjB,MAAM,EAAE,IAAI,CAAC1B,YAAY;gBACnDmE;gBACAW,YAAY,CAACrD,MACX9B,iBACE,IAAI,EACJuD,gBAAgB,IAAI,EAAEP,QAAQf,aAAa,IAAIH;YAErD;QAEJ;QAEA,MAAMsD,mBAA0B,EAAE;QAElC,IAAI/B,mBAAmBL,UAAU;YAC/B,MAAMqC,cAAc,IAAI,CAACZ,UAAU,CAAC;gBAClCC,gBAAgB;oBAAC;iBAAQ;gBACzBE,YAAY;oBAAC;iBAAQ;gBACrBC,WAAW,EAAE;gBACbF,YAAY,EAAE;YAChB;YAEAlC,QAAQ4B,IAAI,CACVH,UAAU;gBACRa,SAASK;gBACTjB;gBACAc,SAAS,IAAI,CAACA,OAAO;gBACrBhE,aAAa,IAAI,CAACA,WAAW;gBAC7BiE,QAAQzB,UAAUT,QAAQlB,GAAG,EAAE,IAAI,CAACzB,YAAY;gBAChDmE,UAAUa;gBACVF,YAAY,CAACrD,MAAgB9B,iBAAiB,IAAI,EAAE8B;YACtD;QAEJ;QAEA,MAAMwD,oBAA2B,EAAE;QACnC,MAAMC,gBAAuB,EAAE;QAE/B,IAAIjC,oBAAoBN,UAAU;YAChC,MAAMwC,eAAe,IAAI,CAACf,UAAU,CAAC;gBACnCC,gBAAgB;oBAAC;iBAAQ;gBACzBC,YAAY,EAAE;gBACdC,YAAY;oBAAC;oBAAO;oBAAS;oBAAQ;iBAAM;gBAC3CC,WAAW;oBAAC;oBAAS;iBAAM;YAC7B;YAEApC,QAAQ4B,IAAI,CACVL,WAAW;gBACTe,SAASO;gBACTN,KAAKO;gBACLpB;gBACAC;gBACAa,SAAS,IAAI,CAACA,OAAO;gBACrBhE,aAAa,IAAI,CAACA,WAAW;gBAC7BuD,UAAUgB;gBACVL,YAAY,CAACrD,MACX9B,iBACE,IAAI,EACJuD,gBAAgB,IAAI,EAAEP,QAAQf,aAAa,IAAIH;YAErD;QAEJ;QAEA,sEAAsE;QACtE,IAAIS,MAAM;YACR,MAAM,EAAEkD,GAAG,EAAE,GAAGlD;YAEhB,IAAIkD,OAAOA,IAAIC,IAAI,KAAK,WAAW;gBACjC,6CAA6C;gBAC7CrD,UAAUoD,IAAIE,IAAI;gBAClB/C,WAAWgD,YAAY,CAAC,WAAW;YACrC;QACF;QAEA,MAAM,EAAEvF,YAAY,EAAE,GAAG,IAAI;QAE7B,IAAIwF;QAEJ,IAAI;YACFA,SAAS,MAAM3C,QAAQT,SAASqD,OAAO,CAACzD,SAAS;gBAC/C0D,MAAM1F;gBACN2F,IAAI3F;gBACJiC,KAAKU,QAAQhB,SAAS,GAClB;oBACEiE,MAAM3D,MAAMuB,mBAAmBvB,KAAKjC,gBAAgB;oBACpD6F,QAAQ;oBACRC,YAAY;gBACd,IACA;YACN;QACF,EAAE,OAAOlD,OAAY;YACnB,IAAIA,MAAMmD,IAAI,EAAE;gBACd,IAAI,CAACC,aAAa,CAACpD,MAAMmD,IAAI;YAC/B;YAEA,MAAMnD,MAAMqD,IAAI,KAAK,mBACjB,IAAIxG,eAAemD,SACnBA;QACN;QAEA,KAAK,MAAMsD,WAAWV,OAAOW,QAAQ,GAAI;YACvC,IAAI,CAAC3E,WAAW,CAAC,IAAI9B,QAAQwG;QAC/B;QAEA,MAAMxB,UAAU;eACXO,kBAAkBxB,IAAI,CAACA;eACvBQ,oBAAoBR,IAAI,CAACA;eACzBsB,iBAAiBtB,IAAI,CAACA;SAC1B;QAED,MAAMkB,MAAM;eAAIT,gBAAgBT,IAAI,CAACA;eAAUyB,cAAczB,IAAI,CAACA;SAAM;QAExE,IAAId,QAAQ1C,OAAO,CAACiB,gBAAgB,KAAK,MAAM;YAC7CwD,QAAQ0B,OAAO,CAAC;gBACdC,YAAY;gBACZ5E,KAAK9B,iBAAiB,IAAI,EAAE+D,QAAQ4C,OAAO,CAAC;YAC9C;QACF;QAEA,MAAMC,aAAalD,cAAcqB,SAAS/B;QAC1C,MAAM6D,aAAalD,cAAckC,QAAQb,KAAKb,cAAcnB,SAAS,IAAI;QACzE,MAAM8D,aAAatD,cAAcY,SAASD,cAAcnB;QAExD,OAAO,CAAC,EAAE4D,WAAW,EAAEC,WAAW,EAAEC,WAAW,CAAC;IAClD,GACCC,IAAI,CACH,CAACC;QACCtE,SAAS,MAAMsE;IACjB,GACA,CAACC;QACCvE,SAASuE;IACX;AAEN"}