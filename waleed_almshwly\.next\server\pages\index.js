/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArrowDown,Download,Github,Linkedin,Mail!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!********************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowDown,Download,Github,Linkedin,Mail!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowDown: () => (/* reexport safe */ _icons_arrow_down_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Download: () => (/* reexport safe */ _icons_download_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Github: () => (/* reexport safe */ _icons_github_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Linkedin: () => (/* reexport safe */ _icons_linkedin_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Mail: () => (/* reexport safe */ _icons_mail_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_arrow_down_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/arrow-down.js */ \"./node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* harmony import */ var _icons_download_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/download.js */ \"./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _icons_github_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/github.js */ \"./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _icons_linkedin_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/linkedin.js */ \"./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _icons_mail_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/mail.js */ \"./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd0Rvd24sRG93bmxvYWQsR2l0aHViLExpbmtlZGluLE1haWwhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQzREO0FBQ0g7QUFDSjtBQUNJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2FsZWVkLWFsbXNod2x5LW5leHRqcy8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzYzMDYiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFycm93RG93biB9IGZyb20gXCIuL2ljb25zL2Fycm93LWRvd24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBEb3dubG9hZCB9IGZyb20gXCIuL2ljb25zL2Rvd25sb2FkLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2l0aHViIH0gZnJvbSBcIi4vaWNvbnMvZ2l0aHViLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTGlua2VkaW4gfSBmcm9tIFwiLi9pY29ucy9saW5rZWRpbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1haWwgfSBmcm9tIFwiLi9pY29ucy9tYWlsLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowDown,Download,Github,Linkedin,Mail!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ExternalLink,Github!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ExternalLink,Github!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExternalLink: () => (/* reexport safe */ _icons_external_link_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Github: () => (/* reexport safe */ _icons_github_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_external_link_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/external-link.js */ \"./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _icons_github_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/github.js */ \"./node_modules/lucide-react/dist/esm/icons/github.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FeHRlcm5hbExpbmssR2l0aHViIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUNrRSIsInNvdXJjZXMiOlsid2VicGFjazovL3dhbGVlZC1hbG1zaHdseS1uZXh0anMvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz82NTY3Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFeHRlcm5hbExpbmsgfSBmcm9tIFwiLi9pY29ucy9leHRlcm5hbC1saW5rLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2l0aHViIH0gZnJvbSBcIi4vaWNvbnMvZ2l0aHViLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ExternalLink,Github!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Mail,MapPin,Phone!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!**********************************************************************************************************!*\
  !*** __barrel_optimize__?names=Mail,MapPin,Phone!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mail: () => (/* reexport safe */ _icons_mail_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   MapPin: () => (/* reexport safe */ _icons_map_pin_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Phone: () => (/* reexport safe */ _icons_phone_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_mail_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/mail.js */ \"./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _icons_map_pin_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/map-pin.js */ \"./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _icons_phone_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/phone.js */ \"./node_modules/lucide-react/dist/esm/icons/phone.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1NYWlsLE1hcFBpbixQaG9uZSE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUNpRDtBQUNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2FsZWVkLWFsbXNod2x5LW5leHRqcy8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2VkN2QiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1haWwgfSBmcm9tIFwiLi9pY29ucy9tYWlsLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWFwUGluIH0gZnJvbSBcIi4vaWNvbnMvbWFwLXBpbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFBob25lIH0gZnJvbSBcIi4vaWNvbnMvcGhvbmUuanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Mail,MapPin,Phone!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Menu,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***********************************************************************************************!*\
  !*** __barrel_optimize__?names=Menu,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Menu: () => (/* reexport safe */ _icons_menu_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_menu_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/menu.js */ \"./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1NZW51LFghPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQ2lEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2FsZWVkLWFsbXNod2x5LW5leHRqcy8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzc5YjEiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1lbnUgfSBmcm9tIFwiLi9pY29ucy9tZW51LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgWCB9IGZyb20gXCIuL2ljb25zL3guanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Menu,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************!*\
  !*** __barrel_optimize__?names=X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/x.js */ "./node_modules/lucide-react/dist/esm/icons/x.js");



/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.tsx */ \"./pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toaster */ \"./src/components/ui/toaster.tsx\");\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sonner */ \"./src/components/ui/sonner.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"@tanstack/react-query\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _index_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/index.css */ \"./src/index.css\");\n/* harmony import */ var _index_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_index_css__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_toaster__WEBPACK_IMPORTED_MODULE_1__, _components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__, _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__, _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__]);\n([_components_ui_toaster__WEBPACK_IMPORTED_MODULE_1__, _components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__, _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__, _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_1__.Toaster, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_app.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_app.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_app.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_app.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_app.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDa0Q7QUFDUztBQUNEO0FBQ2U7QUFDeEM7QUFDYjtBQUVMLFNBQVNNLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQsTUFBTSxDQUFDQyxZQUFZLEdBQUdKLCtDQUFRQSxDQUFDLElBQU0sSUFBSUYsOERBQVdBO0lBRXBELHFCQUNFLDhEQUFDQyxzRUFBbUJBO1FBQUNNLFFBQVFEO2tCQUMzQiw0RUFBQ1AsbUVBQWVBOzs4QkFDZCw4REFBQ0YsMkRBQU9BOzs7Ozs4QkFDUiw4REFBQ0MsMERBQU1BOzs7Ozs4QkFDUCw4REFBQ007b0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93YWxlZWQtYWxtc2h3bHktbmV4dGpzLy4vcGFnZXMvX2FwcC50c3g/MmZiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnXG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90b2FzdGVyXCI7XG5pbXBvcnQgeyBUb2FzdGVyIGFzIFNvbm5lciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvc29ubmVyXCI7XG5pbXBvcnQgeyBUb29sdGlwUHJvdmlkZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3Rvb2x0aXBcIjtcbmltcG9ydCB7IFF1ZXJ5Q2xpZW50LCBRdWVyeUNsaWVudFByb3ZpZGVyIH0gZnJvbSBcIkB0YW5zdGFjay9yZWFjdC1xdWVyeVwiO1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgJ0AvaW5kZXguY3NzJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICBjb25zdCBbcXVlcnlDbGllbnRdID0gdXNlU3RhdGUoKCkgPT4gbmV3IFF1ZXJ5Q2xpZW50KCkpO1xuXG4gIHJldHVybiAoXG4gICAgPFF1ZXJ5Q2xpZW50UHJvdmlkZXIgY2xpZW50PXtxdWVyeUNsaWVudH0+XG4gICAgICA8VG9vbHRpcFByb3ZpZGVyPlxuICAgICAgICA8VG9hc3RlciAvPlxuICAgICAgICA8U29ubmVyIC8+XG4gICAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICAgIDwvVG9vbHRpcFByb3ZpZGVyPlxuICAgIDwvUXVlcnlDbGllbnRQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlRvYXN0ZXIiLCJTb25uZXIiLCJUb29sdGlwUHJvdmlkZXIiLCJRdWVyeUNsaWVudCIsIlF1ZXJ5Q2xpZW50UHJvdmlkZXIiLCJ1c2VTdGF0ZSIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsInF1ZXJ5Q2xpZW50IiwiY2xpZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_document.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/svg+xml\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Full Stack Developer specializing in modern web technologies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_document.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_document.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_document.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_document.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_document.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_document.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZG9jdW1lbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE0RDtBQUU3QyxTQUFTSTtJQUN0QixxQkFDRSw4REFBQ0osK0NBQUlBO1FBQUNLLE1BQUs7OzBCQUNULDhEQUFDSiwrQ0FBSUE7O2tDQUNILDhEQUFDSzt3QkFBS0MsU0FBUTs7Ozs7O2tDQUNkLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzt3QkFBZ0JDLE1BQUs7Ozs7OztrQ0FDM0MsOERBQUNMO3dCQUFLTSxNQUFLO3dCQUFjQyxTQUFROzs7Ozs7a0NBR2pDLDhEQUFDTDt3QkFBS0MsS0FBSTt3QkFBYUUsTUFBSzs7Ozs7O2tDQUM1Qiw4REFBQ0g7d0JBQUtDLEtBQUk7d0JBQWFFLE1BQUs7d0JBQTRCRyxhQUFZOzs7Ozs7a0NBQ3BFLDhEQUFDTjt3QkFDQ0csTUFBSzt3QkFDTEYsS0FBSTs7Ozs7Ozs7Ozs7OzBCQUdSLDhEQUFDTTs7a0NBQ0MsOERBQUNiLCtDQUFJQTs7Ozs7a0NBQ0wsOERBQUNDLHFEQUFVQTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93YWxlZWQtYWxtc2h3bHktbmV4dGpzLy4vcGFnZXMvX2RvY3VtZW50LnRzeD9kMzdkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEh0bWwsIEhlYWQsIE1haW4sIE5leHRTY3JpcHQgfSBmcm9tICduZXh0L2RvY3VtZW50J1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEb2N1bWVudCgpIHtcbiAgcmV0dXJuIChcbiAgICA8SHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxIZWFkPlxuICAgICAgICA8bWV0YSBjaGFyU2V0PVwidXRmLThcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgdHlwZT1cImltYWdlL3N2Zyt4bWxcIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIkZ1bGwgU3RhY2sgRGV2ZWxvcGVyIHNwZWNpYWxpemluZyBpbiBtb2Rlcm4gd2ViIHRlY2hub2xvZ2llc1wiIC8+XG5cbiAgICAgICAgey8qIEdvb2dsZSBGb250cyAqL31cbiAgICAgICAgPGxpbmsgcmVsPVwicHJlY29ubmVjdFwiIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwicHJlY29ubmVjdFwiIGhyZWY9XCJodHRwczovL2ZvbnRzLmdzdGF0aWMuY29tXCIgY3Jvc3NPcmlnaW49XCJhbm9ueW1vdXNcIiAvPlxuICAgICAgICA8bGlua1xuICAgICAgICAgIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PUNhaXJvOndnaHRAMjAwOzMwMDs0MDA7NTAwOzYwMDs3MDA7ODAwOzkwMCZmYW1pbHk9SW50ZXI6d2dodEAxMDA7MjAwOzMwMDs0MDA7NTAwOzYwMDs3MDA7ODAwOzkwMCZkaXNwbGF5PXN3YXBcIlxuICAgICAgICAgIHJlbD1cInN0eWxlc2hlZXRcIlxuICAgICAgICAvPlxuICAgICAgPC9IZWFkPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxNYWluIC8+XG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XG4gICAgICA8L2JvZHk+XG4gICAgPC9IdG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiSHRtbCIsIkhlYWQiLCJNYWluIiwiTmV4dFNjcmlwdCIsIkRvY3VtZW50IiwibGFuZyIsIm1ldGEiLCJjaGFyU2V0IiwibGluayIsInJlbCIsInR5cGUiLCJocmVmIiwibmFtZSIsImNvbnRlbnQiLCJjcm9zc09yaWdpbiIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Hero */ \"./src/components/Hero.tsx\");\n/* harmony import */ var _components_About__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/About */ \"./src/components/About.tsx\");\n/* harmony import */ var _components_Skills__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Skills */ \"./src/components/Skills.tsx\");\n/* harmony import */ var _components_Projects__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Projects */ \"./src/components/Projects.tsx\");\n/* harmony import */ var _components_Contact__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Contact */ \"./src/components/Contact.tsx\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Navigation */ \"./src/components/Navigation.tsx\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useTranslation */ \"./src/hooks/useTranslation.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Hero__WEBPACK_IMPORTED_MODULE_2__, _components_Projects__WEBPACK_IMPORTED_MODULE_5__, _components_Contact__WEBPACK_IMPORTED_MODULE_6__, _components_Navigation__WEBPACK_IMPORTED_MODULE_7__]);\n([_components_Hero__WEBPACK_IMPORTED_MODULE_2__, _components_Projects__WEBPACK_IMPORTED_MODULE_5__, _components_Contact__WEBPACK_IMPORTED_MODULE_6__, _components_Navigation__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Waleed Almshwly - Full Stack Developer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\index.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\index.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\index.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_8__.TranslationProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_7__.Navigation, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\index.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_2__.Hero, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\index.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_About__WEBPACK_IMPORTED_MODULE_3__.About, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\index.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Skills__WEBPACK_IMPORTED_MODULE_4__.Skills, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\index.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Projects__WEBPACK_IMPORTED_MODULE_5__.Projects, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\index.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Contact__WEBPACK_IMPORTED_MODULE_6__.Contact, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\index.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\index.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\pages\\\\index.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "./src/components/About.tsx":
/*!**********************************!*\
  !*** ./src/components/About.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   About: () => (/* binding */ About)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useTranslation */ \"./src/hooks/useTranslation.tsx\");\n/* harmony import */ var _hooks_useScrollReveal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useScrollReveal */ \"./src/hooks/useScrollReveal.tsx\");\n\n\n\nconst About = ()=>{\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    const { elementRef: aboutRef, isVisible: aboutVisible } = (0,_hooks_useScrollReveal__WEBPACK_IMPORTED_MODULE_2__.useScrollReveal)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        className: \"py-20 bg-gray-50 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-10 right-10 w-32 h-32 bg-gradient-to-br from-blue-400/5 to-purple-400/5 rounded-full blur-2xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: aboutRef,\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-center mb-12 ${(0,_hooks_useScrollReveal__WEBPACK_IMPORTED_MODULE_2__.getAnimationClass)(\"fadeInUp\", aboutVisible, 1)}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                children: t(\"aboutMe\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                children: t(\"aboutSubtitle\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-3xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-gray-600 mb-8 leading-relaxed text-lg ${(0,_hooks_useScrollReveal__WEBPACK_IMPORTED_MODULE_2__.getAnimationClass)(\"fadeInUp\", aboutVisible, 2)}`,\n                                children: t(\"aboutDescription1\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-gray-600 mb-8 leading-relaxed text-lg ${(0,_hooks_useScrollReveal__WEBPACK_IMPORTED_MODULE_2__.getAnimationClass)(\"fadeInUp\", aboutVisible, 3)}`,\n                                children: t(\"aboutDescription2\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 ${(0,_hooks_useScrollReveal__WEBPACK_IMPORTED_MODULE_2__.getAnimationClass)(\"fadeInUp\", aboutVisible, 4)}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center hover-lift p-4 rounded-lg bg-white/50 backdrop-blur-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-1 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                                children: \"5+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: t(\"yearsExperience\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center hover-lift p-4 rounded-lg bg-white/50 backdrop-blur-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\",\n                                                children: \"50+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: t(\"projectsCompleted\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center hover-lift p-4 rounded-lg bg-white/50 backdrop-blur-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-1 bg-gradient-to-r from-pink-600 to-red-600 bg-clip-text text-transparent\",\n                                                children: \"30+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: t(\"happyClients\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center hover-lift p-4 rounded-lg bg-white/50 backdrop-blur-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-1 bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent\",\n                                                children: \"15+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: t(\"awards\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\About.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/About.tsx\n");

/***/ }),

/***/ "./src/components/Contact.tsx":
/*!************************************!*\
  !*** ./src/components/Contact.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Contact: () => (/* binding */ Contact)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"__barrel_optimize__?names=Mail,MapPin,Phone!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"./src/components/ui/textarea.tsx\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTranslation */ \"./src/hooks/useTranslation.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _components_ui_input__WEBPACK_IMPORTED_MODULE_2__, _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _components_ui_input__WEBPACK_IMPORTED_MODULE_2__, _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst Contact = ()=>{\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const contactInfo = [\n        {\n            icon: _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Mail,\n            title: t(\"email\"),\n            content: \"<EMAIL>\",\n            href: \"mailto:<EMAIL>\"\n        },\n        {\n            icon: _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Phone,\n            title: t(\"phone\"),\n            content: \"+966 XXX XXX XXX\",\n            href: \"tel:+966XXXXXXXXX\"\n        },\n        {\n            icon: _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__.MapPin,\n            title: t(\"location\"),\n            content: t(\"saudiArabia\"),\n            href: \"#\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"contact\",\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t(\"contact\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: t(\"contactDescription\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                    children: t(\"getInTouch\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: contactInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(info.icon, {\n                                                        className: \"h-5 w-5 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: info.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 52,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        info.href !== \"#\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: info.href,\n                                                            className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n                                                            children: info.content\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 54,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: info.content\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 61,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                    children: t(\"sendMessage\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                    placeholder: t(\"firstName\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                    placeholder: t(\"lastName\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"email\",\n                                            placeholder: t(\"email\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: t(\"subject\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                            placeholder: t(\"message\"),\n                                            rows: 5\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            className: \"w-full bg-gray-900 hover:bg-gray-800\",\n                                            children: t(\"sendMessage\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Contact.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./src/components/Contact.tsx\n");

/***/ }),

/***/ "./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hero: () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/avatar */ \"./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Download,Github,Linkedin,Mail!=!lucide-react */ \"__barrel_optimize__?names=ArrowDown,Download,Github,Linkedin,Mail!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useTranslation */ \"./src/hooks/useTranslation.tsx\");\n/* harmony import */ var _hooks_useRTL__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useRTL */ \"./src/hooks/useRTL.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst Hero = ()=>{\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const { iconLeft, isRTL } = (0,_hooks_useRTL__WEBPACK_IMPORTED_MODULE_4__.useRTL)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"home\",\n        className: \"pt-20 min-h-screen flex items-center justify-center relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 left-20 w-64 h-64 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 right-20 w-48 h-48 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-blue-400/5 to-purple-400/5 rounded-full blur-3xl animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: heroRef,\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `mb-8 ${getAnimationClass(\"scaleIn\", heroVisible, 1)}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                    className: \"w-32 h-32 mx-auto mb-6 ring-4 ring-white shadow-2xl hover-lift hover-glow transition-all duration-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                            src: \"/lovable-uploads/6d3fd0a4-5be1-42cf-ada4-ae04ab1679d3.png\",\n                                            alt: \"Waleed Almshwly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                            className: \"text-2xl font-semibold bg-gradient-to-br from-blue-600 to-purple-600 text-white\",\n                                            children: \"WA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sparkles, {\n                                    className: \"absolute top-2 right-2 w-8 h-8 text-yellow-400 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: `text-4xl md:text-6xl font-bold mb-6 ${getAnimationClass(\"fadeInUp\", heroVisible, 2)}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-gradient\",\n                            children: t(\"fullStackDeveloper\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: `text-lg md:text-xl text-gray-600 mb-10 max-w-2xl mx-auto leading-relaxed ${getAnimationClass(\"fadeInUp\", heroVisible, 3)}`,\n                        children: t(\"heroDescription\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex flex-col sm:flex-row gap-4 justify-center items-center mb-8 ${isRTL ? \"sm:flex-row-reverse\" : \"\"} ${getAnimationClass(\"fadeInUp\", heroVisible, 4)}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                size: \"lg\",\n                                className: \"bg-gray-900 hover:bg-gray-800 text-white hover-lift hover-slide\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#projects\",\n                                    className: \"flex items-center gap-2\",\n                                    children: t(\"viewMyWork\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"outline\",\n                                size: \"lg\",\n                                className: \"hover-lift\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#contact\",\n                                    children: t(\"getInTouch\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"outline\",\n                                size: \"lg\",\n                                className: \"hover-lift\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/resume.pdf\",\n                                    download: true,\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Download, {\n                                            className: `w-4 h-4 ${iconLeft}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        t(\"downloadCV\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex justify-center gap-6 mb-12 ${isRTL ? \"flex-row-reverse\" : \"\"} ${getAnimationClass(\"fadeInUp\", heroVisible, 5)}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://github.com\",\n                                className: \"p-3 rounded-full bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 magnetic transition-all duration-300\",\n                                \"aria-label\": \"GitHub Profile\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Github, {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://linkedin.com\",\n                                className: \"p-3 rounded-full bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 magnetic transition-all duration-300\",\n                                \"aria-label\": \"LinkedIn Profile\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Linkedin, {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"mailto:<EMAIL>\",\n                                className: \"p-3 rounded-full bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 magnetic transition-all duration-300\",\n                                \"aria-label\": \"Send Email\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Mail, {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-center ${getAnimationClass(\"fadeInUp\", heroVisible, 6)}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"#about\",\n                            className: \"inline-block p-3 rounded-full bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 hover-lift animate-bounce\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ArrowDown, {\n                                className: \"h-6 w-6 text-gray-400 hover:text-blue-500 transition-colors duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Hero.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./src/components/Hero.tsx\n");

/***/ }),

/***/ "./src/components/LanguageToggle.tsx":
/*!*******************************************!*\
  !*** ./src/components/LanguageToggle.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageToggle: () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useTranslation */ \"./src/hooks/useTranslation.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_1__]);\n_components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst LanguageToggle = ()=>{\n    const { language, setLanguage } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-1 bg-gray-100 rounded-full p-1\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                variant: language === \"en\" ? \"default\" : \"ghost\",\n                size: \"sm\",\n                onClick: ()=>setLanguage(\"en\"),\n                className: `text-xs rounded-full px-3 py-1 transition-colors ${language === \"en\" ? \"bg-white text-gray-900 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"}`,\n                children: \"EN\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                variant: language === \"ar\" ? \"default\" : \"ghost\",\n                size: \"sm\",\n                onClick: ()=>setLanguage(\"ar\"),\n                className: `text-xs rounded-full px-3 py-1 transition-colors ${language === \"ar\" ? \"bg-white text-gray-900 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"}`,\n                children: \"عربي\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\LanguageToggle.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/LanguageToggle.tsx\n");

/***/ }),

/***/ "./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"__barrel_optimize__?names=Menu,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/LanguageToggle */ \"./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTranslation */ \"./src/hooks/useTranslation.tsx\");\n/* harmony import */ var _hooks_useRTL__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useRTL */ \"./src/hooks/useRTL.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst Navigation = ()=>{\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const { isRTL } = (0,_hooks_useRTL__WEBPACK_IMPORTED_MODULE_5__.useRTL)();\n    const navItems = [\n        {\n            href: \"#about\",\n            label: t(\"about\")\n        },\n        {\n            href: \"#skills\",\n            label: t(\"skills\")\n        },\n        {\n            href: \"#projects\",\n            label: t(\"projects\")\n        },\n        {\n            href: \"#contact\",\n            label: t(\"contact\")\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full bg-white/95 backdrop-blur-sm z-50 border-b border-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: \"Waleed Almshwly\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex items-center ${isRTL ? \"space-x-reverse space-x-6\" : \"space-x-6\"}`,\n                                    children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: item.href,\n                                            className: \"text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors\",\n                                            children: item.label\n                                        }, item.href, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__.LanguageToggle, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__.LanguageToggle, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: ()=>setIsOpen(!isOpen),\n                                    children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__.X, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 25\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Menu, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 53\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3\",\n                        children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: item.href,\n                                className: \"text-gray-700 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium\",\n                                onClick: ()=>setIsOpen(false),\n                                children: item.label\n                            }, item.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Navigation.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Navigation.tsx\n");

/***/ }),

/***/ "./src/components/Projects.tsx":
/*!*************************************!*\
  !*** ./src/components/Projects.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Projects: () => (/* binding */ Projects)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github!=!lucide-react */ \"__barrel_optimize__?names=ExternalLink,Github!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useTranslation */ \"./src/hooks/useTranslation.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_1__]);\n_components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst Projects = ()=>{\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const projects = [\n        {\n            title: t(\"ecommercePlatform\"),\n            description: t(\"ecommerceDescription\"),\n            technologies: [\n                \"Next.js\",\n                \"TypeScript\",\n                \"Stripe\",\n                \"PostgreSQL\",\n                \"Tailwind\"\n            ],\n            liveUrl: \"#\",\n            githubUrl: \"#\"\n        },\n        {\n            title: t(\"taskManagementApp\"),\n            description: t(\"taskDescription\"),\n            technologies: [\n                \"React\",\n                \"Node.js\",\n                \"Socket.io\",\n                \"MongoDB\",\n                \"Express\"\n            ],\n            liveUrl: \"#\",\n            githubUrl: \"#\"\n        },\n        {\n            title: t(\"socialMediaDashboard\"),\n            description: t(\"socialDescription\"),\n            technologies: [\n                \"Next.js\",\n                \"Chart.js\",\n                \"Firebase\",\n                \"Tailwind\",\n                \"TypeScript\"\n            ],\n            liveUrl: \"#\",\n            githubUrl: \"#\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"projects\",\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t(\"projects\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: t(\"projectsDescription\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                    children: project.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4 leading-relaxed\",\n                                    children: project.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-6\",\n                                    children: project.technologies.map((tech, techIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full\",\n                                            children: tech\n                                        }, techIndex, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            size: \"sm\",\n                                            className: \"flex-1 bg-gray-900 hover:bg-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: project.liveUrl,\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_3__.ExternalLink, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                                                        lineNumber: 62,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    t(\"liveDemo\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: project.githubUrl,\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Github, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    t(\"code\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Projects.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Projects.tsx\n");

/***/ }),

/***/ "./src/components/Skills.tsx":
/*!***********************************!*\
  !*** ./src/components/Skills.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skills: () => (/* binding */ Skills)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useTranslation */ \"./src/hooks/useTranslation.tsx\");\n\n\nconst Skills = ()=>{\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    const skillCategories = [\n        {\n            title: t(\"frontendDevelopment\"),\n            skills: [\n                \"React.js\",\n                \"Next.js\",\n                \"TypeScript\",\n                \"Tailwind CSS\",\n                \"HTML/CSS\",\n                \"Vue.js\"\n            ]\n        },\n        {\n            title: t(\"backendDevelopment\"),\n            skills: [\n                \"Node.js\",\n                \"Express.js\",\n                \"Python\",\n                \"PostgreSQL\",\n                \"MongoDB\",\n                \"REST APIs\"\n            ]\n        },\n        {\n            title: t(\"toolsTechnologies\"),\n            skills: [\n                \"Git/GitHub\",\n                \"Docker\",\n                \"AWS\",\n                \"Vercel\",\n                \"Firebase\",\n                \"Linux\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"skills\",\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t(\"skills\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Skills.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: t(\"skillsDescription\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Skills.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Skills.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: skillCategories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                    children: category.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Skills.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: category.skills.map((skill, skillIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg px-4 py-3 text-gray-700 hover:bg-gray-100 transition-colors\",\n                                            children: skill\n                                        }, skillIndex, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Skills.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Skills.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Skills.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Skills.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Skills.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\Skills.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Skills.tsx\n");

/***/ }),

/***/ "./src/components/ui/avatar.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/avatar.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"@radix-ui/react-avatar\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined));\nAvatar.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Root.displayName;\nconst AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Image, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"aspect-square h-full w-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nAvatarImage.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Image.displayName;\nconst AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Fallback, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex h-full w-full items-center justify-center rounded-full bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 37,\n        columnNumber: 3\n    }, undefined));\nAvatarFallback.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Fallback.displayName;\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/avatar.tsx\n");

/***/ }),

/***/ "./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-slot */ \"@radix-ui/react-slot\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/button.tsx\n");

/***/ }),

/***/ "./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxrWUFDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL3dhbGVlZC1hbG1zaHdseS1uZXh0anMvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3g/Yzk4MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxyXG5cclxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxyXG5cclxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiaW5wdXRcIj4+KFxyXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8aW5wdXRcclxuICAgICAgICB0eXBlPXt0eXBlfVxyXG4gICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtYmFzZSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBmaWxlOnRleHQtZm9yZWdyb3VuZCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwIG1kOnRleHQtc21cIixcclxuICAgICAgICAgIGNsYXNzTmFtZVxyXG4gICAgICAgICl9XHJcbiAgICAgICAgcmVmPXtyZWZ9XHJcbiAgICAgICAgey4uLnByb3BzfVxyXG4gICAgICAvPlxyXG4gICAgKVxyXG4gIH1cclxuKVxyXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxyXG5cclxuZXhwb3J0IHsgSW5wdXQgfVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/ui/input.tsx\n");

/***/ }),

/***/ "./src/components/ui/sonner.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/sonner.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* reexport safe */ sonner__WEBPACK_IMPORTED_MODULE_2__.toast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"sonner\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([sonner__WEBPACK_IMPORTED_MODULE_2__]);\nsonner__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Toaster = ({ ...props })=>{\n    const { theme = \"system\" } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        toastOptions: {\n            classNames: {\n                toast: \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n                description: \"group-[.toast]:text-muted-foreground\",\n                actionButton: \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n                cancelButton: \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\"\n            }\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\sonner.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/sonner.tsx\n");

/***/ }),

/***/ "./src/components/ui/textarea.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/textarea.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy91aS90ZXh0YXJlYS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBS2hDLE1BQU1FLHlCQUFXRiw2Q0FBZ0IsQ0FDL0IsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4QixxQkFDRSw4REFBQ0M7UUFDQ0gsV0FBV0gsOENBQUVBLENBQ1gsd1NBQ0FHO1FBRUZFLEtBQUtBO1FBQ0osR0FBR0QsS0FBSzs7Ozs7O0FBR2Y7QUFFRkgsU0FBU00sV0FBVyxHQUFHO0FBRUoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93YWxlZWQtYWxtc2h3bHktbmV4dGpzLy4vc3JjL2NvbXBvbmVudHMvdWkvdGV4dGFyZWEudHN4PzU5MzAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcclxuXHJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVGV4dGFyZWFQcm9wc1xyXG4gIGV4dGVuZHMgUmVhY3QuVGV4dGFyZWFIVE1MQXR0cmlidXRlczxIVE1MVGV4dEFyZWFFbGVtZW50PiB7fVxyXG5cclxuY29uc3QgVGV4dGFyZWEgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxUZXh0QXJlYUVsZW1lbnQsIFRleHRhcmVhUHJvcHM+KFxyXG4gICh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8dGV4dGFyZWFcclxuICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgXCJmbGV4IG1pbi1oLVs4MHB4XSB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXHJcbiAgICAgICAgICBjbGFzc05hbWVcclxuICAgICAgICApfVxyXG4gICAgICAgIHJlZj17cmVmfVxyXG4gICAgICAgIHsuLi5wcm9wc31cclxuICAgICAgLz5cclxuICAgIClcclxuICB9XHJcbilcclxuVGV4dGFyZWEuZGlzcGxheU5hbWUgPSBcIlRleHRhcmVhXCJcclxuXHJcbmV4cG9ydCB7IFRleHRhcmVhIH1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJUZXh0YXJlYSIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsInRleHRhcmVhIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/ui/textarea.tsx\n");

/***/ }),

/***/ "./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-toast */ \"@radix-ui/react-toast\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"__barrel_optimize__?names=X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive group border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.X, {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 84,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 75,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 93,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 105,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Description.displayName;\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/toast.tsx\n");

/***/ }),

/***/ "./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-toast */ \"./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toast */ \"./src/components/ui/toast.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__]);\n_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastClose, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastViewport, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/toaster.tsx\n");

/***/ }),

/***/ "./src/components/ui/tooltip.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/tooltip.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-tooltip */ \"@radix-ui/react-tooltip\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst TooltipProvider = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__.Provider;\nconst Tooltip = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__.Root;\nconst TooltipTrigger = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__.Trigger;\nconst TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__.Content, {\n        ref: ref,\n        sideOffset: sideOffset,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nTooltipContent.displayName = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__.Content.displayName;\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/tooltip.tsx\n");

/***/ }),

/***/ "./src/hooks/use-toast.ts":
/*!********************************!*\
  !*** ./src/hooks/use-toast.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/use-toast.ts\n");

/***/ }),

/***/ "./src/hooks/useRTL.tsx":
/*!******************************!*\
  !*** ./src/hooks/useRTL.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRTL: () => (/* binding */ useRTL)\n/* harmony export */ });\n/* harmony import */ var _useTranslation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useTranslation */ \"./src/hooks/useTranslation.tsx\");\n\nconst useRTL = ()=>{\n    const { language } = (0,_useTranslation__WEBPACK_IMPORTED_MODULE_0__.useTranslation)();\n    const isRTL = language === \"ar\";\n    // Helper function to get flex direction based on RTL\n    const getFlexDirection = (reverse = false)=>{\n        if (isRTL) {\n            return reverse ? \"flex-row\" : \"flex-row-reverse\";\n        }\n        return reverse ? \"flex-row-reverse\" : \"flex-row\";\n    };\n    // Helper function to get margin classes for icons\n    const getIconMargin = (position = \"left\")=>{\n        if (isRTL) {\n            return position === \"left\" ? \"ml-2\" : \"mr-2\";\n        }\n        return position === \"left\" ? \"mr-2\" : \"ml-2\";\n    };\n    // Helper function to get text alignment\n    const getTextAlign = ()=>{\n        return isRTL ? \"text-right\" : \"text-left\";\n    };\n    // Helper function to get padding/margin for RTL\n    const getRTLSpacing = (property, side, value)=>{\n        const oppositeSide = side === \"left\" ? \"right\" : \"left\";\n        const targetSide = isRTL ? oppositeSide : side;\n        return `${property === \"padding\" ? \"p\" : \"m\"}${targetSide[0]}-${value}`;\n    };\n    // Helper function for positioning\n    const getPosition = (side)=>{\n        return isRTL ? side === \"left\" ? \"right\" : \"left\" : side;\n    };\n    return {\n        isRTL,\n        getFlexDirection,\n        getIconMargin,\n        getTextAlign,\n        getRTLSpacing,\n        getPosition,\n        // Common class combinations\n        iconLeft: isRTL ? \"ml-2\" : \"mr-2\",\n        iconRight: isRTL ? \"mr-2\" : \"ml-2\",\n        flexRow: isRTL ? \"flex-row-reverse\" : \"flex-row\",\n        flexRowReverse: isRTL ? \"flex-row\" : \"flex-row-reverse\",\n        textAlign: isRTL ? \"text-right\" : \"text-left\",\n        floatLeft: isRTL ? \"float-right\" : \"float-left\",\n        floatRight: isRTL ? \"float-left\" : \"float-right\"\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useRTL.tsx\n");

/***/ }),

/***/ "./src/hooks/useScrollReveal.tsx":
/*!***************************************!*\
  !*** ./src/hooks/useScrollReveal.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAnimationClass: () => (/* binding */ getAnimationClass),\n/* harmony export */   useScrollReveal: () => (/* binding */ useScrollReveal)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useScrollReveal = (options = {})=>{\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { threshold = 0.1, rootMargin = \"0px 0px -50px 0px\", triggerOnce = true } = options;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const element = elementRef.current;\n        if (!element) return;\n        const observer = new IntersectionObserver(([entry])=>{\n            if (entry.isIntersecting) {\n                setIsVisible(true);\n                if (triggerOnce) {\n                    observer.unobserve(element);\n                }\n            } else if (!triggerOnce) {\n                setIsVisible(false);\n            }\n        }, {\n            threshold,\n            rootMargin\n        });\n        observer.observe(element);\n        return ()=>{\n            observer.unobserve(element);\n        };\n    }, [\n        threshold,\n        rootMargin,\n        triggerOnce\n    ]);\n    return {\n        elementRef,\n        isVisible\n    };\n};\n// Animation variants\nconst getAnimationClass = (animation, isVisible, delay = 0)=>{\n    const baseClasses = \"transition-all duration-700 ease-out\";\n    const delayClass = delay > 0 ? `animate-stagger-${Math.min(delay, 5)}` : \"\";\n    if (!isVisible) {\n        return `${baseClasses} opacity-0 ${getInitialState(animation)}`;\n    }\n    return `${baseClasses} opacity-100 ${getAnimatedState(animation)} ${delayClass}`;\n};\nconst getInitialState = (animation)=>{\n    switch(animation){\n        case \"fadeInUp\":\n            return \"translate-y-8\";\n        case \"fadeInLeft\":\n            return \"-translate-x-8\";\n        case \"fadeInRight\":\n            return \"translate-x-8\";\n        case \"scaleIn\":\n            return \"scale-95\";\n        default:\n            return \"translate-y-8\";\n    }\n};\nconst getAnimatedState = (animation)=>{\n    switch(animation){\n        case \"fadeInUp\":\n        case \"fadeInLeft\":\n        case \"fadeInRight\":\n            return \"translate-x-0 translate-y-0\";\n        case \"scaleIn\":\n            return \"scale-100\";\n        default:\n            return \"translate-x-0 translate-y-0\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useScrollReveal.tsx\n");

/***/ }),

/***/ "./src/hooks/useTranslation.tsx":
/*!**************************************!*\
  !*** ./src/hooks/useTranslation.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TranslationProvider: () => (/* binding */ TranslationProvider),\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst translations = {\n    ar: {\n        // Navigation\n        home: \"الرئيسية\",\n        about: \"نبذة عني\",\n        skills: \"المهارات\",\n        experience: \"الخبرة\",\n        achievements: \"الإنجازات\",\n        projects: \"المشاريع\",\n        contact: \"التواصل\",\n        portfolio: \"معرض الأعمال\",\n        // Hero Section\n        fullStackDeveloper: \"مطور ويب متكامل\",\n        heroDescription: \"أقوم ببناء تجارب رقمية استثنائية باستخدام تقنيات الويب الحديثة. شغوف بإنشاء تطبيقات قابلة للتوسع باستخدام Next.js وReact وNode.js.\",\n        viewMyWork: \"عرض أعمالي\",\n        getInTouch: \"تواصل معي\",\n        downloadCV: \"تحميل السيرة الذاتية\",\n        // About Section\n        aboutMe: \"نبذة عني\",\n        aboutSubtitle: \"مطور ويب متكامل شغوف بخبرة في تقنيات الويب الحديثة\",\n        myJourney: \"رحلتي\",\n        aboutDescription1: \"أنا مطور ويب متكامل شغوف بخبرة تزيد عن 3 سنوات في بناء تطبيقات الويب. أتخصص في نظام JavaScript البيئي، مع خبرة عميقة في React وNext.js وNode.js وتقنيات قواعد البيانات الحديثة.\",\n        aboutDescription2: \"أحب إنشاء كود نظيف وفعال وبناء تطبيقات سهلة الاستخدام تحل مشاكل العالم الحقيقي. عندما لا أكون أبرمج، يمكنك أن تجدني أستكشف تقنيات جديدة أو أساهم في مشاريع مفتوحة المصدر.\",\n        yearsExperience: \"سنوات خبرة\",\n        professionalExperience: \"خبرة مهنية\",\n        projectsCompleted: \"مشروع مكتمل\",\n        successfullyCompleted: \"مكتمل بنجاح\",\n        fullStack: \"متكامل\",\n        frontendBackendExpert: \"خبير الواجهة الأمامية والخلفية\",\n        // Skills Section\n        technicalSkills: \"المهارات التقنية\",\n        skillsDescription: \"هذه هي التقنيات والأدوات التي أعمل بها لتحويل الأفكار إلى واقع\",\n        frontendDevelopment: \"تطوير الواجهة الأمامية\",\n        backendDevelopment: \"تطوير الواجهة الخلفية\",\n        toolsTechnologies: \"الأدوات والتقنيات\",\n        mobileDesign: \"التطبيقات المحمولة والتصميم\",\n        // Experience Section\n        workExperience: \"الخبرة المهنية\",\n        experienceDescription: \"رحلتي المهنية والتأثير الذي أحدثته\",\n        seniorFullStackDeveloper: \"مطور ويب متكامل أول\",\n        techSolutionsInc: \"شركة التقنيات المتكاملة\",\n        present: \"الحاضر\",\n        fullStackDeveloperRole: \"مطور ويب متكامل\",\n        digitalAgencyPro: \"وكالة التسويق الرقمي برو\",\n        frontendDeveloper: \"مطور واجهة أمامية\",\n        startupXYZ: \"شركة ناشئة XYZ\",\n        // Experience descriptions\n        exp1_1: \"قدت تطوير عدة تطبيقات ويب باستخدام Next.js وNode.js\",\n        exp1_2: \"نفذت بنية الخدمات المصغرة مما قلل أوقات التحميل بنسبة 40%\",\n        exp1_3: \"وجهت المطورين المبتدئين وأجريت مراجعات للكود\",\n        exp1_4: \"تعاونت مع فريق التصميم لإنشاء مكونات واجهة متجاوبة\",\n        exp2_1: \"طورت وصنت تطبيقات React لعملاء متنوعين\",\n        exp2_2: \"بنيت واجهات برمجة تطبيقات RESTful باستخدام Express.js وMongoDB\",\n        exp2_3: \"نفذت أنظمة المصادقة والتفويض\",\n        exp2_4: \"حسنت أداء التطبيقات واستعلامات قواعد البيانات\",\n        exp3_1: \"أنشأت واجهات ويب متجاوبة باستخدام React وأطر CSS\",\n        exp3_2: \"تعاونت مع مصممي UX/UI لتنفيذ تصاميم دقيقة\",\n        exp3_3: \"دمجت واجهات برمجة التطبيقات والخدمات الخارجية\",\n        exp3_4: \"شاركت في عمليات التطوير الرشيقة\",\n        // Projects Section\n        featuredProjects: \"المشاريع المميزة\",\n        projectsDescription: \"هنا بعض مشاريعي الحديثة التي تعرض مهاراتي وخبرتي\",\n        ecommercePlatform: \"منصة التجارة الإلكترونية\",\n        ecommerceDescription: \"حل تجارة إلكترونية متكامل مع Next.js، تكامل Stripe، ولوحة تحكم إدارية. يتضمن إدارة المنتجات وتتبع الطلبات ومعالجة المدفوعات.\",\n        taskManagementApp: \"تطبيق إدارة المهام\",\n        taskDescription: \"تطبيق إدارة مهام تعاوني مع تحديثات فورية ووظائف السحب والإفلات وميزات التعاون الجماعي.\",\n        socialMediaDashboard: \"لوحة تحكم وسائل التواصل\",\n        socialDescription: \"لوحة تحكم تحليلية لإدارة وسائل التواصل الاجتماعي مع تصور البيانات وميزات الجدولة وتكامل متعدد المنصات.\",\n        aiChatBot: \"روبوت محادثة ذكي\",\n        aiChatBotDescription: \"روبوت محادثة متقدم مدعوم بالذكاء الاصطناعي مع معالجة اللغة الطبيعية وردود فعل في الوقت الفعلي وواجهة مستخدم بديهية.\",\n        cryptoTracker: \"متتبع العملات المشفرة\",\n        cryptoTrackerDescription: \"تطبيق شامل لتتبع العملات المشفرة مع الرسوم البيانية المباشرة وتنبيهات الأسعار ومحفظة الاستثمار.\",\n        weatherApp: \"تطبيق الطقس\",\n        weatherAppDescription: \"تطبيق طقس محمول مع توقعات دقيقة وخرائط تفاعلية وتنبيهات الطقس القاسي.\",\n        liveDemo: \"عرض مباشر\",\n        code: \"الكود\",\n        completed: \"مكتمل\",\n        inProgress: \"قيد التطوير\",\n        // Contact Section\n        getInTouchTitle: \"تواصل معي\",\n        contactDescription: \"مستعد لبدء مشروعك القادم؟ لنعمل معاً لإنشاء شيء مذهل\",\n        letsConnect: \"لنتواصل\",\n        contactText: \"أنا دائماً مهتم بسماع الفرص الجديدة والمشاريع المثيرة. سواء كنت شركة تبحث عن التوظيف، أو زميل مطور يود التواصل، أحب أن أسمع منك.\",\n        email: \"البريد الإلكتروني\",\n        phone: \"الهاتف\",\n        location: \"الموقع\",\n        remote: \"عن بُعد / مفتوح للانتقال\",\n        sendMessage: \"إرسال رسالة\",\n        name: \"الاسم\",\n        yourName: \"اسمك\",\n        yourEmail: \"بريدك الإلكتروني\",\n        subject: \"الموضوع\",\n        projectDiscussion: \"مناقشة مشروع\",\n        message: \"الرسالة\",\n        messagePlaceholder: \"أخبرني عن مشروعك...\",\n        sendMessageBtn: \"إرسال الرسالة\",\n        saudiArabia: \"المملكة العربية السعودية\",\n        availability: \"التوفر\",\n        availableForWork: \"متاح للعمل\",\n        enterYourName: \"أدخل اسمك\",\n        enterYourEmail: \"أدخل بريدك الإلكتروني\",\n        enterSubject: \"أدخل الموضوع\",\n        enterYourMessage: \"أدخل رسالتك\",\n        sending: \"جاري الإرسال...\",\n        // Achievements Section\n        achievementsTitle: \"الإنجازات والشهادات\",\n        achievementsDescription: \"مسيرة من النجاحات والإنجازات المهنية التي حققتها\",\n        topDeveloper: \"أفضل مطور للعام\",\n        topDeveloperDesc: \"حصلت على لقب أفضل مطور في الشركة لعام 2024 بناءً على الأداء والإبداع\",\n        fullStackCertification: \"شهادة التطوير المتكامل\",\n        fullStackCertificationDesc: \"شهادة معتمدة في تطوير الويب المتكامل من أكاديمية التقنية المتقدمة\",\n        openSourceContributor: \"مساهم في المصادر المفتوحة\",\n        openSourceContributorDesc: \"مساهمة فعالة في أكثر من 20 مشروع مفتوح المصدر على GitHub\",\n        hackathonWinner: \"فائز في الهاكاثون\",\n        hackathonWinnerDesc: \"المركز الأول في هاكاثون التقنية المالية لعام 2023\",\n        clientSatisfaction: \"رضا العملاء 100%\",\n        clientSatisfactionDesc: \"تقييم مثالي من جميع العملاء مع معدل إكمال المشاريع في الوقت المحدد\",\n        teamLeadership: \"قيادة الفريق\",\n        teamLeadershipDesc: \"قدت فريق من 8 مطورين في مشاريع متعددة بنجاح\",\n        projectsDelivered: \"مشروع مسلم\",\n        happyClients: \"عميل راضي\",\n        codeCommits: \"كود كوميت\",\n        countriesServed: \"دولة خدمت\",\n        recognition: \"تقدير\",\n        certification: \"شهادة\",\n        contribution: \"مساهمة\",\n        competition: \"مسابقة\",\n        performance: \"أداء\",\n        leadership: \"قيادة\"\n    },\n    en: {\n        // Navigation\n        home: \"Home\",\n        about: \"About\",\n        skills: \"Skills\",\n        experience: \"Experience\",\n        achievements: \"Achievements\",\n        projects: \"Projects\",\n        contact: \"Contact\",\n        portfolio: \"Portfolio\",\n        // Hero Section\n        fullStackDeveloper: \"Full Stack Developer\",\n        heroDescription: \"I build exceptional digital experiences with modern web technologies. Passionate about creating scalable applications using Next.js, React, and Node.js.\",\n        viewMyWork: \"View My Work\",\n        getInTouch: \"Get In Touch\",\n        downloadCV: \"Download CV\",\n        // About Section\n        aboutMe: \"About Me\",\n        aboutSubtitle: \"Passionate Full Stack Developer with expertise in modern web technologies\",\n        myJourney: \"My Journey\",\n        aboutDescription1: \"I'm a passionate Full Stack Developer with over 3 years of experience building web applications. I specialize in JavaScript ecosystem, with deep expertise in React, Next.js, Node.js, and modern database technologies.\",\n        aboutDescription2: \"I love creating clean, efficient code and building user-friendly applications that solve real-world problems. When I'm not coding, you can find me exploring new technologies or contributing to open-source projects.\",\n        yearsExperience: \"Years\",\n        professionalExperience: \"Professional Experience\",\n        projectsCompleted: \"Projects\",\n        successfullyCompleted: \"Successfully Completed\",\n        fullStack: \"Full Stack\",\n        frontendBackendExpert: \"Frontend & Backend Expert\",\n        // Skills Section\n        technicalSkills: \"Technical Skills\",\n        skillsDescription: \"Here are the technologies and tools I work with to bring ideas to life\",\n        frontendDevelopment: \"Frontend Development\",\n        backendDevelopment: \"Backend Development\",\n        toolsTechnologies: \"Tools & Technologies\",\n        mobileDesign: \"Mobile & Design\",\n        // Experience Section\n        workExperience: \"Work Experience\",\n        experienceDescription: \"My professional journey and the impact I've made\",\n        seniorFullStackDeveloper: \"Senior Full Stack Developer\",\n        techSolutionsInc: \"Tech Solutions Inc.\",\n        present: \"Present\",\n        fullStackDeveloperRole: \"Full Stack Developer\",\n        digitalAgencyPro: \"Digital Agency Pro\",\n        frontendDeveloper: \"Frontend Developer\",\n        startupXYZ: \"StartupXYZ\",\n        // Experience descriptions\n        exp1_1: \"Led development of multiple web applications using Next.js and Node.js\",\n        exp1_2: \"Implemented microservices architecture reducing load times by 40%\",\n        exp1_3: \"Mentored junior developers and conducted code reviews\",\n        exp1_4: \"Collaborated with design team to create responsive UI components\",\n        exp2_1: \"Developed and maintained React applications for various clients\",\n        exp2_2: \"Built RESTful APIs using Express.js and MongoDB\",\n        exp2_3: \"Implemented authentication and authorization systems\",\n        exp2_4: \"Optimized application performance and database queries\",\n        exp3_1: \"Created responsive web interfaces using React and CSS frameworks\",\n        exp3_2: \"Collaborated with UX/UI designers to implement pixel-perfect designs\",\n        exp3_3: \"Integrated third-party APIs and services\",\n        exp3_4: \"Participated in agile development processes\",\n        // Projects Section\n        featuredProjects: \"Featured Projects\",\n        projectsDescription: \"Here are some of my recent projects that showcase my skills and expertise\",\n        ecommercePlatform: \"E-Commerce Platform\",\n        ecommerceDescription: \"Full-stack e-commerce solution with Next.js, Stripe integration, and admin dashboard. Features include product management, order tracking, and payment processing.\",\n        taskManagementApp: \"Task Management App\",\n        taskDescription: \"Collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.\",\n        socialMediaDashboard: \"Social Media Dashboard\",\n        socialDescription: \"Analytics dashboard for social media management with data visualization, scheduling features, and multi-platform integration.\",\n        aiChatBot: \"AI Chat Bot\",\n        aiChatBotDescription: \"Advanced AI-powered chatbot with natural language processing, real-time responses, and intuitive user interface.\",\n        cryptoTracker: \"Crypto Tracker\",\n        cryptoTrackerDescription: \"Comprehensive cryptocurrency tracking app with live charts, price alerts, and portfolio management.\",\n        weatherApp: \"Weather App\",\n        weatherAppDescription: \"Mobile weather application with accurate forecasts, interactive maps, and severe weather alerts.\",\n        liveDemo: \"Live Demo\",\n        code: \"Code\",\n        completed: \"Completed\",\n        inProgress: \"In Progress\",\n        // Contact Section\n        getInTouchTitle: \"Get In Touch\",\n        contactDescription: \"Ready to start your next project? Let's work together to create something amazing\",\n        letsConnect: \"Let's Connect\",\n        contactText: \"I'm always interested in hearing about new opportunities and exciting projects. Whether you're a company looking to hire, or you're a fellow developer who'd like to connect, I'd love to hear from you.\",\n        email: \"Email\",\n        phone: \"Phone\",\n        location: \"Location\",\n        remote: \"Remote / Open to Relocation\",\n        sendMessage: \"Send a Message\",\n        name: \"Name\",\n        yourName: \"Your Name\",\n        yourEmail: \"<EMAIL>\",\n        subject: \"Subject\",\n        projectDiscussion: \"Project Discussion\",\n        message: \"Message\",\n        messagePlaceholder: \"Tell me about your project...\",\n        sendMessageBtn: \"Send Message\",\n        saudiArabia: \"Saudi Arabia\",\n        availability: \"Availability\",\n        availableForWork: \"Available for Work\",\n        enterYourName: \"Enter your name\",\n        enterYourEmail: \"Enter your email\",\n        enterSubject: \"Enter subject\",\n        enterYourMessage: \"Enter your message\",\n        sending: \"Sending...\",\n        // Achievements Section\n        achievementsTitle: \"Achievements & Certifications\",\n        achievementsDescription: \"A journey of professional successes and accomplishments\",\n        topDeveloper: \"Top Developer of the Year\",\n        topDeveloperDesc: \"Awarded Top Developer in the company for 2024 based on performance and innovation\",\n        fullStackCertification: \"Full Stack Certification\",\n        fullStackCertificationDesc: \"Certified Full Stack Web Developer from Advanced Technology Academy\",\n        openSourceContributor: \"Open Source Contributor\",\n        openSourceContributorDesc: \"Active contributor to 20+ open source projects on GitHub\",\n        hackathonWinner: \"Hackathon Winner\",\n        hackathonWinnerDesc: \"First place winner in FinTech Hackathon 2023\",\n        clientSatisfaction: \"100% Client Satisfaction\",\n        clientSatisfactionDesc: \"Perfect rating from all clients with on-time project delivery record\",\n        teamLeadership: \"Team Leadership\",\n        teamLeadershipDesc: \"Successfully led a team of 8 developers across multiple projects\",\n        projectsDelivered: \"Projects Delivered\",\n        happyClients: \"Happy Clients\",\n        codeCommits: \"Code Commits\",\n        countriesServed: \"Countries Served\",\n        recognition: \"Recognition\",\n        certification: \"Certification\",\n        contribution: \"Contribution\",\n        competition: \"Competition\",\n        performance: \"Performance\",\n        leadership: \"Leadership\"\n    }\n};\nconst TranslationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst TranslationProvider = ({ children })=>{\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"en\");\n    // Load saved language from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedLanguage = localStorage.getItem(\"preferred-language\");\n        if (savedLanguage && (savedLanguage === \"ar\" || savedLanguage === \"en\")) {\n            setLanguage(savedLanguage);\n        }\n    }, []);\n    // Save language to localStorage when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        localStorage.setItem(\"preferred-language\", language);\n        document.documentElement.lang = language;\n    }, [\n        language\n    ]);\n    const t = (key)=>{\n        return translations[language][key] || key;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TranslationContext.Provider, {\n        value: {\n            language,\n            setLanguage,\n            t\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            dir: language === \"ar\" ? \"rtl\" : \"ltr\",\n            className: `${language === \"ar\" ? \"font-cairo\" : \"font-inter\"} text-transition`,\n            style: {\n                fontFamily: language === \"ar\" ? \"'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif\" : \"'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif\"\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\hooks\\\\useTranslation.tsx\",\n            lineNumber: 314,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\الملف الشخصي\\\\waleed_almshwly\\\\src\\\\hooks\\\\useTranslation.tsx\",\n        lineNumber: 313,\n        columnNumber: 5\n    }, undefined);\n};\nconst useTranslation = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TranslationContext);\n    if (!context) {\n        throw new Error(\"useTranslation must be used within TranslationProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./src/hooks/useTranslation.tsx\n");

/***/ }),

/***/ "./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3dhbGVlZC1hbG1zaHdseS1uZXh0anMvLi9zcmMvbGliL3V0aWxzLnRzPzdjMWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSBcImNsc3hcIlxyXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xyXG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcclxufVxyXG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/lib/utils.ts\n");

/***/ }),

/***/ "./src/index.css":
/*!***********************!*\
  !*** ./src/index.css ***!
  \***********************/
/***/ (() => {



/***/ }),

/***/ "next-themes":
/*!******************************!*\
  !*** external "next-themes" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-themes");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "@radix-ui/react-avatar":
/*!*****************************************!*\
  !*** external "@radix-ui/react-avatar" ***!
  \*****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-avatar");;

/***/ }),

/***/ "@radix-ui/react-slot":
/*!***************************************!*\
  !*** external "@radix-ui/react-slot" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-slot");;

/***/ }),

/***/ "@radix-ui/react-toast":
/*!****************************************!*\
  !*** external "@radix-ui/react-toast" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-toast");;

/***/ }),

/***/ "@radix-ui/react-tooltip":
/*!******************************************!*\
  !*** external "@radix-ui/react-tooltip" ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-tooltip");;

/***/ }),

/***/ "@tanstack/react-query":
/*!****************************************!*\
  !*** external "@tanstack/react-query" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@tanstack/react-query");;

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "sonner":
/*!*************************!*\
  !*** external "sonner" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = import("sonner");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();