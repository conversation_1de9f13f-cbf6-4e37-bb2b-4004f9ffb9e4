{"version": 3, "sources": ["../../src/lib/get-project-dir.ts"], "names": ["path", "error", "warn", "detectTypo", "realpathSync", "getProjectDir", "dir", "resolvedDir", "resolve", "realDir", "toLowerCase", "err", "code", "detectedTypo", "process", "exit"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,KAAK,EAAEC,IAAI,QAAQ,sBAAqB;AACjD,SAASC,UAAU,QAAQ,gBAAe;AAC1C,SAASC,YAAY,QAAQ,aAAY;AAEzC,OAAO,SAASC,cAAcC,GAAY;IACxC,IAAI;QACF,MAAMC,cAAcP,KAAKQ,OAAO,CAACF,OAAO;QACxC,MAAMG,UAAUL,aAAaG;QAE7B,IACEA,gBAAgBE,WAChBF,YAAYG,WAAW,OAAOD,QAAQC,WAAW,IACjD;YACAR,KACE,CAAC,kDAAkD,EAAEK,YAAY,aAAa,EAAEE,QAAQ,gFAAgF,CAAC;QAE7K;QAEA,OAAOA;IACT,EAAE,OAAOE,KAAU;QACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU;YACzB,IAAI,OAAON,QAAQ,UAAU;gBAC3B,MAAMO,eAAeV,WAAWG,KAAK;oBACnC;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,IAAIO,cAAc;oBAChBZ,MACE,CAAC,MAAM,EAAEK,IAAI,qCAAqC,EAAEO,aAAa,EAAE,CAAC;oBAEtEC,QAAQC,IAAI,CAAC;gBACf;YACF;YAEAd,MACE,CAAC,uDAAuD,EAAED,KAAKQ,OAAO,CACpEF,OAAO,KACP,CAAC;YAELQ,QAAQC,IAAI,CAAC;QACf;QACA,MAAMJ;IACR;AACF"}