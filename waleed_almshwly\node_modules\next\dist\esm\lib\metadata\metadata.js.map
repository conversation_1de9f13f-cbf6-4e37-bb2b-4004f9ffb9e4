{"version": 3, "sources": ["../../../src/lib/metadata/metadata.tsx"], "names": ["React", "AppleWebAppMeta", "FormatDetectionMeta", "ItunesMeta", "BasicMeta", "ViewportMeta", "VerificationMeta", "FacebookMeta", "AlternatesMetadata", "OpenGraphMetadata", "TwitterMetadata", "AppLinksMeta", "IconsMetadata", "resolveMetadata", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createDefaultMetadata", "createDefaultViewport", "isNotFoundError", "createMetadataContext", "urlPathname", "renderOpts", "pathname", "split", "trailingSlash", "isStandaloneMode", "nextConfigOutput", "createMetadataComponents", "tree", "query", "metadataContext", "getDynamicParamFromSegment", "appUsingSizeAdjustment", "errorType", "createDynamicallyTrackedSearchParams", "resolve", "metadataErrorResolving", "Promise", "res", "MetadataTree", "defaultMetadata", "defaultViewport", "metadata", "viewport", "error", "errorMetadataItem", "errorConvention", "undefined", "searchParams", "resolvedError", "resolvedMetadata", "resolvedViewport", "parentParams", "metadataItems", "notFoundMetadataError", "notFoundMetadata", "notFoundViewport", "elements", "alternates", "itunes", "facebook", "formatDetection", "verification", "appleWebApp", "openGraph", "twitter", "appLinks", "icons", "push", "meta", "name", "map", "el", "index", "cloneElement", "key", "MetadataOutlet"], "mappings": ";AAOA,OAAOA,WAAW,QAAO;AACzB,SACEC,eAAe,EACfC,mBAAmB,EACnBC,UAAU,EACVC,SAAS,EACTC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,QACP,mBAAkB;AACzB,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,QACP,uBAAsB;AAC7B,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,eAAe,QAAQ,qBAAoB;AACpD,SAASC,UAAU,QAAQ,kBAAiB;AAK5C,SACEC,qBAAqB,EACrBC,qBAAqB,QAChB,qBAAoB;AAC3B,SAASC,eAAe,QAAQ,oCAAmC;AAGnE,OAAO,SAASC,sBACdC,WAAmB,EACnBC,UAA0C;IAE1C,OAAO;QACLC,UAAUF,YAAYG,KAAK,CAAC,IAAI,CAAC,EAAE;QACnCC,eAAeH,WAAWG,aAAa;QACvCC,kBAAkBJ,WAAWK,gBAAgB,KAAK;IACpD;AACF;AAEA,+DAA+D;AAC/D,+DAA+D;AAC/D,sGAAsG;AACtG,0GAA0G;AAC1G,uEAAuE;AACvE,4EAA4E;AAC5E,OAAO,SAASC,yBAAyB,EACvCC,IAAI,EACJC,KAAK,EACLC,eAAe,EACfC,0BAA0B,EAC1BC,sBAAsB,EACtBC,SAAS,EACTC,oCAAoC,EAWrC;IACC,IAAIC;IACJ,8DAA8D;IAC9D,MAAMC,yBAAyB,IAAIC,QAA2B,CAACC;QAC7DH,UAAUG;IACZ;IAEA,eAAeC;QACb,MAAMC,kBAAkBxB;QACxB,MAAMyB,kBAAkBxB;QACxB,IAAIyB,WAAyCF;QAC7C,IAAIG,WAAyCF;QAC7C,IAAIG;QACJ,MAAMC,oBAAwC;YAAC;YAAM;YAAM;SAAK;QAChE,MAAMC,kBAAkBb,cAAc,aAAac,YAAYd;QAC/D,MAAMe,eAAed,qCAAqCL;QAE1D,MAAM,CAACoB,eAAeC,kBAAkBC,iBAAiB,GACvD,MAAMrC,gBAAgB;YACpBc;YACAwB,cAAc,CAAC;YACfC,eAAe,EAAE;YACjBR;YACAG;YACAjB;YACAe;YACAhB;QACF;QACF,IAAI,CAACmB,eAAe;YAClBN,WAAWQ;YACXT,WAAWQ;YACXf,QAAQY;QACV,OAAO;YACLH,QAAQK;YACR,gGAAgG;YAChG,sGAAsG;YACtG,0FAA0F;YAC1F,+FAA+F;YAC/F,IAAI,CAAChB,aAAaf,gBAAgB+B,gBAAgB;gBAChD,MAAM,CAACK,uBAAuBC,kBAAkBC,iBAAiB,GAC/D,MAAM1C,gBAAgB;oBACpBc;oBACAwB,cAAc,CAAC;oBACfC,eAAe,EAAE;oBACjBR;oBACAG;oBACAjB;oBACAe,iBAAiB;oBACjBhB;gBACF;gBACFa,WAAWa;gBACXd,WAAWa;gBACXX,QAAQU,yBAAyBV;YACnC;YACAT,QAAQS;QACV;QAEA,MAAMa,WAAW1C,WAAW;YAC1BT,aAAa;gBAAEqC,UAAUA;YAAS;YAClCtC,UAAU;gBAAEqC;YAAS;YACrBjC,mBAAmB;gBAAEiD,YAAYhB,SAASgB,UAAU;YAAC;YACrDtD,WAAW;gBAAEuD,QAAQjB,SAASiB,MAAM;YAAC;YACrCnD,aAAa;gBAAEoD,UAAUlB,SAASkB,QAAQ;YAAC;YAC3CzD,oBAAoB;gBAAE0D,iBAAiBnB,SAASmB,eAAe;YAAC;YAChEtD,iBAAiB;gBAAEuD,cAAcpB,SAASoB,YAAY;YAAC;YACvD5D,gBAAgB;gBAAE6D,aAAarB,SAASqB,WAAW;YAAC;YACpDrD,kBAAkB;gBAAEsD,WAAWtB,SAASsB,SAAS;YAAC;YAClDrD,gBAAgB;gBAAEsD,SAASvB,SAASuB,OAAO;YAAC;YAC5CrD,aAAa;gBAAEsD,UAAUxB,SAASwB,QAAQ;YAAC;YAC3CrD,cAAc;gBAAEsD,OAAOzB,SAASyB,KAAK;YAAC;SACvC;QAED,IAAInC,wBAAwByB,SAASW,IAAI,eAAC,KAACC;YAAKC,MAAK;;QAErD,qBACE;sBACGb,SAASc,GAAG,CAAC,CAACC,IAAIC;gBACjB,qBAAOxE,MAAMyE,YAAY,CAACF,IAA0B;oBAAEG,KAAKF;gBAAM;YACnE;;IAGN;IAEA,eAAeG;QACb,MAAMhC,QAAQ,MAAMR;QACpB,IAAIQ,OAAO;YACT,MAAMA;QACR;QACA,OAAO;IACT;IAEA,OAAO;QAACL;QAAcqC;KAAe;AACvC"}