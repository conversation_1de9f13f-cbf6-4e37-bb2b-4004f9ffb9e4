{"version": 3, "sources": ["../../src/client/on-recoverable-error.ts"], "names": ["onRecoverableError", "err", "defaultOnRecoverableError", "reportError", "error", "window", "console", "isBailoutToCSRError"], "mappings": ";;;;+BAEA;;;eAAwBA;;;8BAFY;AAErB,SAASA,mBAAmBC,GAAY;IACrD,yCAAyC;IACzC,2IAA2I;IAC3I,MAAMC,4BACJ,OAAOC,gBAAgB,aAEnB,0CAA0C;IAC1CA,cACA,CAACC;QACCC,OAAOC,OAAO,CAACF,KAAK,CAACA;IACvB;IAEN,6EAA6E;IAC7E,IAAIG,IAAAA,iCAAmB,EAACN,MAAM;IAE9BC,0BAA0BD;AAC5B"}