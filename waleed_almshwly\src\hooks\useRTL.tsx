import { useTranslation } from "./useTranslation";

export const useRTL = () => {
  const { language } = useTranslation();
  const isRTL = language === 'ar';

  // Helper function to get flex direction based on RTL
  const getFlexDirection = (reverse = false) => {
    if (isRTL) {
      return reverse ? 'flex-row' : 'flex-row-reverse';
    }
    return reverse ? 'flex-row-reverse' : 'flex-row';
  };

  // Helper function to get margin classes for icons
  const getIconMargin = (position: 'left' | 'right' = 'left') => {
    if (isRTL) {
      return position === 'left' ? 'ml-2' : 'mr-2';
    }
    return position === 'left' ? 'mr-2' : 'ml-2';
  };

  // Helper function to get text alignment
  const getTextAlign = () => {
    return isRTL ? 'text-right' : 'text-left';
  };

  // Helper function to get padding/margin for RTL
  const getRTLSpacing = (property: 'padding' | 'margin', side: 'left' | 'right', value: string) => {
    const oppositeSide = side === 'left' ? 'right' : 'left';
    const targetSide = isRTL ? oppositeSide : side;
    return `${property === 'padding' ? 'p' : 'm'}${targetSide[0]}-${value}`;
  };

  // Helper function for positioning
  const getPosition = (side: 'left' | 'right') => {
    return isRTL ? (side === 'left' ? 'right' : 'left') : side;
  };

  return {
    isRTL,
    getFlexDirection,
    getIconMargin,
    getTextAlign,
    getRTLSpacing,
    getPosition,
    // Common class combinations
    iconLeft: isRTL ? 'ml-2' : 'mr-2',
    iconRight: isRTL ? 'mr-2' : 'ml-2',
    flexRow: isRTL ? 'flex-row-reverse' : 'flex-row',
    flexRowReverse: isRTL ? 'flex-row' : 'flex-row-reverse',
    textAlign: isRTL ? 'text-right' : 'text-left',
    floatLeft: isRTL ? 'float-right' : 'float-left',
    floatRight: isRTL ? 'float-left' : 'float-right',
  };
};
