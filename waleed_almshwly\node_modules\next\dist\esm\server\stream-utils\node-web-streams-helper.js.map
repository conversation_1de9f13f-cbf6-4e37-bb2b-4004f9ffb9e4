{"version": 3, "sources": ["../../../src/server/stream-utils/node-web-streams-helper.ts"], "names": ["getTracer", "AppRenderSpan", "Detached<PERSON>romise", "scheduleImmediate", "atLeastOneTask", "ENCODED_TAGS", "indexOfUint8Array", "isEquivalentUint8Arrays", "removeFromUint8Array", "voidCatch", "encoder", "TextEncoder", "chainStreams", "streams", "length", "Error", "readable", "writable", "TransformStream", "promise", "pipeTo", "preventClose", "i", "nextStream", "then", "lastStream", "catch", "streamFromString", "str", "ReadableStream", "start", "controller", "enqueue", "encode", "close", "streamToString", "stream", "decoder", "TextDecoder", "fatal", "string", "chunk", "decode", "createBufferedTransformStream", "bufferedChunks", "bufferByteLength", "pending", "flush", "detached", "Uint8Array", "copiedBytes", "bufferedChunk", "set", "byteLength", "undefined", "resolve", "transform", "push", "createInsertedHTMLStream", "getServerInsertedHTML", "html", "renderToInitialFizzStream", "ReactDOMServer", "element", "streamOptions", "trace", "renderToReadableStream", "createHeadInsertionTransformStream", "insert", "inserted", "freezing", "hasBytes", "insertion", "encodedInsertion", "index", "CLOSED", "HEAD", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "flushed", "createMergedTransformStream", "pull", "donePulling", "startPulling", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "value", "read", "err", "error", "createMoveSuffixStream", "foundSuffix", "encodedSuffix", "before", "after", "createStripDocumentClosingTagsTransform", "BODY_AND_HTML", "BODY", "HTML", "createRootLayoutValidatorStream", "foundHtml", "foundBody", "OPENING", "missingTags", "JSON", "stringify", "chainTransformers", "transformers", "transformer", "pipeThrough", "continueFizzStream", "renderStream", "inlinedDataStream", "isStaticGeneration", "serverInsertedHTMLToHead", "validateRootLayout", "closeTag", "suffixUnclosed", "split", "allReady", "continueDynamicPrerender", "prerenderStream", "continueStaticP<PERSON><PERSON>", "continueDynamicHTMLResume", "continueDynamicDataResume"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,eAAe,QAAQ,6BAA4B;AAC5D,SAASC,iBAAiB,EAAEC,cAAc,QAAQ,sBAAqB;AACvE,SAASC,YAAY,QAAQ,gBAAe;AAC5C,SACEC,iBAAiB,EACjBC,uBAAuB,EACvBC,oBAAoB,QACf,uBAAsB;AAE7B,SAASC;AACP,iFAAiF;AACjF,uFAAuF;AACvF,mBAAmB;AACrB;AAMA,oDAAoD;AACpD,uEAAuE;AACvE,+BAA+B;AAC/B,MAAMC,UAAU,IAAIC;AAEpB,OAAO,SAASC,aACd,GAAGC,OAA4B;IAE/B,yFAAyF;IACzF,sCAAsC;IACtC,IAAIA,QAAQC,MAAM,KAAK,GAAG;QACxB,MAAM,IAAIC,MAAM;IAClB;IAEA,yEAAyE;IACzE,IAAIF,QAAQC,MAAM,KAAK,GAAG;QACxB,OAAOD,OAAO,CAAC,EAAE;IACnB;IAEA,MAAM,EAAEG,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;IAEnC,4EAA4E;IAC5E,mEAAmE;IACnE,IAAIC,UAAUN,OAAO,CAAC,EAAE,CAACO,MAAM,CAACH,UAAU;QAAEI,cAAc;IAAK;IAE/D,IAAIC,IAAI;IACR,MAAOA,IAAIT,QAAQC,MAAM,GAAG,GAAGQ,IAAK;QAClC,MAAMC,aAAaV,OAAO,CAACS,EAAE;QAC7BH,UAAUA,QAAQK,IAAI,CAAC,IACrBD,WAAWH,MAAM,CAACH,UAAU;gBAAEI,cAAc;YAAK;IAErD;IAEA,kFAAkF;IAClF,wEAAwE;IACxE,MAAMI,aAAaZ,OAAO,CAACS,EAAE;IAC7BH,UAAUA,QAAQK,IAAI,CAAC,IAAMC,WAAWL,MAAM,CAACH;IAE/C,0EAA0E;IAC1E,gDAAgD;IAChDE,QAAQO,KAAK,CAACjB;IAEd,OAAOO;AACT;AAEA,OAAO,SAASW,iBAAiBC,GAAW;IAC1C,OAAO,IAAIC,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACL;YAClCG,WAAWG,KAAK;QAClB;IACF;AACF;AAEA,OAAO,eAAeC,eACpBC,MAAkC;IAElC,MAAMC,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IACvD,IAAIC,SAAS;IAEb,uGAAuG;IACvG,WAAW,MAAMC,SAASL,OAAQ;QAChCI,UAAUH,QAAQK,MAAM,CAACD,OAAO;YAAEL,QAAQ;QAAK;IACjD;IAEAI,UAAUH,QAAQK,MAAM;IAExB,OAAOF;AACT;AAEA,OAAO,SAASG;IAId,IAAIC,iBAAoC,EAAE;IAC1C,IAAIC,mBAA2B;IAC/B,IAAIC;IAEJ,MAAMC,QAAQ,CAAChB;QACb,yDAAyD;QACzD,IAAIe,SAAS;QAEb,MAAME,WAAW,IAAI9C;QACrB4C,UAAUE;QAEV7C,kBAAkB;YAChB,IAAI;gBACF,MAAMsC,QAAQ,IAAIQ,WAAWJ;gBAC7B,IAAIK,cAAc;gBAClB,IAAK,IAAI5B,IAAI,GAAGA,IAAIsB,eAAe9B,MAAM,EAAEQ,IAAK;oBAC9C,MAAM6B,gBAAgBP,cAAc,CAACtB,EAAE;oBACvCmB,MAAMW,GAAG,CAACD,eAAeD;oBACzBA,eAAeC,cAAcE,UAAU;gBACzC;gBACA,qFAAqF;gBACrF,4EAA4E;gBAC5ET,eAAe9B,MAAM,GAAG;gBACxB+B,mBAAmB;gBACnBd,WAAWC,OAAO,CAACS;YACrB,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRK,UAAUQ;gBACVN,SAASO,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIrC,gBAAgB;QACzBsC,WAAUf,KAAK,EAAEV,UAAU;YACzB,kDAAkD;YAClDa,eAAea,IAAI,CAAChB;YACpBI,oBAAoBJ,MAAMY,UAAU;YAEpC,sCAAsC;YACtCN,MAAMhB;QACR;QACAgB;YACE,IAAI,CAACD,SAAS;YAEd,OAAOA,QAAQ3B,OAAO;QACxB;IACF;AACF;AAEA,SAASuC,yBACPC,qBAA4C;IAE5C,OAAO,IAAIzC,gBAAgB;QACzBsC,WAAW,OAAOf,OAAOV;YACvB,MAAM6B,OAAO,MAAMD;YACnB,IAAIC,MAAM;gBACR7B,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAAC2B;YACpC;YAEA7B,WAAWC,OAAO,CAACS;QACrB;IACF;AACF;AAEA,OAAO,SAASoB,0BAA0B,EACxCC,cAAc,EACdC,OAAO,EACPC,aAAa,EAKd;IACC,OAAOhE,YAAYiE,KAAK,CAAChE,cAAciE,sBAAsB,EAAE,UAC7DJ,eAAeI,sBAAsB,CAACH,SAASC;AAEnD;AAEA,SAASG,mCACPC,MAA6B;IAE7B,IAAIC,WAAW;IACf,IAAIC,WAAW;IAEf,wEAAwE;IACxE,iDAAiD;IACjD,IAAIC,WAAW;IAEf,OAAO,IAAIrD,gBAAgB;QACzB,MAAMsC,WAAUf,KAAK,EAAEV,UAAU;YAC/BwC,WAAW;YACX,4DAA4D;YAC5D,IAAID,UAAU;gBACZvC,WAAWC,OAAO,CAACS;gBACnB;YACF;YAEA,MAAM+B,YAAY,MAAMJ;YAExB,IAAIC,UAAU;gBACZ,IAAIG,WAAW;oBACb,MAAMC,mBAAmB/D,QAAQuB,MAAM,CAACuC;oBACxCzC,WAAWC,OAAO,CAACyC;gBACrB;gBACA1C,WAAWC,OAAO,CAACS;gBACnB6B,WAAW;YACb,OAAO;gBACL,0JAA0J;gBAC1J,MAAMI,QAAQpE,kBAAkBmC,OAAOpC,aAAasE,MAAM,CAACC,IAAI;gBAC/D,IAAIF,UAAU,CAAC,GAAG;oBAChB,IAAIF,WAAW;wBACb,MAAMC,mBAAmB/D,QAAQuB,MAAM,CAACuC;wBACxC,MAAMK,sBAAsB,IAAI5B,WAC9BR,MAAM3B,MAAM,GAAG2D,iBAAiB3D,MAAM;wBAExC+D,oBAAoBzB,GAAG,CAACX,MAAMqC,KAAK,CAAC,GAAGJ;wBACvCG,oBAAoBzB,GAAG,CAACqB,kBAAkBC;wBAC1CG,oBAAoBzB,GAAG,CACrBX,MAAMqC,KAAK,CAACJ,QACZA,QAAQD,iBAAiB3D,MAAM;wBAEjCiB,WAAWC,OAAO,CAAC6C;oBACrB,OAAO;wBACL9C,WAAWC,OAAO,CAACS;oBACrB;oBACA6B,WAAW;oBACXD,WAAW;gBACb;YACF;YAEA,IAAI,CAACA,UAAU;gBACbtC,WAAWC,OAAO,CAACS;YACrB,OAAO;gBACLtC,kBAAkB;oBAChBmE,WAAW;gBACb;YACF;QACF;QACA,MAAMvB,OAAMhB,UAAU;YACpB,gEAAgE;YAChE,IAAIwC,UAAU;gBACZ,MAAMC,YAAY,MAAMJ;gBACxB,IAAII,WAAW;oBACbzC,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACuC;gBACpC;YACF;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASO,2BACPC,MAAc;IAEd,IAAIC,UAAU;IACd,IAAInC;IAEJ,MAAMC,QAAQ,CAAChB;QACb,MAAMiB,WAAW,IAAI9C;QACrB4C,UAAUE;QAEV7C,kBAAkB;YAChB,IAAI;gBACF4B,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAAC+C;YACpC,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRlC,UAAUQ;gBACVN,SAASO,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIrC,gBAAgB;QACzBsC,WAAUf,KAAK,EAAEV,UAAU;YACzBA,WAAWC,OAAO,CAACS;YAEnB,wCAAwC;YACxC,IAAIwC,SAAS;YAEb,gCAAgC;YAChCA,UAAU;YACVlC,MAAMhB;QACR;QACAgB,OAAMhB,UAAU;YACd,IAAIe,SAAS,OAAOA,QAAQ3B,OAAO;YACnC,IAAI8D,SAAS;YAEb,aAAa;YACblD,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAAC+C;QACpC;IACF;AACF;AAEA,0EAA0E;AAC1E,0BAA0B;AAC1B,SAASE,4BACP9C,MAAkC;IAElC,IAAI+C,OAA6B;IACjC,IAAIC,cAAc;IAElB,eAAeC,aAAatD,UAA4C;QACtE,IAAIoD,MAAM;YACR;QACF;QAEA,MAAMG,SAASlD,OAAOmD,SAAS;QAE/B,wBAAwB;QACxB,gEAAgE;QAChE,qEAAqE;QACrE,uEAAuE;QACvE,8DAA8D;QAC9D,aAAa;QAEb,qEAAqE;QACrE,6EAA6E;QAC7E,gEAAgE;QAChE,MAAMnF;QAEN,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAEoF,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMH,OAAOI,IAAI;gBACzC,IAAIF,MAAM;oBACRJ,cAAc;oBACd;gBACF;gBAEArD,WAAWC,OAAO,CAACyD;YACrB;QACF,EAAE,OAAOE,KAAK;YACZ5D,WAAW6D,KAAK,CAACD;QACnB;IACF;IAEA,OAAO,IAAIzE,gBAAgB;QACzBsC,WAAUf,KAAK,EAAEV,UAAU;YACzBA,WAAWC,OAAO,CAACS;YAEnB,6DAA6D;YAC7D,IAAI,CAAC0C,MAAM;gBACTA,OAAOE,aAAatD;YACtB;QACF;QACAgB,OAAMhB,UAAU;YACd,IAAIqD,aAAa;gBACf;YACF;YACA,OAAOD,QAAQE,aAAatD;QAC9B;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS8D,uBACPb,MAAc;IAEd,IAAIc,cAAc;IAElB,MAAMC,gBAAgBrF,QAAQuB,MAAM,CAAC+C;IAErC,OAAO,IAAI9D,gBAAgB;QACzBsC,WAAUf,KAAK,EAAEV,UAAU;YACzB,IAAI+D,aAAa;gBACf,OAAO/D,WAAWC,OAAO,CAACS;YAC5B;YAEA,MAAMiC,QAAQpE,kBAAkBmC,OAAOsD;YACvC,IAAIrB,QAAQ,CAAC,GAAG;gBACdoB,cAAc;gBAEd,uEAAuE;gBACvE,2BAA2B;gBAC3B,IAAIrD,MAAM3B,MAAM,KAAKkE,OAAOlE,MAAM,EAAE;oBAClC;gBACF;gBAEA,wCAAwC;gBACxC,MAAMkF,SAASvD,MAAMqC,KAAK,CAAC,GAAGJ;gBAC9B3C,WAAWC,OAAO,CAACgE;gBAEnB,sEAAsE;gBACtE,qCAAqC;gBACrC,IAAIvD,MAAM3B,MAAM,GAAGkE,OAAOlE,MAAM,GAAG4D,OAAO;oBACxC,uCAAuC;oBACvC,MAAMuB,QAAQxD,MAAMqC,KAAK,CAACJ,QAAQM,OAAOlE,MAAM;oBAC/CiB,WAAWC,OAAO,CAACiE;gBACrB;YACF,OAAO;gBACLlE,WAAWC,OAAO,CAACS;YACrB;QACF;QACAM,OAAMhB,UAAU;YACd,uEAAuE;YACvE,mCAAmC;YACnCA,WAAWC,OAAO,CAAC+D;QACrB;IACF;AACF;AAEA,SAASG;IAIP,OAAO,IAAIhF,gBAAgB;QACzBsC,WAAUf,KAAK,EAAEV,UAAU;YACzB,6EAA6E;YAC7E,qFAAqF;YACrF,wFAAwF;YACxF,2FAA2F;YAC3F,sCAAsC;YACtC,IACExB,wBAAwBkC,OAAOpC,aAAasE,MAAM,CAACwB,aAAa,KAChE5F,wBAAwBkC,OAAOpC,aAAasE,MAAM,CAACyB,IAAI,KACvD7F,wBAAwBkC,OAAOpC,aAAasE,MAAM,CAAC0B,IAAI,GACvD;gBACA,4EAA4E;gBAC5E;YACF;YAEA,+EAA+E;YAC/E,wFAAwF;YACxF,sFAAsF;YACtF5D,QAAQjC,qBAAqBiC,OAAOpC,aAAasE,MAAM,CAACyB,IAAI;YAC5D3D,QAAQjC,qBAAqBiC,OAAOpC,aAAasE,MAAM,CAAC0B,IAAI;YAE5DtE,WAAWC,OAAO,CAACS;QACrB;IACF;AACF;AAEA;;;;CAIC,GACD,OAAO,SAAS6D;IAId,IAAIC,YAAY;IAChB,IAAIC,YAAY;IAChB,OAAO,IAAItF,gBAAgB;QACzB,MAAMsC,WAAUf,KAAK,EAAEV,UAAU;YAC/B,+DAA+D;YAC/D,IACE,CAACwE,aACDjG,kBAAkBmC,OAAOpC,aAAaoG,OAAO,CAACJ,IAAI,IAAI,CAAC,GACvD;gBACAE,YAAY;YACd;YAEA,IACE,CAACC,aACDlG,kBAAkBmC,OAAOpC,aAAaoG,OAAO,CAACL,IAAI,IAAI,CAAC,GACvD;gBACAI,YAAY;YACd;YAEAzE,WAAWC,OAAO,CAACS;QACrB;QACAM,OAAMhB,UAAU;YACd,MAAM2E,cAA6D,EAAE;YACrE,IAAI,CAACH,WAAWG,YAAYjD,IAAI,CAAC;YACjC,IAAI,CAAC+C,WAAWE,YAAYjD,IAAI,CAAC;YAEjC,IAAI,CAACiD,YAAY5F,MAAM,EAAE;YAEzBiB,WAAWC,OAAO,CAChBtB,QAAQuB,MAAM,CACZ,CAAC,6CAA6C,EAAE0E,KAAKC,SAAS,CAC5DF,aACA,SAAS,CAAC;QAGlB;IACF;AACF;AAEA,SAASG,kBACP7F,QAA2B,EAC3B8F,YAAyD;IAEzD,IAAI1E,SAASpB;IACb,KAAK,MAAM+F,eAAeD,aAAc;QACtC,IAAI,CAACC,aAAa;QAElB3E,SAASA,OAAO4E,WAAW,CAACD;IAC9B;IACA,OAAO3E;AACT;AAcA,OAAO,eAAe6E,mBACpBC,YAAiC,EACjC,EACElC,MAAM,EACNmC,iBAAiB,EACjBC,kBAAkB,EAClBzD,qBAAqB,EACrB0D,wBAAwB,EACxBC,kBAAkB,EACI;IAExB,MAAMC,WAAW;IAEjB,6EAA6E;IAC7E,MAAMC,iBAAiBxC,SAASA,OAAOyC,KAAK,CAACF,UAAU,EAAE,CAAC,EAAE,GAAG;IAE/D,2EAA2E;IAC3E,+DAA+D;IAC/D,IAAIH,sBAAsB,cAAcF,cAAc;QACpD,MAAMA,aAAaQ,QAAQ;IAC7B;IAEA,OAAOb,kBAAkBK,cAAc;QACrC,qDAAqD;QACrDvE;QAEA,gCAAgC;QAChCgB,yBAAyB,CAAC0D,2BACtB3D,yBAAyBC,yBACzB;QAEJ,wBAAwB;QACxB6D,kBAAkB,QAAQA,eAAe1G,MAAM,GAAG,IAC9CiE,2BAA2ByC,kBAC3B;QAEJ,+EAA+E;QAC/EL,oBAAoBjC,4BAA4BiC,qBAAqB;QAErE,yDAAyD;QACzDG,qBAAqBhB,oCAAoC;QAEzD,kDAAkD;QAClDT,uBAAuB0B;QAEvB,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/E5D,yBAAyB0D,2BACrBlD,mCAAmCR,yBACnC;KACL;AACH;AAMA,OAAO,eAAegE,yBACpBC,eAA2C,EAC3C,EAAEjE,qBAAqB,EAAmC;IAE1D,OACEiE,eACE,qDAAqD;KACpDZ,WAAW,CAACrE,iCACZqE,WAAW,CAACd,0CACb,gCAAgC;KAC/Bc,WAAW,CAAC7C,mCAAmCR;AAEtD;AAOA,OAAO,eAAekE,wBACpBD,eAA2C,EAC3C,EAAET,iBAAiB,EAAExD,qBAAqB,EAAkC;IAE5E,MAAM4D,WAAW;IAEjB,OACEK,eACE,qDAAqD;KACpDZ,WAAW,CAACrE,gCACb,gCAAgC;KAC/BqE,WAAW,CAAC7C,mCAAmCR,uBAChD,+EAA+E;KAC9EqD,WAAW,CAAC9B,4BAA4BiC,mBACzC,kDAAkD;KACjDH,WAAW,CAACnB,uBAAuB0B;AAE1C;AAOA,OAAO,eAAeO,0BACpBZ,YAAwC,EACxC,EAAEC,iBAAiB,EAAExD,qBAAqB,EAAyB;IAEnE,MAAM4D,WAAW;IAEjB,OACEL,YACE,qDAAqD;KACpDF,WAAW,CAACrE,gCACb,gCAAgC;KAC/BqE,WAAW,CAAC7C,mCAAmCR,uBAChD,+EAA+E;KAC9EqD,WAAW,CAAC9B,4BAA4BiC,mBACzC,kDAAkD;KACjDH,WAAW,CAACnB,uBAAuB0B;AAE1C;AAMA,OAAO,eAAeQ,0BACpBb,YAAwC,EACxC,EAAEC,iBAAiB,EAAoC;IAEvD,MAAMI,WAAW;IAEjB,OACEL,YACE,+EAA+E;KAC9EF,WAAW,CAAC9B,4BAA4BiC,mBACzC,kDAAkD;KACjDH,WAAW,CAACnB,uBAAuB0B;AAE1C"}