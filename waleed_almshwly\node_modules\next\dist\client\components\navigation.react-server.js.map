{"version": 3, "sources": ["../../../src/client/components/navigation.react-server.ts"], "names": ["ReadonlyURLSearchParams", "RedirectType", "notFound", "permanentRedirect", "redirect", "ReadonlyURLSearchParamsError", "Error", "constructor", "URLSearchParams", "append", "delete", "set", "sort"], "mappings": "AAAA,cAAc;;;;;;;;;;;;;;;;;;IA8BLA,uBAAuB;eAAvBA;;IAF6BC,YAAY;eAAZA,sBAAY;;IACzCC,QAAQ;eAARA,kBAAQ;;IADEC,iBAAiB;eAAjBA,2BAAiB;;IAA3BC,QAAQ;eAARA,kBAAQ;;;0BAAyC;0BACjC;AA5BzB,MAAMC,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAEA,MAAMP,gCAAgCQ;IACpC,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIJ;IACZ;IACA,wKAAwK,GACxKK,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,MAAM;QACJ,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,OAAO;QACL,MAAM,IAAIP;IACZ;AACF"}