{"version": 3, "sources": ["../../../../src/build/webpack/plugins/next-drop-client-page-plugin.ts"], "names": ["DropClientPage", "ampFirstEntryNamesMap", "WeakMap", "PLUGIN_NAME", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "findEntryModule", "mod", "queue", "Set", "module", "incomingConnections", "moduleGraph", "getIncomingConnections", "incomingConnection", "originModule", "add", "handler", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entryModule", "state", "buildInfo", "NEXT_ampFirst", "preDeclarator", "declarator", "id", "name", "STRING_LITERAL_DROP_BUNDLE", "for", "has", "set", "ampFirstEntryNamesItem", "get", "seal", "entryData", "entries", "dependency", "dependencies", "getModule", "push", "delete", "ampPages"], "mappings": ";;;;;;;;;;;;;;;IASaA,cAAc;eAAdA;;IANAC,qBAAqB;eAArBA;;;2BAF8B;AAEpC,MAAMA,wBACX,IAAIC;AAEN,MAAMC,cAAc;AAGb,MAAMH;IAGXI,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5BL,aACA,CAACI,aAAkB,EAAEE,mBAAmB,EAAO;YAC7C,6DAA6D;YAC7D,SAASC,gBAAgBC,GAAQ;gBAC/B,MAAMC,QAAQ,IAAIC,IAAI;oBAACF;iBAAI;gBAC3B,KAAK,MAAMG,WAAUF,MAAO;oBAC1B,MAAMG,sBACJR,YAAYS,WAAW,CAACC,sBAAsB,CAACH;oBAEjD,KAAK,MAAMI,sBAAsBH,oBAAqB;wBACpD,IAAI,CAACG,mBAAmBC,YAAY,EAAE,OAAOL;wBAC7CF,MAAMQ,GAAG,CAACF,mBAAmBC,YAAY;oBAC3C;gBACF;gBAEA,OAAO;YACT;YAEA,SAASE,QAAQC,MAAW;gBAC1B,SAASC;oBACP,MAAMC,cAAcd,gBAAgBY,OAAOG,KAAK,CAACX,MAAM;oBAEvD,IAAI,CAACU,aAAa;wBAChB;oBACF;oBAEA,wCAAwC;oBACxCA,YAAYE,SAAS,CAACC,aAAa,GAAG;gBACxC;gBAEAL,OAAOhB,KAAK,CAACsB,aAAa,CAACpB,GAAG,CAACL,aAAa,CAAC0B;wBACvCA;oBAAJ,IAAIA,CAAAA,+BAAAA,iBAAAA,WAAYC,EAAE,qBAAdD,eAAgBE,IAAI,MAAKC,qCAA0B,EAAE;wBACvDT;oBACF;gBACF;YACF;YAEAd,oBAAoBH,KAAK,CAACgB,MAAM,CAC7BW,GAAG,CAAC,mBACJzB,GAAG,CAACL,aAAakB;YAEpBZ,oBAAoBH,KAAK,CAACgB,MAAM,CAC7BW,GAAG,CAAC,kBACJzB,GAAG,CAACL,aAAakB;YAEpBZ,oBAAoBH,KAAK,CAACgB,MAAM,CAC7BW,GAAG,CAAC,sBACJzB,GAAG,CAACL,aAAakB;YAEpB,IAAI,CAACpB,sBAAsBiC,GAAG,CAAC3B,cAAc;gBAC3CN,sBAAsBkC,GAAG,CAAC5B,aAAa,EAAE;YAC3C;YAEA,MAAM6B,yBAAyBnC,sBAAsBoC,GAAG,CACtD9B;YAGFA,YAAYD,KAAK,CAACgC,IAAI,CAAC9B,GAAG,CAACL,aAAa;gBACtC,KAAK,MAAM,CAAC4B,MAAMQ,UAAU,IAAIhC,YAAYiC,OAAO,CAAE;oBACnD,KAAK,MAAMC,cAAcF,UAAUG,YAAY,CAAE;4BAE3C5B;wBADJ,MAAMA,UAASP,YAAYS,WAAW,CAAC2B,SAAS,CAACF;wBACjD,IAAI3B,4BAAAA,oBAAAA,QAAQY,SAAS,qBAAjBZ,kBAAmBa,aAAa,EAAE;4BACpCS,uBAAuBQ,IAAI,CAACb;4BAC5BxB,YAAYiC,OAAO,CAACK,MAAM,CAACd;wBAC7B;oBACF;gBACF;YACF;QACF;IAEJ;;aA1EAe,WAAW,IAAIjC;;AA2EjB"}