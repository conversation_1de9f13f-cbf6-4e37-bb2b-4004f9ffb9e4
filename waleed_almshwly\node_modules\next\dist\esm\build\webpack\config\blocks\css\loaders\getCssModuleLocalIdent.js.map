{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/getCssModuleLocalIdent.ts"], "names": ["loaderUtils", "path", "regexLikeIndexModule", "getCssModuleLocalIdent", "context", "_", "exportName", "options", "relativePath", "relative", "rootContext", "resourcePath", "replace", "fileNameOrFolder", "test", "hash", "getHashDigest", "<PERSON><PERSON><PERSON>", "from", "interpolateName"], "mappings": "AAAA,OAAOA,iBAAiB,mCAAkC;AAC1D,OAAOC,UAAU,OAAM;AAGvB,MAAMC,uBAAuB;AAE7B,OAAO,SAASC,uBACdC,OAAkC,EAClCC,CAAM,EACNC,UAAkB,EAClBC,OAAe;IAEf,MAAMC,eAAeP,KAClBQ,QAAQ,CAACL,QAAQM,WAAW,EAAEN,QAAQO,YAAY,EAClDC,OAAO,CAAC,QAAQ;IAEnB,0EAA0E;IAC1E,2BAA2B;IAC3B,MAAMC,mBAAmBX,qBAAqBY,IAAI,CAACN,gBAC/C,aACA;IAEJ,iDAAiD;IACjD,MAAMO,OAAOf,YAAYgB,aAAa,CACpCC,OAAOC,IAAI,CAAC,CAAC,SAAS,EAAEV,aAAa,WAAW,EAAEF,WAAW,CAAC,GAC9D,QACA,UACA;IAGF,yEAAyE;IACzE,OACEN,YACGmB,eAAe,CACdf,SACAS,mBAAmB,MAAMP,aAAa,OAAOS,MAC7CR,SAEDK,OAAO,CACN,oEAAoE;IACpE,oEAAoE;IACpE,QAAQ;IACR,aACA,IAEF,+DAA+D;IAC/D,iEAAiE;KAChEA,OAAO,CAAC,mBAAmB,IAC5B,uFAAuF;IACvF,sDAAsD;KACrDA,OAAO,CAAC,gBAAgB;AAE/B"}