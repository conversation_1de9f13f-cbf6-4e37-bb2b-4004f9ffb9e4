{"version": 3, "sources": ["../../../src/server/response-cache/index.ts"], "names": ["RouteKind", "<PERSON><PERSON>", "scheduleOnNextTick", "fromResponseCacheEntry", "toResponseCacheEntry", "ResponseCache", "constructor", "minimalMode", "batcher", "create", "cacheKeyFn", "key", "isOnDemandRevalidate", "schedulerFn", "minimalModeKey", "get", "responseGenerator", "context", "incrementalCache", "response", "batch", "cache<PERSON>ey", "resolve", "previousCacheItem", "expiresAt", "Date", "now", "entry", "kindHint", "routeKind", "APP_PAGE", "APP_ROUTE", "PAGES", "resolved", "cachedResponse", "value", "kind", "Error", "revalidate", "curRevalidate", "isStale", "isPrefetch", "cacheEntry", "undefined", "resolveValue", "isMiss", "set", "err", "Math", "min", "max", "console", "error"], "mappings": "AAQA,SAASA,SAAS,QAAQ,uBAAsB;AAEhD,SAASC,OAAO,QAAQ,oBAAmB;AAC3C,SAASC,kBAAkB,QAAQ,sBAAqB;AACxD,SAASC,sBAAsB,EAAEC,oBAAoB,QAAQ,UAAS;AAEtE,cAAc,UAAS;AAEvB,eAAe,MAAMC;IAwBnBC,YAAYC,WAAoB,CAAE;aAvBjBC,UAAUP,QAAQQ,MAAM,CAIvC;YACA,0EAA0E;YAC1E,4EAA4E;YAC5EC,YAAY,CAAC,EAAEC,GAAG,EAAEC,oBAAoB,EAAE,GACxC,CAAC,EAAED,IAAI,CAAC,EAAEC,uBAAuB,MAAM,IAAI,CAAC;YAC9C,sEAAsE;YACtE,uEAAuE;YACvE,oDAAoD;YACpDC,aAAaX;QACf;QAWE,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMY,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAAGP;IACzB;IAEA,MAAaQ,IACXJ,GAAkB,EAClBK,iBAAoC,EACpCC,OAKC,EACmC;QACpC,0EAA0E;QAC1E,6DAA6D;QAC7D,IAAI,CAACN,KAAK,OAAOK,kBAAkB,OAAO;QAE1C,MAAM,EAAEE,gBAAgB,EAAEN,uBAAuB,KAAK,EAAE,GAAGK;QAE3D,MAAME,WAAW,MAAM,IAAI,CAACX,OAAO,CAACY,KAAK,CACvC;YAAET;YAAKC;QAAqB,GAC5B,OAAOS,UAAUC;gBAKb;YAJF,+DAA+D;YAC/D,iDAAiD;YACjD,IACE,IAAI,CAACf,WAAW,IAChB,EAAA,0BAAA,IAAI,CAACgB,iBAAiB,qBAAtB,wBAAwBZ,GAAG,MAAKU,YAChC,IAAI,CAACE,iBAAiB,CAACC,SAAS,GAAGC,KAAKC,GAAG,IAC3C;gBACA,OAAO,IAAI,CAACH,iBAAiB,CAACI,KAAK;YACrC;YAEA,mEAAmE;YACnE,IAAIC;YACJ,IACEX,QAAQY,SAAS,KAAK7B,UAAU8B,QAAQ,IACxCb,QAAQY,SAAS,KAAK7B,UAAU+B,SAAS,EACzC;gBACAH,WAAW;YACb,OAAO,IAAIX,QAAQY,SAAS,KAAK7B,UAAUgC,KAAK,EAAE;gBAChDJ,WAAW;YACb;YAEA,IAAIK,WAAW;YACf,IAAIC,iBAAuC;YAC3C,IAAI;gBACFA,iBAAiB,CAAC,IAAI,CAAC3B,WAAW,GAC9B,MAAMW,iBAAiBH,GAAG,CAACJ,KAAK;oBAAEiB;gBAAS,KAC3C;gBAEJ,IAAIM,kBAAkB,CAACtB,sBAAsB;wBACvCsB;oBAAJ,IAAIA,EAAAA,wBAAAA,eAAeC,KAAK,qBAApBD,sBAAsBE,IAAI,MAAK,SAAS;wBAC1C,MAAM,IAAIC,MACR,CAAC,oEAAoE,CAAC;oBAE1E;oBAEAf,QAAQ;wBACN,GAAGY,cAAc;wBACjBI,YAAYJ,eAAeK,aAAa;oBAC1C;oBACAN,WAAW;oBAEX,IAAI,CAACC,eAAeM,OAAO,IAAIvB,QAAQwB,UAAU,EAAE;wBACjD,oDAAoD;wBACpD,oBAAoB;wBACpB,OAAO;oBACT;gBACF;gBAEA,MAAMC,aAAa,MAAM1B,kBACvBiB,UACAC,gBACA;gBAGF,mEAAmE;gBACnE,cAAc;gBACd,IAAI,CAACQ,YAAY;oBACf,+CAA+C;oBAC/C,IAAI,IAAI,CAACnC,WAAW,EAAE,IAAI,CAACgB,iBAAiB,GAAGoB;oBAC/C,OAAO;gBACT;gBAEA,MAAMC,eAAe,MAAMzC,uBAAuB;oBAChD,GAAGuC,UAAU;oBACbG,QAAQ,CAACX;gBACX;gBACA,IAAI,CAACU,cAAc;oBACjB,+CAA+C;oBAC/C,IAAI,IAAI,CAACrC,WAAW,EAAE,IAAI,CAACgB,iBAAiB,GAAGoB;oBAC/C,OAAO;gBACT;gBAEA,+DAA+D;gBAC/D,yBAAyB;gBACzB,IAAI,CAAC/B,wBAAwB,CAACqB,UAAU;oBACtCX,QAAQsB;oBACRX,WAAW;gBACb;gBAEA,IAAI,OAAOW,aAAaN,UAAU,KAAK,aAAa;oBAClD,IAAI,IAAI,CAAC/B,WAAW,EAAE;wBACpB,IAAI,CAACgB,iBAAiB,GAAG;4BACvBZ,KAAKU;4BACLM,OAAOiB;4BACPpB,WAAWC,KAAKC,GAAG,KAAK;wBAC1B;oBACF,OAAO;wBACL,MAAMR,iBAAiB4B,GAAG,CAACnC,KAAKiC,aAAaT,KAAK,EAAE;4BAClDG,YAAYM,aAAaN,UAAU;wBACrC;oBACF;gBACF;gBAEA,OAAOM;YACT,EAAE,OAAOG,KAAK;gBACZ,qEAAqE;gBACrE,sEAAsE;gBACtE,IAAIb,gBAAgB;oBAClB,MAAMhB,iBAAiB4B,GAAG,CAACnC,KAAKuB,eAAeC,KAAK,EAAE;wBACpDG,YAAYU,KAAKC,GAAG,CAClBD,KAAKE,GAAG,CAAChB,eAAeI,UAAU,IAAI,GAAG,IACzC;oBAEJ;gBACF;gBAEA,qEAAqE;gBACrE,kDAAkD;gBAClD,IAAIL,UAAU;oBACZkB,QAAQC,KAAK,CAACL;oBACd,OAAO;gBACT;gBAEA,gEAAgE;gBAChE,MAAMA;YACR;QACF;QAGF,OAAO3C,qBAAqBe;IAC9B;AACF"}