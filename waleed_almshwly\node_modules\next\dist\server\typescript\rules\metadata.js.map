{"version": 3, "sources": ["../../../../src/server/typescript/rules/metadata.ts"], "names": ["TYPE_ANOTATION", "TYPE_ANOTATION_ASYNC", "TYPE_IMPORT", "getMetadataExport", "fileName", "position", "source", "getSource", "metadataExport", "ts", "getTs", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "visit", "node", "isPositionInsideNode", "isVariableStatement", "modifiers", "some", "m", "kind", "SyntaxKind", "ExportKeyword", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "name", "getText", "cachedProxiedLanguageService", "cachedProxiedLanguageServiceHost", "getProxiedLanguageService", "languageService", "languageServiceHost", "getInfo", "ProxiedLanguageServiceHost", "getScriptFileNames", "names", "Set", "files", "hasOwnProperty", "add", "file", "addFile", "body", "snap", "ScriptSnapshot", "fromString", "getChangeRange", "_", "undefined", "existing", "ver", "readFile", "<PERSON><PERSON><PERSON><PERSON>", "fileExists", "log", "trace", "error", "getCompilationSettings", "getScriptIsOpen", "getCurrentDirectory", "getDefaultLibFileName", "o", "getScriptVersion", "toString", "getScriptSnapshot", "createLanguageService", "createDocumentRegistry", "updateVirtualFileWithType", "isGenerateMetadata", "sourceText", "getFullText", "nodeEnd", "annotation", "isFunctionDeclaration", "getFullStart", "isAsync", "AsyncKeyword", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "newSource", "slice", "length", "isTyped", "type", "proxyDiagnostics", "pos", "n", "diagnostics", "getSemanticDiagnostics", "filter", "d", "start", "map", "category", "code", "messageText", "metadata", "filterCompletionsAtPosition", "_options", "prior", "newPos", "completions", "getCompletionsAtPosition", "isIncomplete", "entries", "e", "ScriptElementKind", "memberVariableElement", "typeElement", "string", "includes", "insertText", "test", "kindModifiers", "sortText", "labelDetails", "description", "data", "getSemanticDiagnosticsForExportVariableStatementInClientEntry", "DiagnosticCategory", "Error", "NEXT_TS_ERRORS", "INVALID_METADATA_EXPORT", "getStart", "getWidth", "getSemanticDiagnosticsForExportVariableStatement", "getSemanticDiagnosticsForExportDeclarationInClientEntry", "exportClause", "isNamedExports", "elements", "push", "getSemanticDiagnosticsForExportDeclaration", "typeC<PERSON>cker", "getType<PERSON><PERSON>cker", "symbol", "getSymbolAtLocation", "metadataSymbol", "getAliasedSymbol", "isVariableDeclaration", "declarationFileName", "getSourceFile", "isSameFile", "getCompletionEntryDetails", "entryName", "formatOptions", "preferences", "details", "getQuickInfoAtPosition", "insight", "getDefinitionAndBoundSpan", "definitionInfoAndBoundSpan", "textSpan"], "mappings": ";;;;+BAygBA;;;eAAA;;;0BAzgB+B;uBAOxB;AAIP,MAAMA,iBAAiB;AACvB,MAAMC,uBAAuB;AAC7B,MAAMC,cAAc,CAAC,wCAAwC,CAAC;AAE9D,+CAA+C;AAC/C,SAASC,kBAAkBC,QAAgB,EAAEC,QAAgB;IAC3D,MAAMC,SAASC,IAAAA,gBAAS,EAACH;IACzB,IAAII;IAEJ,IAAIF,QAAQ;QACV,MAAMG,KAAKC,IAAAA,YAAK;QAChBD,GAAGE,YAAY,CAACL,QAAQ,SAASM,MAAMC,IAAI;YACzC,IAAIL,gBAAgB;YAEpB,uBAAuB;YACvB,IAAIM,IAAAA,2BAAoB,EAACT,UAAUQ,OAAO;oBAItCA;gBAHF,kBAAkB;gBAClB,IACEJ,GAAGM,mBAAmB,CAACF,WACvBA,kBAAAA,KAAKG,SAAS,qBAAdH,gBAAgBI,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKV,GAAGW,UAAU,CAACC,aAAa,IAClE;oBACA,IAAIZ,GAAGa,yBAAyB,CAACT,KAAKU,eAAe,GAAG;wBACtD,KAAK,MAAMC,eAAeX,KAAKU,eAAe,CAACE,YAAY,CAAE;4BAC3D,IACEX,IAAAA,2BAAoB,EAACT,UAAUmB,gBAC/BA,YAAYE,IAAI,CAACC,OAAO,OAAO,YAC/B;gCACA,gCAAgC;gCAChCnB,iBAAiBgB;gCACjB;4BACF;wBACF;oBACF;gBACF;YACF;QACF;IACF;IACA,OAAOhB;AACT;AAEA,IAAIoB;AACJ,IAAIC;AACJ,SAASC;IACP,IAAIF,8BACF,OAAO;QACLG,iBAAiBH;QACjBI,qBACEH;IAGJ;IAEF,MAAMG,sBAAsBC,IAAAA,cAAO,IAAGD,mBAAmB;IAEzD,MAAMvB,KAAKC,IAAAA,YAAK;IAChB,MAAMwB;QA0BJC,qBAA+B;YAC7B,MAAMC,QAAqB,IAAIC;YAC/B,IAAK,IAAIX,QAAQ,IAAI,CAACY,KAAK,CAAE;gBAC3B,IAAI,IAAI,CAACA,KAAK,CAACC,cAAc,CAACb,OAAO;oBACnCU,MAAMI,GAAG,CAACd;gBACZ;YACF;YACA,MAAMY,QAAQN,oBAAoBG,kBAAkB;YACpD,KAAK,MAAMM,QAAQH,MAAO;gBACxBF,MAAMI,GAAG,CAACC;YACZ;YACA,OAAO;mBAAIL;aAAM;QACnB;QAEAM,QAAQtC,QAAgB,EAAEuC,IAAY,EAAE;YACtC,MAAMC,OAAOnC,GAAGoC,cAAc,CAACC,UAAU,CAACH;YAC1CC,KAAKG,cAAc,GAAG,CAACC,IAAMC;YAC7B,MAAMC,WAAW,IAAI,CAACZ,KAAK,CAAClC,SAAS;YACrC,IAAI8C,UAAU;gBACZ,IAAI,CAACZ,KAAK,CAAClC,SAAS,CAAC+C,GAAG;gBACxB,IAAI,CAACb,KAAK,CAAClC,SAAS,CAACqC,IAAI,GAAGG;YAC9B,OAAO;gBACL,IAAI,CAACN,KAAK,CAAClC,SAAS,GAAG;oBAAE+C,KAAK;oBAAGV,MAAMG;gBAAK;YAC9C;QACF;QAEAQ,SAAShD,QAAgB,EAAE;YACzB,MAAMqC,OAAO,IAAI,CAACH,KAAK,CAAClC,SAAS;YACjC,OAAOqC,OACHA,KAAKA,IAAI,CAACd,OAAO,CAAC,GAAGc,KAAKA,IAAI,CAACY,SAAS,MACxCrB,oBAAoBoB,QAAQ,CAAChD;QACnC;QACAkD,WAAWlD,QAAgB,EAAE;YAC3B,OACE,IAAI,CAACkC,KAAK,CAAClC,SAAS,KAAK6C,aACzBjB,oBAAoBsB,UAAU,CAAClD;QAEnC;;iBA9DAkC,QAEI,CAAC;iBAELiB,MAAM,KAAO;iBACbC,QAAQ,KAAO;iBACfC,QAAQ,KAAO;iBACfC,yBAAyB,IAAM1B,oBAAoB0B,sBAAsB;iBACzEC,kBAAkB,IAAM;iBACxBC,sBAAsB,IAAM5B,oBAAoB4B,mBAAmB;iBACnEC,wBAAwB,CAACC,IACvB9B,oBAAoB6B,qBAAqB,CAACC;iBAE5CC,mBAAmB,CAAC3D;gBAClB,MAAMqC,OAAO,IAAI,CAACH,KAAK,CAAClC,SAAS;gBACjC,IAAI,CAACqC,MAAM,OAAOT,oBAAoB+B,gBAAgB,CAAC3D;gBACvD,OAAOqC,KAAKU,GAAG,CAACa,QAAQ;YAC1B;iBAEAC,oBAAoB,CAAC7D;gBACnB,MAAMqC,OAAO,IAAI,CAACH,KAAK,CAAClC,SAAS;gBACjC,IAAI,CAACqC,MAAM,OAAOT,oBAAoBiC,iBAAiB,CAAC7D;gBACxD,OAAOqC,KAAKA,IAAI;YAClB;;IAwCF;IAEAZ,mCAAmC,IAAIK;IACvCN,+BAA+BnB,GAAGyD,qBAAqB,CACrDrC,kCACApB,GAAG0D,sBAAsB;IAE3B,OAAO;QACLpC,iBAAiBH;QACjBI,qBACEH;IAGJ;AACF;AAEA,SAASuC,0BACPhE,QAAgB,EAChBS,IAAiE,EACjEwD,kBAA4B;IAE5B,MAAM/D,SAASC,IAAAA,gBAAS,EAACH;IACzB,IAAI,CAACE,QAAQ;IAEb,0DAA0D;IAC1D,MAAMgE,aAAahE,OAAOiE,WAAW;IACrC,IAAIC;IACJ,IAAIC;IAEJ,MAAMhE,KAAKC,IAAAA,YAAK;IAChB,IAAID,GAAGiE,qBAAqB,CAAC7D,OAAO;QAClC,IAAIwD,oBAAoB;gBAENxD;YADhB2D,UAAU3D,KAAK8B,IAAI,CAAEgC,YAAY;YACjC,MAAMC,WAAU/D,kBAAAA,KAAKG,SAAS,qBAAdH,gBAAgBI,IAAI,CAClC,CAACC,IAAMA,EAAEC,IAAI,KAAKV,GAAGW,UAAU,CAACyD,YAAY;YAE9CJ,aAAaG,UAAU3E,uBAAuBD;QAChD,OAAO;YACL;QACF;IACF,OAAO;QACLwE,UAAU3D,KAAKa,IAAI,CAACiD,YAAY,KAAK9D,KAAKa,IAAI,CAACoD,YAAY;QAC3DL,aAAazE;IACf;IAEA,MAAM+E,YACJT,WAAWU,KAAK,CAAC,GAAGR,WACpBC,aACAH,WAAWU,KAAK,CAACR,WACjBtE;IACF,MAAM,EAAE8B,mBAAmB,EAAE,GAAGF;IAChCE,oBAAoBU,OAAO,CAACtC,UAAU2E;IAEtC,OAAO;QAACP;QAASC,WAAWQ,MAAM;KAAC;AACrC;AAEA,SAASC,QACPrE,IAAiE;IAEjE,OAAOA,KAAKsE,IAAI,KAAKlC;AACvB;AAEA,SAASmC,iBACPhF,QAAgB,EAChBiF,GAAa,EACbC,CAA8D;IAE9D,kBAAkB;IAClB,MAAM,EAAEvD,eAAe,EAAE,GAAGD;IAC5B,MAAMyD,cAAcxD,gBAAgByD,sBAAsB,CAACpF;IAC3D,MAAME,SAASC,IAAAA,gBAAS,EAACH;IAEzB,6BAA6B;IAC7B,OAAOmF,YACJE,MAAM,CAAC,CAACC;QACP,IAAIA,EAAEC,KAAK,KAAK1C,aAAayC,EAAET,MAAM,KAAKhC,WAAW,OAAO;QAC5D,IAAIyC,EAAEC,KAAK,GAAGL,EAAEX,YAAY,IAAI,OAAO;QACvC,IAAIe,EAAEC,KAAK,GAAGD,EAAET,MAAM,IAAIK,EAAEX,YAAY,KAAKW,EAAER,YAAY,KAAKO,GAAG,CAAC,EAAE,EACpE,OAAO;QACT,OAAO;IACT,GACCO,GAAG,CAAC,CAACF;QACJ,OAAO;YACLjD,MAAMnC;YACNuF,UAAUH,EAAEG,QAAQ;YACpBC,MAAMJ,EAAEI,IAAI;YACZC,aAAaL,EAAEK,WAAW;YAC1BJ,OAAOD,EAAEC,KAAK,GAAIN,GAAG,CAAC,EAAE,GAAGK,EAAEC,KAAK,GAAGD,EAAEC,KAAK,GAAIN,GAAG,CAAC,EAAE;YACtDJ,QAAQS,EAAET,MAAM;QAClB;IACF;AACJ;AAEA,MAAMe,WAAW;IACfC,6BACE7F,QAAgB,EAChBC,QAAgB,EAChB6F,QAAa,EACbC,KAAqD;QAErD,MAAMtF,OAAOV,kBAAkBC,UAAUC;QACzC,IAAI,CAACQ,MAAM,OAAOsF;QAClB,IAAIjB,QAAQrE,OAAO,OAAOsF;QAE1B,MAAM1F,KAAKC,IAAAA,YAAK;QAEhB,0DAA0D;QAC1D,MAAM2E,MAAMjB,0BAA0BhE,UAAUS;QAChD,IAAIwE,QAAQpC,WAAW,OAAOkD;QAE9B,kBAAkB;QAClB,MAAM,EAAEpE,eAAe,EAAE,GAAGD;QAC5B,MAAMsE,SAAS/F,YAAYgF,GAAG,CAAC,EAAE,GAAGhF,WAAWA,WAAWgF,GAAG,CAAC,EAAE;QAChE,MAAMgB,cAActE,gBAAgBuE,wBAAwB,CAC1DlG,UACAgG,QACAnD;QAGF,IAAIoD,aAAa;YACfA,YAAYE,YAAY,GAAG;YAE3BF,YAAYG,OAAO,GAAGH,YAAYG,OAAO,CACtCf,MAAM,CAAC,CAACgB;gBACP,OAAO;oBACLhG,GAAGiG,iBAAiB,CAACC,qBAAqB;oBAC1ClG,GAAGiG,iBAAiB,CAACE,WAAW;oBAChCnG,GAAGiG,iBAAiB,CAACG,MAAM;iBAC5B,CAACC,QAAQ,CAACL,EAAEtF,IAAI;YACnB,GACCyE,GAAG,CAAC,CAACa;gBACJ,MAAMM,aACJN,EAAEtF,IAAI,KAAKV,GAAGiG,iBAAiB,CAACC,qBAAqB,IACrD,kBAAkBK,IAAI,CAACP,EAAE/E,IAAI,IACzB+E,EAAE/E,IAAI,GAAG,OACT+E,EAAE/E,IAAI;gBAEZ,OAAO;oBACLA,MAAM+E,EAAE/E,IAAI;oBACZqF;oBACA5F,MAAMsF,EAAEtF,IAAI;oBACZ8F,eAAeR,EAAEQ,aAAa;oBAC9BC,UAAU,MAAMT,EAAE/E,IAAI;oBACtByF,cAAc;wBACZC,aAAa,CAAC,gBAAgB,CAAC;oBACjC;oBACAC,MAAMZ,EAAEY,IAAI;gBACd;YACF;YAEF,OAAOhB;QACT;QAEA,OAAOF;IACT;IAEAmB,+DACElH,QAAgB,EAChBS,IAA+D;QAE/D,MAAMP,SAASC,IAAAA,gBAAS,EAACH;QACzB,MAAMK,KAAKC,IAAAA,YAAK;QAEhB,+EAA+E;QAC/E,IAAID,GAAGiE,qBAAqB,CAAC7D,OAAO;gBAC9BA;YAAJ,IAAIA,EAAAA,aAAAA,KAAKa,IAAI,qBAATb,WAAWc,OAAO,QAAO,oBAAoB;gBAC/C,OAAO;oBACL;wBACEc,MAAMnC;wBACNuF,UAAUpF,GAAG8G,kBAAkB,CAACC,KAAK;wBACrC1B,MAAM2B,wBAAc,CAACC,uBAAuB;wBAC5C3B,aAAa,CAAC,wEAAwE,CAAC;wBACvFJ,OAAO9E,KAAKa,IAAI,CAACiG,QAAQ;wBACzB1C,QAAQpE,KAAKa,IAAI,CAACkG,QAAQ;oBAC5B;iBACD;YACH;QACF,OAAO;YACL,KAAK,MAAMpG,eAAeX,KAAKU,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAMC,OAAOF,YAAYE,IAAI,CAACC,OAAO;gBACrC,IAAID,SAAS,YAAY;oBACvB,OAAO;wBACL;4BACEe,MAAMnC;4BACNuF,UAAUpF,GAAG8G,kBAAkB,CAACC,KAAK;4BACrC1B,MAAM2B,wBAAc,CAACC,uBAAuB;4BAC5C3B,aAAa,CAAC,gEAAgE,CAAC;4BAC/EJ,OAAOnE,YAAYE,IAAI,CAACiG,QAAQ;4BAChC1C,QAAQzD,YAAYE,IAAI,CAACkG,QAAQ;wBACnC;qBACD;gBACH;YACF;QACF;QACA,OAAO,EAAE;IACX;IAEAC,kDACEzH,QAAgB,EAChBS,IAA+D;QAE/D,MAAMJ,KAAKC,IAAAA,YAAK;QAEhB,IAAID,GAAGiE,qBAAqB,CAAC7D,OAAO;gBAC9BA;YAAJ,IAAIA,EAAAA,aAAAA,KAAKa,IAAI,qBAATb,WAAWc,OAAO,QAAO,oBAAoB;gBAC/C,IAAIuD,QAAQrE,OAAO,OAAO,EAAE;gBAE5B,0DAA0D;gBAC1D,MAAMwE,MAAMjB,0BAA0BhE,UAAUS,MAAM;gBACtD,IAAI,CAACwE,KAAK,OAAO,EAAE;gBAEnB,OAAOD,iBAAiBhF,UAAUiF,KAAKxE;YACzC;QACF,OAAO;YACL,KAAK,MAAMW,eAAeX,KAAKU,eAAe,CAACE,YAAY,CAAE;gBAC3D,IAAID,YAAYE,IAAI,CAACC,OAAO,OAAO,YAAY;oBAC7C,IAAIuD,QAAQ1D,cAAc;oBAE1B,0DAA0D;oBAC1D,MAAM6D,MAAMjB,0BAA0BhE,UAAUoB;oBAChD,IAAI,CAAC6D,KAAK;oBAEV,OAAOD,iBAAiBhF,UAAUiF,KAAK7D;gBACzC;YACF;QACF;QACA,OAAO,EAAE;IACX;IAEAsG,yDACE1H,QAAgB,EAChBS,IAAgC;QAEhC,MAAMJ,KAAKC,IAAAA,YAAK;QAChB,MAAMJ,SAASC,IAAAA,gBAAS,EAACH;QACzB,MAAMmF,cAAqC,EAAE;QAE7C,MAAMwC,eAAelH,KAAKkH,YAAY;QACtC,IAAIA,gBAAgBtH,GAAGuH,cAAc,CAACD,eAAe;YACnD,KAAK,MAAMtB,KAAKsB,aAAaE,QAAQ,CAAE;gBACrC,IAAI;oBAAC;oBAAoB;iBAAW,CAACnB,QAAQ,CAACL,EAAE/E,IAAI,CAACC,OAAO,KAAK;oBAC/D4D,YAAY2C,IAAI,CAAC;wBACfzF,MAAMnC;wBACNuF,UAAUpF,GAAG8G,kBAAkB,CAACC,KAAK;wBACrC1B,MAAM2B,wBAAc,CAACC,uBAAuB;wBAC5C3B,aAAa,CAAC,aAAa,EAAEU,EAAE/E,IAAI,CAACC,OAAO,GAAG,2CAA2C,CAAC;wBAC1FgE,OAAOc,EAAE/E,IAAI,CAACiG,QAAQ;wBACtB1C,QAAQwB,EAAE/E,IAAI,CAACkG,QAAQ;oBACzB;gBACF;YACF;QACF;QAEA,OAAOrC;IACT;IAEA4C,4CACE/H,QAAgB,EAChBS,IAAgC;QAEhC,MAAMJ,KAAKC,IAAAA,YAAK;QAEhB,MAAMqH,eAAelH,KAAKkH,YAAY;QACtC,IAAIA,gBAAgBtH,GAAGuH,cAAc,CAACD,eAAe;YACnD,KAAK,MAAMtB,KAAKsB,aAAaE,QAAQ,CAAE;gBACrC,IAAIxB,EAAE/E,IAAI,CAACC,OAAO,OAAO,YAAY;oBACnC,+CAA+C;oBAC/C,MAAMyG,cAAcC,IAAAA,qBAAc;oBAClC,IAAID,aAAa;wBACf,MAAME,SAASF,YAAYG,mBAAmB,CAAC9B,EAAE/E,IAAI;wBACrD,IAAI4G,QAAQ;4BACV,MAAME,iBAAiBJ,YAAYK,gBAAgB,CAACH;4BACpD,IAAIE,kBAAkBA,eAAe/G,YAAY,EAAE;gCACjD,MAAMD,cAAcgH,eAAe/G,YAAY,CAAC,EAAE;gCAClD,IAAID,eAAef,GAAGiI,qBAAqB,CAAClH,cAAc;oCACxD,IAAI0D,QAAQ1D,cAAc;oCAE1B,MAAMmH,sBACJnH,YAAYoH,aAAa,GAAGxI,QAAQ;oCACtC,MAAMyI,aAAaF,wBAAwBvI;oCAE3C,0DAA0D;oCAC1D,MAAMiF,MAAMjB,0BACVuE,qBACAnH;oCAEF,IAAI,CAAC6D,KAAK;oCAEV,MAAME,cAAcH,iBAClBuD,qBACAtD,KACA7D;oCAEF,IAAI+D,YAAYN,MAAM,EAAE;wCACtB,IAAI4D,YAAY;4CACd,OAAOtD;wCACT,OAAO;4CACL,OAAO;gDACL;oDACE9C,MAAMlC,IAAAA,gBAAS,EAACH;oDAChByF,UAAUpF,GAAG8G,kBAAkB,CAACC,KAAK;oDACrC1B,MAAM2B,wBAAc,CAACC,uBAAuB;oDAC5C3B,aAAa,CAAC,0LAA0L,CAAC;oDACzMJ,OAAOc,EAAE/E,IAAI,CAACiG,QAAQ;oDACtB1C,QAAQwB,EAAE/E,IAAI,CAACkG,QAAQ;gDACzB;6CACD;wCACH;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,EAAE;IACX;IAEAkB,2BACE1I,QAAgB,EAChBC,QAAgB,EAChB0I,SAAiB,EACjBC,aAAyC,EACzC1I,MAAc,EACd2I,WAAqC,EACrC5B,IAAkC;QAElC,MAAMxG,OAAOV,kBAAkBC,UAAUC;QACzC,IAAI,CAACQ,MAAM;QACX,IAAIqE,QAAQrE,OAAO;QAEnB,0DAA0D;QAC1D,MAAMwE,MAAMjB,0BAA0BhE,UAAUS;QAChD,IAAIwE,QAAQpC,WAAW;QAEvB,MAAM,EAAElB,eAAe,EAAE,GAAGD;QAC5B,MAAMsE,SAAS/F,YAAYgF,GAAG,CAAC,EAAE,GAAGhF,WAAWA,WAAWgF,GAAG,CAAC,EAAE;QAEhE,MAAM6D,UAAUnH,gBAAgB+G,yBAAyB,CACvD1I,UACAgG,QACA2C,WACAC,eACA1I,QACA2I,aACA5B;QAEF,OAAO6B;IACT;IAEAC,wBAAuB/I,QAAgB,EAAEC,QAAgB;QACvD,MAAMQ,OAAOV,kBAAkBC,UAAUC;QACzC,IAAI,CAACQ,MAAM;QACX,IAAIqE,QAAQrE,OAAO;QAEnB,0DAA0D;QAC1D,MAAMwE,MAAMjB,0BAA0BhE,UAAUS;QAChD,IAAIwE,QAAQpC,WAAW;QAEvB,MAAM,EAAElB,eAAe,EAAE,GAAGD;QAC5B,MAAMsE,SAAS/F,YAAYgF,GAAG,CAAC,EAAE,GAAGhF,WAAWA,WAAWgF,GAAG,CAAC,EAAE;QAChE,MAAM+D,UAAUrH,gBAAgBoH,sBAAsB,CAAC/I,UAAUgG;QACjE,OAAOgD;IACT;IAEAC,2BAA0BjJ,QAAgB,EAAEC,QAAgB;QAC1D,MAAMQ,OAAOV,kBAAkBC,UAAUC;QACzC,IAAI,CAACQ,MAAM;QACX,IAAIqE,QAAQrE,OAAO;QACnB,IAAI,CAACC,IAAAA,2BAAoB,EAACT,UAAUQ,OAAO;QAC3C,0DAA0D;QAC1D,MAAMwE,MAAMjB,0BAA0BhE,UAAUS;QAChD,IAAIwE,QAAQpC,WAAW;QACvB,MAAM,EAAElB,eAAe,EAAE,GAAGD;QAC5B,MAAMsE,SAAS/F,YAAYgF,GAAG,CAAC,EAAE,GAAGhF,WAAWA,WAAWgF,GAAG,CAAC,EAAE;QAEhE,MAAMiE,6BACJvH,gBAAgBsH,yBAAyB,CAACjJ,UAAUgG;QAEtD,IAAIkD,4BAA4B;YAC9B,6CAA6C;YAC7C,IAAIA,2BAA2BC,QAAQ,CAAC5D,KAAK,GAAGN,GAAG,CAAC,EAAE,EAAE;gBACtDiE,2BAA2BC,QAAQ,CAAC5D,KAAK,IAAIN,GAAG,CAAC,EAAE;YACrD;QACF;QACA,OAAOiE;IACT;AACF;MAEA,WAAetD"}