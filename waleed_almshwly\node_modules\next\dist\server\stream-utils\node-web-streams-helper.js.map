{"version": 3, "sources": ["../../../src/server/stream-utils/node-web-streams-helper.ts"], "names": ["chainStreams", "continueDynamicDataResume", "continueDynamicHTMLResume", "continueDynamicPrerender", "continueFizzStream", "continueStaticP<PERSON><PERSON>", "createBufferedTransformStream", "createRootLayoutValidatorStream", "renderToInitialFizzStream", "streamFromString", "streamToString", "voidCatch", "encoder", "TextEncoder", "streams", "length", "Error", "readable", "writable", "TransformStream", "promise", "pipeTo", "preventClose", "i", "nextStream", "then", "lastStream", "catch", "str", "ReadableStream", "start", "controller", "enqueue", "encode", "close", "stream", "decoder", "TextDecoder", "fatal", "string", "chunk", "decode", "bufferedChunks", "bufferByteLength", "pending", "flush", "detached", "Detached<PERSON>romise", "scheduleImmediate", "Uint8Array", "copiedBytes", "bufferedChunk", "set", "byteLength", "undefined", "resolve", "transform", "push", "createInsertedHTMLStream", "getServerInsertedHTML", "html", "ReactDOMServer", "element", "streamOptions", "getTracer", "trace", "AppRenderSpan", "renderToReadableStream", "createHeadInsertionTransformStream", "insert", "inserted", "freezing", "hasBytes", "insertion", "encodedInsertion", "index", "indexOfUint8Array", "ENCODED_TAGS", "CLOSED", "HEAD", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "flushed", "createMergedTransformStream", "pull", "donePulling", "startPulling", "reader", "<PERSON><PERSON><PERSON><PERSON>", "atLeastOneTask", "done", "value", "read", "err", "error", "createMoveSuffixStream", "foundSuffix", "encodedSuffix", "before", "after", "createStripDocumentClosingTagsTransform", "isEquivalentUint8Arrays", "BODY_AND_HTML", "BODY", "HTML", "removeFromUint8Array", "foundHtml", "foundBody", "OPENING", "missingTags", "JSON", "stringify", "chainTransformers", "transformers", "transformer", "pipeThrough", "renderStream", "inlinedDataStream", "isStaticGeneration", "serverInsertedHTMLToHead", "validateRootLayout", "closeTag", "suffixUnclosed", "split", "allReady", "prerenderStream"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IA0BgBA,YAAY;eAAZA;;IAimBMC,yBAAyB;eAAzBA;;IAvBAC,yBAAyB;eAAzBA;;IA3CAC,wBAAwB;eAAxBA;;IA1DAC,kBAAkB;eAAlBA;;IA6EAC,uBAAuB;eAAvBA;;IAjfNC,6BAA6B;eAA7BA;;IAgWAC,+BAA+B;eAA/BA;;IAxRAC,yBAAyB;eAAzBA;;IAjGAC,gBAAgB;eAAhBA;;IASMC,cAAc;eAAdA;;;wBA3EI;2BACI;iCACE;2BACkB;6BACrB;mCAKtB;AAEP,SAASC;AACP,iFAAiF;AACjF,uFAAuF;AACvF,mBAAmB;AACrB;AAMA,oDAAoD;AACpD,uEAAuE;AACvE,+BAA+B;AAC/B,MAAMC,UAAU,IAAIC;AAEb,SAASb,aACd,GAAGc,OAA4B;IAE/B,yFAAyF;IACzF,sCAAsC;IACtC,IAAIA,QAAQC,MAAM,KAAK,GAAG;QACxB,MAAM,IAAIC,MAAM;IAClB;IAEA,yEAAyE;IACzE,IAAIF,QAAQC,MAAM,KAAK,GAAG;QACxB,OAAOD,OAAO,CAAC,EAAE;IACnB;IAEA,MAAM,EAAEG,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;IAEnC,4EAA4E;IAC5E,mEAAmE;IACnE,IAAIC,UAAUN,OAAO,CAAC,EAAE,CAACO,MAAM,CAACH,UAAU;QAAEI,cAAc;IAAK;IAE/D,IAAIC,IAAI;IACR,MAAOA,IAAIT,QAAQC,MAAM,GAAG,GAAGQ,IAAK;QAClC,MAAMC,aAAaV,OAAO,CAACS,EAAE;QAC7BH,UAAUA,QAAQK,IAAI,CAAC,IACrBD,WAAWH,MAAM,CAACH,UAAU;gBAAEI,cAAc;YAAK;IAErD;IAEA,kFAAkF;IAClF,wEAAwE;IACxE,MAAMI,aAAaZ,OAAO,CAACS,EAAE;IAC7BH,UAAUA,QAAQK,IAAI,CAAC,IAAMC,WAAWL,MAAM,CAACH;IAE/C,0EAA0E;IAC1E,gDAAgD;IAChDE,QAAQO,KAAK,CAAChB;IAEd,OAAOM;AACT;AAEO,SAASR,iBAAiBmB,GAAW;IAC1C,OAAO,IAAIC,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAACL;YAClCG,WAAWG,KAAK;QAClB;IACF;AACF;AAEO,eAAexB,eACpByB,MAAkC;IAElC,MAAMC,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IACvD,IAAIC,SAAS;IAEb,uGAAuG;IACvG,WAAW,MAAMC,SAASL,OAAQ;QAChCI,UAAUH,QAAQK,MAAM,CAACD,OAAO;YAAEL,QAAQ;QAAK;IACjD;IAEAI,UAAUH,QAAQK,MAAM;IAExB,OAAOF;AACT;AAEO,SAASjC;IAId,IAAIoC,iBAAoC,EAAE;IAC1C,IAAIC,mBAA2B;IAC/B,IAAIC;IAEJ,MAAMC,QAAQ,CAACd;QACb,yDAAyD;QACzD,IAAIa,SAAS;QAEb,MAAME,WAAW,IAAIC,gCAAe;QACpCH,UAAUE;QAEVE,IAAAA,4BAAiB,EAAC;YAChB,IAAI;gBACF,MAAMR,QAAQ,IAAIS,WAAWN;gBAC7B,IAAIO,cAAc;gBAClB,IAAK,IAAI3B,IAAI,GAAGA,IAAImB,eAAe3B,MAAM,EAAEQ,IAAK;oBAC9C,MAAM4B,gBAAgBT,cAAc,CAACnB,EAAE;oBACvCiB,MAAMY,GAAG,CAACD,eAAeD;oBACzBA,eAAeC,cAAcE,UAAU;gBACzC;gBACA,qFAAqF;gBACrF,4EAA4E;gBAC5EX,eAAe3B,MAAM,GAAG;gBACxB4B,mBAAmB;gBACnBZ,WAAWC,OAAO,CAACQ;YACrB,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRI,UAAUU;gBACVR,SAASS,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIpC,gBAAgB;QACzBqC,WAAUhB,KAAK,EAAET,UAAU;YACzB,kDAAkD;YAClDW,eAAee,IAAI,CAACjB;YACpBG,oBAAoBH,MAAMa,UAAU;YAEpC,sCAAsC;YACtCR,MAAMd;QACR;QACAc;YACE,IAAI,CAACD,SAAS;YAEd,OAAOA,QAAQxB,OAAO;QACxB;IACF;AACF;AAEA,SAASsC,yBACPC,qBAA4C;IAE5C,OAAO,IAAIxC,gBAAgB;QACzBqC,WAAW,OAAOhB,OAAOT;YACvB,MAAM6B,OAAO,MAAMD;YACnB,IAAIC,MAAM;gBACR7B,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAAC2B;YACpC;YAEA7B,WAAWC,OAAO,CAACQ;QACrB;IACF;AACF;AAEO,SAAShC,0BAA0B,EACxCqD,cAAc,EACdC,OAAO,EACPC,aAAa,EAKd;IACC,OAAOC,IAAAA,iBAAS,IAAGC,KAAK,CAACC,wBAAa,CAACC,sBAAsB,EAAE,UAC7DN,eAAeM,sBAAsB,CAACL,SAASC;AAEnD;AAEA,SAASK,mCACPC,MAA6B;IAE7B,IAAIC,WAAW;IACf,IAAIC,WAAW;IAEf,wEAAwE;IACxE,iDAAiD;IACjD,IAAIC,WAAW;IAEf,OAAO,IAAIrD,gBAAgB;QACzB,MAAMqC,WAAUhB,KAAK,EAAET,UAAU;YAC/ByC,WAAW;YACX,4DAA4D;YAC5D,IAAID,UAAU;gBACZxC,WAAWC,OAAO,CAACQ;gBACnB;YACF;YAEA,MAAMiC,YAAY,MAAMJ;YAExB,IAAIC,UAAU;gBACZ,IAAIG,WAAW;oBACb,MAAMC,mBAAmB9D,QAAQqB,MAAM,CAACwC;oBACxC1C,WAAWC,OAAO,CAAC0C;gBACrB;gBACA3C,WAAWC,OAAO,CAACQ;gBACnB+B,WAAW;YACb,OAAO;gBACL,0JAA0J;gBAC1J,MAAMI,QAAQC,IAAAA,oCAAiB,EAACpC,OAAOqC,yBAAY,CAACC,MAAM,CAACC,IAAI;gBAC/D,IAAIJ,UAAU,CAAC,GAAG;oBAChB,IAAIF,WAAW;wBACb,MAAMC,mBAAmB9D,QAAQqB,MAAM,CAACwC;wBACxC,MAAMO,sBAAsB,IAAI/B,WAC9BT,MAAMzB,MAAM,GAAG2D,iBAAiB3D,MAAM;wBAExCiE,oBAAoB5B,GAAG,CAACZ,MAAMyC,KAAK,CAAC,GAAGN;wBACvCK,oBAAoB5B,GAAG,CAACsB,kBAAkBC;wBAC1CK,oBAAoB5B,GAAG,CACrBZ,MAAMyC,KAAK,CAACN,QACZA,QAAQD,iBAAiB3D,MAAM;wBAEjCgB,WAAWC,OAAO,CAACgD;oBACrB,OAAO;wBACLjD,WAAWC,OAAO,CAACQ;oBACrB;oBACA+B,WAAW;oBACXD,WAAW;gBACb;YACF;YAEA,IAAI,CAACA,UAAU;gBACbvC,WAAWC,OAAO,CAACQ;YACrB,OAAO;gBACLQ,IAAAA,4BAAiB,EAAC;oBAChBuB,WAAW;gBACb;YACF;QACF;QACA,MAAM1B,OAAMd,UAAU;YACpB,gEAAgE;YAChE,IAAIyC,UAAU;gBACZ,MAAMC,YAAY,MAAMJ;gBACxB,IAAII,WAAW;oBACb1C,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAACwC;gBACpC;YACF;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASS,2BACPC,MAAc;IAEd,IAAIC,UAAU;IACd,IAAIxC;IAEJ,MAAMC,QAAQ,CAACd;QACb,MAAMe,WAAW,IAAIC,gCAAe;QACpCH,UAAUE;QAEVE,IAAAA,4BAAiB,EAAC;YAChB,IAAI;gBACFjB,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAACkD;YACpC,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRvC,UAAUU;gBACVR,SAASS,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIpC,gBAAgB;QACzBqC,WAAUhB,KAAK,EAAET,UAAU;YACzBA,WAAWC,OAAO,CAACQ;YAEnB,wCAAwC;YACxC,IAAI4C,SAAS;YAEb,gCAAgC;YAChCA,UAAU;YACVvC,MAAMd;QACR;QACAc,OAAMd,UAAU;YACd,IAAIa,SAAS,OAAOA,QAAQxB,OAAO;YACnC,IAAIgE,SAAS;YAEb,aAAa;YACbrD,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAACkD;QACpC;IACF;AACF;AAEA,0EAA0E;AAC1E,0BAA0B;AAC1B,SAASE,4BACPlD,MAAkC;IAElC,IAAImD,OAA6B;IACjC,IAAIC,cAAc;IAElB,eAAeC,aAAazD,UAA4C;QACtE,IAAIuD,MAAM;YACR;QACF;QAEA,MAAMG,SAAStD,OAAOuD,SAAS;QAE/B,wBAAwB;QACxB,gEAAgE;QAChE,qEAAqE;QACrE,uEAAuE;QACvE,8DAA8D;QAC9D,aAAa;QAEb,qEAAqE;QACrE,6EAA6E;QAC7E,gEAAgE;QAChE,MAAMC,IAAAA,yBAAc;QAEpB,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;gBACzC,IAAIF,MAAM;oBACRL,cAAc;oBACd;gBACF;gBAEAxD,WAAWC,OAAO,CAAC6D;YACrB;QACF,EAAE,OAAOE,KAAK;YACZhE,WAAWiE,KAAK,CAACD;QACnB;IACF;IAEA,OAAO,IAAI5E,gBAAgB;QACzBqC,WAAUhB,KAAK,EAAET,UAAU;YACzBA,WAAWC,OAAO,CAACQ;YAEnB,6DAA6D;YAC7D,IAAI,CAAC8C,MAAM;gBACTA,OAAOE,aAAazD;YACtB;QACF;QACAc,OAAMd,UAAU;YACd,IAAIwD,aAAa;gBACf;YACF;YACA,OAAOD,QAAQE,aAAazD;QAC9B;IACF;AACF;AAEA;;;;CAIC,GACD,SAASkE,uBACPd,MAAc;IAEd,IAAIe,cAAc;IAElB,MAAMC,gBAAgBvF,QAAQqB,MAAM,CAACkD;IAErC,OAAO,IAAIhE,gBAAgB;QACzBqC,WAAUhB,KAAK,EAAET,UAAU;YACzB,IAAImE,aAAa;gBACf,OAAOnE,WAAWC,OAAO,CAACQ;YAC5B;YAEA,MAAMmC,QAAQC,IAAAA,oCAAiB,EAACpC,OAAO2D;YACvC,IAAIxB,QAAQ,CAAC,GAAG;gBACduB,cAAc;gBAEd,uEAAuE;gBACvE,2BAA2B;gBAC3B,IAAI1D,MAAMzB,MAAM,KAAKoE,OAAOpE,MAAM,EAAE;oBAClC;gBACF;gBAEA,wCAAwC;gBACxC,MAAMqF,SAAS5D,MAAMyC,KAAK,CAAC,GAAGN;gBAC9B5C,WAAWC,OAAO,CAACoE;gBAEnB,sEAAsE;gBACtE,qCAAqC;gBACrC,IAAI5D,MAAMzB,MAAM,GAAGoE,OAAOpE,MAAM,GAAG4D,OAAO;oBACxC,uCAAuC;oBACvC,MAAM0B,QAAQ7D,MAAMyC,KAAK,CAACN,QAAQQ,OAAOpE,MAAM;oBAC/CgB,WAAWC,OAAO,CAACqE;gBACrB;YACF,OAAO;gBACLtE,WAAWC,OAAO,CAACQ;YACrB;QACF;QACAK,OAAMd,UAAU;YACd,uEAAuE;YACvE,mCAAmC;YACnCA,WAAWC,OAAO,CAACmE;QACrB;IACF;AACF;AAEA,SAASG;IAIP,OAAO,IAAInF,gBAAgB;QACzBqC,WAAUhB,KAAK,EAAET,UAAU;YACzB,6EAA6E;YAC7E,qFAAqF;YACrF,wFAAwF;YACxF,2FAA2F;YAC3F,sCAAsC;YACtC,IACEwE,IAAAA,0CAAuB,EAAC/D,OAAOqC,yBAAY,CAACC,MAAM,CAAC0B,aAAa,KAChED,IAAAA,0CAAuB,EAAC/D,OAAOqC,yBAAY,CAACC,MAAM,CAAC2B,IAAI,KACvDF,IAAAA,0CAAuB,EAAC/D,OAAOqC,yBAAY,CAACC,MAAM,CAAC4B,IAAI,GACvD;gBACA,4EAA4E;gBAC5E;YACF;YAEA,+EAA+E;YAC/E,wFAAwF;YACxF,sFAAsF;YACtFlE,QAAQmE,IAAAA,uCAAoB,EAACnE,OAAOqC,yBAAY,CAACC,MAAM,CAAC2B,IAAI;YAC5DjE,QAAQmE,IAAAA,uCAAoB,EAACnE,OAAOqC,yBAAY,CAACC,MAAM,CAAC4B,IAAI;YAE5D3E,WAAWC,OAAO,CAACQ;QACrB;IACF;AACF;AAOO,SAASjC;IAId,IAAIqG,YAAY;IAChB,IAAIC,YAAY;IAChB,OAAO,IAAI1F,gBAAgB;QACzB,MAAMqC,WAAUhB,KAAK,EAAET,UAAU;YAC/B,+DAA+D;YAC/D,IACE,CAAC6E,aACDhC,IAAAA,oCAAiB,EAACpC,OAAOqC,yBAAY,CAACiC,OAAO,CAACJ,IAAI,IAAI,CAAC,GACvD;gBACAE,YAAY;YACd;YAEA,IACE,CAACC,aACDjC,IAAAA,oCAAiB,EAACpC,OAAOqC,yBAAY,CAACiC,OAAO,CAACL,IAAI,IAAI,CAAC,GACvD;gBACAI,YAAY;YACd;YAEA9E,WAAWC,OAAO,CAACQ;QACrB;QACAK,OAAMd,UAAU;YACd,MAAMgF,cAA6D,EAAE;YACrE,IAAI,CAACH,WAAWG,YAAYtD,IAAI,CAAC;YACjC,IAAI,CAACoD,WAAWE,YAAYtD,IAAI,CAAC;YAEjC,IAAI,CAACsD,YAAYhG,MAAM,EAAE;YAEzBgB,WAAWC,OAAO,CAChBpB,QAAQqB,MAAM,CACZ,CAAC,6CAA6C,EAAE+E,KAAKC,SAAS,CAC5DF,aACA,SAAS,CAAC;QAGlB;IACF;AACF;AAEA,SAASG,kBACPjG,QAA2B,EAC3BkG,YAAyD;IAEzD,IAAIhF,SAASlB;IACb,KAAK,MAAMmG,eAAeD,aAAc;QACtC,IAAI,CAACC,aAAa;QAElBjF,SAASA,OAAOkF,WAAW,CAACD;IAC9B;IACA,OAAOjF;AACT;AAcO,eAAe/B,mBACpBkH,YAAiC,EACjC,EACEnC,MAAM,EACNoC,iBAAiB,EACjBC,kBAAkB,EAClB7D,qBAAqB,EACrB8D,wBAAwB,EACxBC,kBAAkB,EACI;IAExB,MAAMC,WAAW;IAEjB,6EAA6E;IAC7E,MAAMC,iBAAiBzC,SAASA,OAAO0C,KAAK,CAACF,UAAU,EAAE,CAAC,EAAE,GAAG;IAE/D,2EAA2E;IAC3E,+DAA+D;IAC/D,IAAIH,sBAAsB,cAAcF,cAAc;QACpD,MAAMA,aAAaQ,QAAQ;IAC7B;IAEA,OAAOZ,kBAAkBI,cAAc;QACrC,qDAAqD;QACrDhH;QAEA,gCAAgC;QAChCqD,yBAAyB,CAAC8D,2BACtB/D,yBAAyBC,yBACzB;QAEJ,wBAAwB;QACxBiE,kBAAkB,QAAQA,eAAe7G,MAAM,GAAG,IAC9CmE,2BAA2B0C,kBAC3B;QAEJ,+EAA+E;QAC/EL,oBAAoBlC,4BAA4BkC,qBAAqB;QAErE,yDAAyD;QACzDG,qBAAqBnH,oCAAoC;QAEzD,kDAAkD;QAClD0F,uBAAuB0B;QAEvB,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/EhE,yBAAyB8D,2BACrBrD,mCAAmCT,yBACnC;KACL;AACH;AAMO,eAAexD,yBACpB4H,eAA2C,EAC3C,EAAEpE,qBAAqB,EAAmC;IAE1D,OACEoE,eACE,qDAAqD;KACpDV,WAAW,CAAC/G,iCACZ+G,WAAW,CAACf,0CACb,gCAAgC;KAC/Be,WAAW,CAACjD,mCAAmCT;AAEtD;AAOO,eAAetD,wBACpB0H,eAA2C,EAC3C,EAAER,iBAAiB,EAAE5D,qBAAqB,EAAkC;IAE5E,MAAMgE,WAAW;IAEjB,OACEI,eACE,qDAAqD;KACpDV,WAAW,CAAC/G,gCACb,gCAAgC;KAC/B+G,WAAW,CAACjD,mCAAmCT,uBAChD,+EAA+E;KAC9E0D,WAAW,CAAChC,4BAA4BkC,mBACzC,kDAAkD;KACjDF,WAAW,CAACpB,uBAAuB0B;AAE1C;AAOO,eAAezH,0BACpBoH,YAAwC,EACxC,EAAEC,iBAAiB,EAAE5D,qBAAqB,EAAyB;IAEnE,MAAMgE,WAAW;IAEjB,OACEL,YACE,qDAAqD;KACpDD,WAAW,CAAC/G,gCACb,gCAAgC;KAC/B+G,WAAW,CAACjD,mCAAmCT,uBAChD,+EAA+E;KAC9E0D,WAAW,CAAChC,4BAA4BkC,mBACzC,kDAAkD;KACjDF,WAAW,CAACpB,uBAAuB0B;AAE1C;AAMO,eAAe1H,0BACpBqH,YAAwC,EACxC,EAAEC,iBAAiB,EAAoC;IAEvD,MAAMI,WAAW;IAEjB,OACEL,YACE,+EAA+E;KAC9ED,WAAW,CAAChC,4BAA4BkC,mBACzC,kDAAkD;KACjDF,WAAW,CAACpB,uBAAuB0B;AAE1C"}