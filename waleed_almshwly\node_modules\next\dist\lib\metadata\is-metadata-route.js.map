{"version": 3, "sources": ["../../../src/lib/metadata/is-metadata-route.ts"], "names": ["STATIC_METADATA_IMAGES", "isMetadataRoute", "isMetadataRouteFile", "isStaticMetadataRoute", "isStaticMetadataRouteFile", "icon", "filename", "extensions", "apple", "favicon", "openGraph", "twitter", "defaultExtensions", "getExtensionRegexString", "join", "appDirRelativePath", "pageExtensions", "withExtension", "metadataRouteFilesRegex", "RegExp", "concat", "normalizedAppDirRelativePath", "normalizePathSep", "some", "r", "test", "page", "route", "replace", "endsWith"], "mappings": ";;;;;;;;;;;;;;;;;;IAGaA,sBAAsB;eAAtBA;;IAiIGC,eAAe;eAAfA;;IA7FAC,mBAAmB;eAAnBA;;IA8EAC,qBAAqB;eAArBA;;IAJAC,yBAAyB;eAAzBA;;;kCAhHiB;AAE1B,MAAMJ,yBAAyB;IACpCK,MAAM;QACJC,UAAU;QACVC,YAAY;YAAC;YAAO;YAAO;YAAQ;YAAO;SAAM;IAClD;IACAC,OAAO;QACLF,UAAU;QACVC,YAAY;YAAC;YAAO;YAAQ;SAAM;IACpC;IACAE,SAAS;QACPH,UAAU;QACVC,YAAY;YAAC;SAAM;IACrB;IACAG,WAAW;QACTJ,UAAU;QACVC,YAAY;YAAC;YAAO;YAAQ;YAAO;SAAM;IAC3C;IACAI,SAAS;QACPL,UAAU;QACVC,YAAY;YAAC;YAAO;YAAQ;YAAO;SAAM;IAC3C;AACF;AAEA,gGAAgG;AAChG,mEAAmE;AACnE,MAAMK,oBAAoB;IAAC;IAAM;IAAO;IAAM;CAAM;AAEpD,MAAMC,0BAA0B,CAACN,aAC/B,CAAC,GAAG,EAAEA,WAAWO,IAAI,CAAC,KAAK,CAAC,CAAC;AAQxB,SAASZ,oBACda,kBAA0B,EAC1BC,cAA8B,EAC9BC,aAAsB;IAEtB,MAAMC,0BAA0B;QAC9B,IAAIC,OACF,CAAC,cAAc,EACbF,gBACI,CAAC,GAAG,EAAEJ,wBAAwBG,eAAeI,MAAM,CAAC,QAAQ,CAAC,CAAC,GAC9D,GACL,CAAC;QAEJ,IAAID,OACF,CAAC,gBAAgB,EACfF,gBACI,CAAC,GAAG,EAAEJ,wBACJG,eAAeI,MAAM,CAAC,eAAe,SACrC,CAAC,CAAC,GACJ,GACL,CAAC;QAEJ,IAAID,OAAO,CAAC,sBAAsB,CAAC;QACnC,IAAIA,OACF,CAAC,cAAc,EACbF,gBACI,CAAC,GAAG,EAAEJ,wBAAwBG,eAAeI,MAAM,CAAC,QAAQ,CAAC,CAAC,GAC9D,GACL,CAAC;QAEJ,IAAID,OACF,CAAC,OAAO,EAAEnB,uBAAuBK,IAAI,CAACC,QAAQ,CAAC,IAAI,EACjDW,gBACI,CAAC,GAAG,EAAEJ,wBACJG,eAAeI,MAAM,CAACpB,uBAAuBK,IAAI,CAACE,UAAU,GAC5D,CAAC,CAAC,GACJ,GACL,CAAC;QAEJ,IAAIY,OACF,CAAC,OAAO,EAAEnB,uBAAuBQ,KAAK,CAACF,QAAQ,CAAC,IAAI,EAClDW,gBACI,CAAC,GAAG,EAAEJ,wBACJG,eAAeI,MAAM,CAACpB,uBAAuBQ,KAAK,CAACD,UAAU,GAC7D,CAAC,CAAC,GACJ,GACL,CAAC;QAEJ,IAAIY,OACF,CAAC,OAAO,EAAEnB,uBAAuBU,SAAS,CAACJ,QAAQ,CAAC,IAAI,EACtDW,gBACI,CAAC,GAAG,EAAEJ,wBACJG,eAAeI,MAAM,CAACpB,uBAAuBU,SAAS,CAACH,UAAU,GACjE,CAAC,CAAC,GACJ,GACL,CAAC;QAEJ,IAAIY,OACF,CAAC,OAAO,EAAEnB,uBAAuBW,OAAO,CAACL,QAAQ,CAAC,IAAI,EACpDW,gBACI,CAAC,GAAG,EAAEJ,wBACJG,eAAeI,MAAM,CAACpB,uBAAuBW,OAAO,CAACJ,UAAU,GAC/D,CAAC,CAAC,GACJ,GACL,CAAC;KAEL;IAED,MAAMc,+BAA+BC,IAAAA,kCAAgB,EAACP;IACtD,OAAOG,wBAAwBK,IAAI,CAAC,CAACC,IACnCA,EAAEC,IAAI,CAACJ;AAEX;AAEO,SAASjB,0BAA0BW,kBAA0B;IAClE,OAAOb,oBAAoBa,oBAAoB,EAAE,EAAE;AACrD;AAEO,SAASZ,sBAAsBuB,IAAY;IAChD,OACEA,SAAS,aACTA,SAAS,eACTtB,0BAA0BsB;AAE9B;AASO,SAASzB,gBAAgB0B,KAAa;IAC3C,IAAID,OAAOC,MAAMC,OAAO,CAAC,aAAa,IAAIA,OAAO,CAAC,YAAY;IAC9D,IAAIF,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAElC,OACE,CAACA,KAAKG,QAAQ,CAAC,YACf3B,oBAAoBwB,MAAMd,mBAAmB;AAEjD"}