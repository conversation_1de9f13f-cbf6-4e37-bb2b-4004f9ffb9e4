{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/resolve-url-loader/index.ts"], "names": ["resolveUrlLoader", "content", "sourceMap", "options", "Object", "assign", "silent", "absolute", "<PERSON><PERSON><PERSON><PERSON>", "root", "debug", "join", "defaultJoin", "getOptions", "sourceMapConsumer", "SourceMapConsumer", "callback", "async", "postcss", "require", "process", "resourcePath", "outputSourceMap", "Boolean", "transformDeclaration", "valueProcessor", "inputSourceMap", "catch", "onFailure", "then", "onSuccess", "error", "encodeError", "reworked", "map", "label", "exception", "Error", "concat", "message", "stack", "split", "trim", "filter"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;+BAMA;;;CAGC,GACD;;;eAA8BA;;;2BARI;uEACP;8BACC;gEACR;;;;;;AAKL,eAAeA,iBAE5B,gBAAgB,GAChBC,OAAe,EACf,mBAAmB,GACnBC,SAAc;IAEd,MAAMC,UAAUC,OAAOC,MAAM,CAC3B;QACEH,WAAW,IAAI,CAACA,SAAS;QACzBI,QAAQ;QACRC,UAAU;QACVC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,MAAMC,yBAAW;IACnB,GACA,IAAI,CAACC,UAAU;IAGjB,IAAIC;IACJ,IAAIZ,WAAW;QACbY,oBAAoB,IAAIC,4BAAiB,CAACb;IAC5C;IAEA,MAAMc,WAAW,IAAI,CAACC,KAAK;IAC3B,MAAM,EAAEC,OAAO,EAAE,GAAGf,QAAQe,OAAO,GAC/B,MAAMf,QAAQe,OAAO,KACrB;QAAEA,SAASC,QAAQ;IAAW;IAClCC,IAAAA,gBAAO,EAACF,SAAS,IAAI,CAACG,YAAY,EAAEpB,SAAS;QAC3CqB,iBAAiBC,QAAQpB,QAAQD,SAAS;QAC1CsB,sBAAsBC,IAAAA,uBAAc,EAAC,IAAI,CAACJ,YAAY,EAAElB;QACxDuB,gBAAgBxB;QAChBY,mBAAmBA;IACrB,EACE,mEAAmE;KAClEa,KAAK,CAACC,UACP,mEAAmE;KAClEC,IAAI,CAACC;IAER,SAASF,UAAUG,KAAY;QAC7B,mEAAmE;QACnEf,SAASgB,YAAY,aAAaD;IACpC;IAEA,SAASD,UAAUG,QAAa;QAC9B,IAAIA,UAAU;YACZ,2BAA2B;YAC3B,+DAA+D;YAC/D,IAAI9B,QAAQD,SAAS,EAAE;gBACrBc,SAAS,MAAMiB,SAAShC,OAAO,EAAEgC,SAASC,GAAG;YAC/C,OAEK;gBACHlB,SAAS,MAAMiB,SAAShC,OAAO;YACjC;QACF;IACF;IAEA,SAAS+B,YAAYG,KAAU,EAAEC,SAAc;QAC7C,OAAO,IAAIC,MACT;YACE;YACA;YACA;gBAACF;aAAM,CACJG,MAAM,CACL,AAAC,OAAOF,cAAc,YAAYA,aAC/BA,qBAAqBC,SAAS;gBAC7BD,UAAUG,OAAO;gBAChBH,UAAkBI,KAAK,CAACC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAACC,IAAI;aAChD,IACD,EAAE,EAELC,MAAM,CAACpB,SACPZ,IAAI,CAAC;SACT,CAACA,IAAI,CAAC;IAEX;AACF"}