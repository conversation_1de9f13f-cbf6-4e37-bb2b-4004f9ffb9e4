{"version": 3, "sources": ["../../../src/lib/helpers/get-pkg-manager.ts"], "names": ["fs", "path", "execSync", "getPkgManager", "baseDir", "lockFile", "packageManager", "existsSync", "join", "userAgent", "process", "env", "npm_config_user_agent", "startsWith", "stdio"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,SAASC,QAAQ,QAAQ,gBAAe;AAIxC,OAAO,SAASC,cAAcC,OAAe;IAC3C,IAAI;QACF,KAAK,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAE,IAAI;YACzC;gBAAED,UAAU;gBAAaC,gBAAgB;YAAO;YAChD;gBAAED,UAAU;gBAAkBC,gBAAgB;YAAO;YACrD;gBAAED,UAAU;gBAAqBC,gBAAgB;YAAM;SACxD,CAAE;YACD,IAAIN,GAAGO,UAAU,CAACN,KAAKO,IAAI,CAACJ,SAASC,YAAY;gBAC/C,OAAOC;YACT;QACF;QACA,MAAMG,YAAYC,QAAQC,GAAG,CAACC,qBAAqB;QACnD,IAAIH,WAAW;YACb,IAAIA,UAAUI,UAAU,CAAC,SAAS;gBAChC,OAAO;YACT,OAAO,IAAIJ,UAAUI,UAAU,CAAC,SAAS;gBACvC,OAAO;YACT;QACF;QACA,IAAI;YACFX,SAAS,kBAAkB;gBAAEY,OAAO;YAAS;YAC7C,OAAO;QACT,EAAE,OAAM;YACNZ,SAAS,kBAAkB;gBAAEY,OAAO;YAAS;YAC7C,OAAO;QACT;IACF,EAAE,OAAM;QACN,OAAO;IACT;AACF"}