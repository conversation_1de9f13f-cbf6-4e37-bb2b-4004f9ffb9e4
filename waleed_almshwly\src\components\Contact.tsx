
import { Mail, Phone, MapPin, Clock } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ContactForm } from "@/components/ContactForm";
import { useTranslation } from "@/hooks/useTranslation";

export const Contact = () => {
  const { t } = useTranslation();

  const contactInfo = [
    {
      icon: Mail,
      title: t("email"),
      content: "<EMAIL>",
      href: "mailto:<EMAIL>"
    },
    {
      icon: Phone,
      title: t("phone"),
      content: "+966 XXX XXX XXX",
      href: "tel:+966XXXXXXXXX"
    },
    {
      icon: MapPin,
      title: t("location"),
      content: t("saudiArabia"),
      href: "#"
    },
    {
      icon: Clock,
      title: t("availability"),
      content: t("availableForWork"),
      href: "#"
    }
  ];

  return (
    <section id="contact" className="py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl translate-x-1/2 -translate-y-1/2"></div>
      <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl -translate-x-1/2 translate-y-1/2"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-16">
          <div className="inline-block">
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-4 animate-fade-in">
              {t("contact")}
            </h2>
            <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transform scale-x-0 animate-[scale-x-100_1s_ease-out_0.5s_forwards]"></div>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mt-6 animate-fade-in">
            {t("contactDescription")}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Contact Info */}
          <div className="space-y-8">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {contactInfo.map((info, index) => (
                <Card 
                  key={index}
                  className="group bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 border-0 hover:scale-105 animate-fade-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <CardHeader className="text-center pb-3">
                    <div className="w-12 h-12 mx-auto bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
                      <info.icon className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-lg font-semibold text-gray-800">
                      {info.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    {info.href !== "#" ? (
                      <a 
                        href={info.href}
                        className="text-gray-600 hover:text-blue-600 transition-colors duration-200 break-all"
                      >
                        {info.content}
                      </a>
                    ) : (
                      <span className="text-gray-600">{info.content}</span>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Contact Form */}
          <div className="animate-fade-in" style={{ animationDelay: '0.4s' }}>
            <ContactForm />
          </div>
        </div>
      </div>
    </section>
  );
};
