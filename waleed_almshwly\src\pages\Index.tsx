
import { Hero } from "@/components/Hero";
import { About } from "@/components/About";
import { Skills } from "@/components/Skills";
import { Projects } from "@/components/Projects";
import { Experience } from "@/components/Experience";
import { Achievements } from "@/components/Achievements";
import { Contact } from "@/components/Contact";
import { Navigation } from "@/components/Navigation";
import { ScrollProgress } from "@/components/ScrollProgress";
import { BackToTop } from "@/components/BackToTop";
import { TranslationProvider } from "@/hooks/useTranslation";

const Index = () => {
  return (
    <TranslationProvider>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <ScrollProgress />
        <Navigation />
        <Hero />
        <About />
        <Skills />
        <Experience />
        <Achievements />
        <Projects />
        <Contact />
        <BackToTop />
      </div>
    </TranslationProvider>
  );
};

export default Index;
